#!/usr/bin/env python3
"""
添加简单的测试数据
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

def add_test_data():
    """添加测试数据"""
    try:
        from app import create_app, db
        from app.models import Order
        
        print("=== 添加测试数据 ===")
        
        # 创建应用上下文
        app = create_app()
        with app.app_context():
            
            existing_count = Order.query.count()
            print(f"现有订单数: {existing_count}")
            
            # 创建5个已完成支付订单
            completed_orders = [
                {
                    'edaijia_order_id': 'EDJ20250608001',
                    'store_name': '龙华店',
                    'customer_name': '张先生',
                    'customer_phone': '13900000001',
                    'car_model': '宝马X3',
                    'plate_number': '粤B12345',
                    'manager_name': '周鑫芸',
                    'manager_phone': '13800138001',
                    'status': 'completed',
                    'pickup_address': '深圳市龙华区民治街道',
                    'destination_address': '深圳市福田区中心区',
                    'price': 80.0,
                    'driver_name': '张师傅',
                    'driver_phone': '13700000001',
                    'payment_status': 'paid',
                    'pay_amount': 8000,
                    'payment_time': datetime.now() - timedelta(days=2),
                    'created_at': datetime.now() - timedelta(days=2, hours=1)
                },
                {
                    'edaijia_order_id': 'EDJ20250608002',
                    'store_name': '福田店',
                    'customer_name': '李女士',
                    'customer_phone': '13900000002',
                    'car_model': '奥迪A4',
                    'plate_number': '粤B23456',
                    'manager_name': '李经理',
                    'manager_phone': '13800138002',
                    'status': 'completed',
                    'pickup_address': '深圳市福田区中心区',
                    'destination_address': '深圳市南山区科技园',
                    'price': 95.0,
                    'driver_name': '李师傅',
                    'driver_phone': '13700000002',
                    'payment_status': 'paid',
                    'pay_amount': 9500,
                    'payment_time': datetime.now() - timedelta(days=1),
                    'created_at': datetime.now() - timedelta(days=1, hours=2)
                },
                {
                    'edaijia_order_id': 'EDJ20250608003',
                    'store_name': '南山店',
                    'customer_name': '王总',
                    'customer_phone': '13900000003',
                    'car_model': '特斯拉Model3',
                    'plate_number': '粤B34567',
                    'manager_name': '王店长',
                    'manager_phone': '13800138003',
                    'status': 'completed',
                    'pickup_address': '深圳市南山区科技园',
                    'destination_address': '深圳宝安国际机场',
                    'price': 120.0,
                    'driver_name': '王师傅',
                    'driver_phone': '13700000003',
                    'payment_status': 'paid',
                    'pay_amount': 12000,
                    'payment_time': datetime.now() - timedelta(hours=5),
                    'created_at': datetime.now() - timedelta(hours=6)
                }
            ]
            
            # 创建3个活跃订单
            active_orders = [
                {
                    'edaijia_order_id': 'EDJ20250608004',
                    'store_name': '龙华店',
                    'customer_name': '陈经理',
                    'customer_phone': '13900000004',
                    'car_model': '本田CRV',
                    'plate_number': '粤B45678',
                    'manager_name': '周鑫芸',
                    'manager_phone': '13800138001',
                    'status': 'pending',
                    'pickup_address': '深圳市龙华区民治街道',
                    'destination_address': '深圳市罗湖区东门',
                    'price': 75.0,
                    'payment_status': 'unpaid',
                    'created_at': datetime.now() - timedelta(hours=1)
                },
                {
                    'edaijia_order_id': 'EDJ20250608005',
                    'store_name': '福田店',
                    'customer_name': '刘先生',
                    'customer_phone': '13900000005',
                    'car_model': '丰田凯美瑞',
                    'plate_number': '粤B56789',
                    'manager_name': '李经理',
                    'manager_phone': '13800138002',
                    'status': 'in_progress',
                    'pickup_address': '深圳市福田区中心区',
                    'destination_address': '深圳北站',
                    'price': 85.0,
                    'driver_name': '赵师傅',
                    'driver_phone': '13700000004',
                    'payment_status': 'paid',
                    'pay_amount': 8500,
                    'payment_time': datetime.now() - timedelta(minutes=30),
                    'created_at': datetime.now() - timedelta(minutes=45)
                }
            ]
            
            # 添加已完成订单
            print("添加已完成订单...")
            for order_data in completed_orders:
                # 检查是否已存在
                existing = Order.query.filter_by(edaijia_order_id=order_data['edaijia_order_id']).first()
                if not existing:
                    order = Order(**order_data)
                    db.session.add(order)
                    print(f"  {order_data['edaijia_order_id']} | {order_data['customer_name']} | {order_data['store_name']} | ¥{order_data['price']}")
                else:
                    print(f"  跳过已存在订单: {order_data['edaijia_order_id']}")
            
            # 添加活跃订单
            print("添加活跃订单...")
            for order_data in active_orders:
                # 检查是否已存在
                existing = Order.query.filter_by(edaijia_order_id=order_data['edaijia_order_id']).first()
                if not existing:
                    order = Order(**order_data)
                    db.session.add(order)
                    print(f"  {order_data['edaijia_order_id']} | {order_data['customer_name']} | {order_data['store_name']} | {order_data['status']}")
                else:
                    print(f"  跳过已存在订单: {order_data['edaijia_order_id']}")
            
            # 提交数据
            db.session.commit()
            
            # 验证结果
            final_count = Order.query.count()
            completed_count = Order.query.filter(Order.status == 'completed').count()
            pending_count = Order.query.filter(Order.status == 'pending').count()
            in_progress_count = Order.query.filter(Order.status == 'in_progress').count()
            paid_count = Order.query.filter(Order.payment_status == 'paid').count()
            
            print(f"\n✅ 数据添加完成!")
            print(f"总订单数: {final_count}")
            print(f"  已完成: {completed_count}")
            print(f"  待处理: {pending_count}")
            print(f"  进行中: {in_progress_count}")
            print(f"  已支付: {paid_count}")
            
            return True
            
    except Exception as e:
        print(f"❌ 添加数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = add_test_data()
    if success:
        print("\n🎉 测试数据添加成功！")
    else:
        print("\n❌ 测试数据添加失败")
