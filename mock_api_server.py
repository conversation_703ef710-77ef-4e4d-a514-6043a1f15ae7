from flask import Flask, request, jsonify
import time
import hashlib
import random
import requests
import threading
from datetime import datetime, timedelta

app = Flask(__name__)

# 全局订单存储 - 模拟数据库（生产环境：所有数据通过前端API创建）
MOCK_ORDERS = {}

MOCK_APP_KEY = "mock_key"
MOCK_APP_SECRET = "mock_secret"

# ===== e代驾 mock API =====
EDJ_APPKEY = "61000158"
EDJ_SECRET = "0031186e-5cc6-45a6-a090-3e88ec220452"
EDJ_FROM = "01012345"
EDJ_VER = "3.4.3"

# ===== 测试数据存储 =====
# 模拟订单数据库（生产环境：空字典，所有数据通过前端API创建）
MOCK_ORDERS = {}
MOCK_CUSTOMERS = {}
MOCK_POLLING_SESSIONS = {}

def init_test_orders():
    """初始化一些测试订单数据，用于测试取消订单功能"""
    test_orders = [
        {
            'edj_order_id': 'EDJ20250608001',
            'edj_booking_id': 'booking_001',
            'status_code': '102',  # 系统派单中
            'status_desc': '系统派单中',
            'customer_phone': '13800138000',
            'start_address': '龙华店',
            'end_address': '深圳市南山区深圳湾',
            'created_time': time.time() - 3600,  # 1小时前创建
            'estimated_price': 85.0
        },
        {
            'edj_order_id': 'EDJ20250608002',
            'edj_booking_id': 'booking_002',
            'status_code': '180',  # 司机接单
            'status_desc': '司机接单',
            'customer_phone': '13800138001',
            'start_address': '龙华店',
            'end_address': '深圳市福田区华强北',
            'created_time': time.time() - 1800,  # 30分钟前创建
            'estimated_price': 65.0
        },
        {
            'edj_order_id': 'EDJ20250608003',
            'edj_booking_id': 'booking_003',
            'status_code': '301',  # 司机就位
            'status_desc': '司机就位',
            'customer_phone': '13800138002',
            'start_address': '龙华店',
            'end_address': '深圳市宝安区机场',
            'created_time': time.time() - 900,  # 15分钟前创建
            'estimated_price': 120.0
        },
        {
            'edj_order_id': 'EDJ20250608004',
            'edj_booking_id': 'booking_004',
            'status_code': '102',  # 系统派单中
            'status_desc': '系统派单中',
            'customer_phone': '13800138003',
            'start_address': '龙华店',
            'end_address': '深圳市罗湖区东门',
            'created_time': time.time() - 600,  # 10分钟前创建
            'estimated_price': 75.0
        }
    ]

    for order in test_orders:
        MOCK_ORDERS[order['edj_order_id']] = order

    print(f"📊 初始化完成：0个客户，{len(test_orders)}个测试订单")

# 初始化测试订单数据
init_test_orders()

def calc_signature(app_key, app_secret, body):
    sign_str = f"{app_key}{body}{app_secret}"
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest()

def edj_calc_sig(params, secret):
    """
    e代驾签名算法：
    1. 对所有参数（除sig外）按key排序
    2. 拼接成 key1value1key2value2... 格式
    3. 前后加上secret: secret + paramStr + secret
    4. MD5加密后取前30位
    """
    # 移除sig参数（如果存在）
    params_copy = {k: v for k, v in params.items() if k != 'sig'}
    # 按key排序
    items = sorted(params_copy.items())
    # 拼接参数字符串
    param_str = ''.join(f"{k}{v}" for k, v in items)
    # 前后加secret
    sign_str = f"{secret}{param_str}{secret}"
    # MD5加密并取前30位
    md5 = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    return md5[:30]

def edj_check_sys_params(params):
    for k in ['appkey', 'timestamp', 'ver', 'from', 'sig']:
        if k not in params:
            return False
    return True

def edj_check_sig(params, secret):
    params = dict(params)
    sig = params.pop('sig', None)
    sig_calc = edj_calc_sig(params, secret)
    return sig == sig_calc

def trigger_automatic_payment(order_info):
    """
    订单完成时自动触发支付流程 - 符合e代驾官方规范

    流程：
    1. 模拟客户支付完成
    2. 触发支付回调通知中间件

    参考官方文档：
    - 支付通知: http://openapi.d.edaijia.cn/doc/v2/payNotice.html
    - 支付回调: http://openapi.d.edaijia.cn/doc/v2/callbackNotice.html
    """
    try:
        edj_order_id = order_info.get('edj_order_id')
        third_order_id = order_info.get('third_order_id', f"F6_{edj_order_id}")

        # 模拟订单费用（基于预估费用）
        estimated_price = order_info.get('estimated_price', 100.0)  # 默认100元
        order_fee = estimated_price  # 订单总金额
        pay_amount = estimated_price * 0.95  # 模拟优惠后实付金额

        print(f"[自动支付] 触发订单 {edj_order_id} 的支付流程，金额: {pay_amount:.2f}元")

        # 延迟3秒模拟支付处理时间
        def delayed_payment_callback():
            time.sleep(3)

            # 构造支付回调参数 - 严格按照官方文档格式
            callback_params = {
                'orderId': edj_order_id,
                'merchant': '01051419',  # 商户号
                'payChannel': 'wxpay',  # 支付渠道：wxpay/alipay/unionpay
                'orderFee': f"{order_fee:.2f}",  # 订单金额（元）
                'payAmount': f"{pay_amount:.2f}",  # 实付金额（元）
                'payTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # 支付时间
                'timestamp': str(int(time.time())),  # 时间戳
                'thirdOrderId': third_order_id  # 第三方订单号
            }

            # 计算签名 - 按照官方规范
            # sig=md5(orderId&timestamp&partnerSecret)
            partner_secret = 'your_callback_secret'  # 应与中间件配置一致
            sign_str = f"{callback_params['orderId']}&{callback_params['timestamp']}&{partner_secret}"
            callback_params['sig'] = hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()

            # 发送支付回调到中间件
            try:
                middleware_url = 'http://127.0.0.1:5000/api/edaijia_payment_callback'
                response = requests.post(middleware_url, data=callback_params, timeout=10)

                if response.status_code == 200:
                    resp_json = response.json()
                    if resp_json.get('code') == 0:
                        print(f"[自动支付] 支付回调成功: {edj_order_id}")
                        # 更新订单支付状态
                        order_info['payment_status'] = 'paid'
                        order_info['pay_amount'] = pay_amount
                        order_info['payment_time'] = time.time()
                    else:
                        print(f"[自动支付] 支付回调失败: {resp_json.get('message')}")
                else:
                    print(f"[自动支付] 支付回调HTTP错误: {response.status_code}")

            except Exception as e:
                print(f"[自动支付] 支付回调异常: {e}")

        # 在后台线程中执行支付回调
        threading.Thread(target=delayed_payment_callback, daemon=True).start()

    except Exception as e:
        print(f"[自动支付] 触发支付流程失败: {e}")

@app.route('/api/v1/auth/token', methods=['POST'])
def f6_token():
    data = request.get_json()
    app_key = data.get('appKey')
    # 签名校验略
    if app_key != MOCK_APP_KEY:
        return jsonify({"code": 403, "msg": "Invalid AppKey"}), 403
    return jsonify({
        "code": 0,
        "data": {
            "token": "mock_token_123456",
            "expires_at": int(time.time()) + 3600
        },
        "msg": "SUCCESS"
    })

@app.route('/merchant/org/list', methods=['POST'])
def merchant_org_list():
    return jsonify({
        "code": 200,
        "data": {
            "list": [
                {
                    "idOwnOrg": "1001",
                    "name": "测试门店A",
                    "shortName": "A店",
                    "province": "北京",
                    "city": "北京",
                    "area": "朝阳",
                    "detailAddress": "望京SOHO"
                },
                {
                    "idOwnOrg": "1002",
                    "name": "测试门店B",
                    "shortName": "B店",
                    "province": "上海",
                    "city": "上海",
                    "area": "浦东",
                    "detailAddress": "世纪大道"
                }
            ]
        },
        "message": "SUCCESS"
    })

@app.route('/customer/list', methods=['POST'])
def customer_list():
    app_key = request.headers.get('X-Ca-Key')
    signature = request.headers.get('X-Ca-Signature')
    body = request.get_data(as_text=True)
    if app_key != MOCK_APP_KEY:
        return jsonify({"code": 403, "message": "Invalid AppKey"}), 403
    expected_sign = calc_signature(MOCK_APP_KEY, MOCK_APP_SECRET, body)
    if signature != expected_sign:
        return jsonify({"code": 403, "message": "Invalid Signature"}), 403
    data = request.get_json()
    page_size = 10
    current_page = 1
    if data and 'paramValues' in data and len(data['paramValues']) > 0:
        params = data['paramValues'][0]
        page_size = params.get('pageSize', 10)
        current_page = params.get('currentPage', 1)
    customers = [
        {
            "id": "f6_sim_101",
            "name": "张三",
            "phone": "13812345670",
            "car_model": "大众朗逸",
            "plate_number": "京A00001",
            "address": "北京朝阳区望京SOHO",
            "remarks": "e代驾订单, VIP"
        },
        {
            "id": "f6_sim_102",
            "name": "李四",
            "phone": "13912345671",
            "car_model": "丰田凯美瑞",
            "plate_number": "沪B00002",
            "address": "上海浦东新区世纪大道",
            "remarks": "e代驾订单"
        }
    ]
    resp = {
        "code": 200,
        "data": {
            "total": len(customers),
            "list": customers[(current_page-1)*page_size:current_page*page_size]
        },
        "message": "SUCCESS"
    }
    return jsonify(resp)

@app.route('/openApi/accessToken', methods=['POST'])
def edj_access_token():
    """e代驾获取访问令牌API - 符合官方规范"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    return jsonify({
        "code": "0",  # 官方返回字符串格式
        "message": "success",
        "data": {
            "accessToken": f"mock_access_token_{int(time.time())}",
            "expiresIn": 7200
        }
    })

@app.route('/customer/getAuthenToken', methods=['POST'])
def edj_get_authen_token():
    """e代驾获取用户授权token API - 符合官方规范"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 获取手机号参数
    phone = params.get('phone', '')
    if not phone:
        return jsonify({"code":"4","message":"手机号不能为空"}), 200

    return jsonify({
        "code":"0",
        "message":"success",
        "data":{
            "encrypt_authentoken": f"MOCK_USER_TOKEN_{phone}_{int(time.time())}"
        }
    })


@app.route('/order/costestimateV2', methods=['POST'])
def edj_cost_estimate_v2():
    """e代驾预估费用V2接口 - 完全符合官方文档规范"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 获取请求参数 - 严格按照官方文档
    token = params.get('token', '')
    start_longitude = params.get('start_longitude', '')
    start_latitude = params.get('start_latitude', '')
    end_longitude = params.get('end_longitude', '')
    end_latitude = params.get('end_latitude', '')

    # 可选参数
    channel = params.get('channel', '')  # 远程单传01007
    bonus_sn = params.get('bonus_sn', '')  # 优惠券码
    is_use_bonus = params.get('is_use_bonus', '0')  # 是否使用优惠券
    estimate_distance = params.get('estimate_distance', '')  # 合作方预估距离(米)
    estimate_duration = params.get('estimate_duration', '')  # 合作方预估时长(秒)

    print(f"[预估费用V2] 请求参数: token={token}, 起点=({start_longitude}, {start_latitude}), 终点=({end_longitude}, {end_latitude})")
    print(f"[预估费用V2] 可选参数: channel={channel}, bonus_sn={bonus_sn}, is_use_bonus={is_use_bonus}")

    # 验证必要参数 - 严格按照官方文档
    required_params = ['token', 'start_latitude', 'start_longitude', 'end_latitude', 'end_longitude']
    for param in required_params:
        if not params.get(param):
            return jsonify({
                'code': '4',
                'message': f'缺少必要参数: {param}',
                'data': None
            })

    try:
        # 转换坐标为浮点数
        start_lon = float(start_longitude)
        start_lat = float(start_latitude)
        end_lon = float(end_longitude)
        end_lat = float(end_latitude)

        # 计算距离（使用Haversine公式）
        import math

        def calculate_distance(lat1, lon1, lat2, lon2):
            """计算两点间距离（公里）"""
            R = 6371  # 地球半径（公里）
            lat1_rad = math.radians(lat1)
            lon1_rad = math.radians(lon1)
            lat2_rad = math.radians(lat2)
            lon2_rad = math.radians(lon2)
            dlat = lat2_rad - lat1_rad
            dlon = lon2_rad - lon1_rad
            a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
            return R * c

        distance = calculate_distance(start_lat, start_lon, end_lat, end_lon)

        # 如果提供了合作方预估距离，优先使用
        if estimate_distance and estimate_duration:
            try:
                distance = float(estimate_distance) / 1000  # 转换米为公里
                duration_minutes = float(estimate_duration) / 60  # 转换秒为分钟
            except:
                duration_minutes = max(15, distance * 3)  # 默认计算
        else:
            duration_minutes = max(15, distance * 3)  # 每公里3分钟

        # 模拟费用计算 - 按照e代驾实际计费规则
        start_fee = 5.0  # 起步价(含1公里)
        millage_fee = max(0, (distance - 1) * 1.2)  # 里程费，超过1公里按1.2元/公里
        time_fee = max(0.5, duration_minutes * 1.0)  # 时长费，1元/分钟
        return_fee = max(0, (distance - 5) * 2.0) if distance > 5 else 0  # 远途费，超过5公里
        extra_server_fee = 1.9  # 代叫服务费
        remote_fee = 2.0 if channel == '01007' else 0  # 远程司机调度费

        # 动态费用
        dynamic_fee = 0.0
        dynamic_rate = "0"
        fee_max = 0.0
        fee_type = 1  # 1:金额 2:倍率

        # 模拟高峰期动态加价
        current_hour = datetime.now().hour
        if current_hour in [7, 8, 17, 18, 19] and random.random() > 0.5:  # 高峰期50%概率加价
            dynamic_fee = random.uniform(2, 8)
            dynamic_rate = "0.2"
            fee_max = 10.0

        # 计算总费用
        total_fee = start_fee + millage_fee + time_fee + return_fee + extra_server_fee + remote_fee + dynamic_fee

        # 优惠券抵扣
        coupon_deduction = 0.0
        if is_use_bonus == '1' and bonus_sn:
            coupon_deduction = min(5.0, total_fee * 0.1)  # 模拟优惠券最多抵扣5元或10%

        need_pay = max(0, total_fee - coupon_deduction)

        # 构造费用明细 - 完全按照官方文档格式
        fee_detail = [
            {
                "children": [],
                "data_id": "start_fee",
                "money": f"{start_fee:.1f}",
                "title": "起步价(含1公里)",
                "total_value": "1"
            },
            {
                "children": [
                    {
                        "children": [],
                        "data_id": "start_fee",
                        "item_type": 0,
                        "money": "0",
                        "title": "起步里程(含1.0公里)",
                        "total_value": "1"
                    },
                    {
                        "children": [],
                        "data_id": "millage_fee",
                        "item_type": 0,
                        "money": f"{millage_fee:.1f}",
                        "title": f"收费里程(共{distance:.1f}公里)",
                        "total_value": f"{distance:.1f}"
                    }
                ],
                "data_id": "millage_fee",
                "money": f"{millage_fee:.1f}",
                "title": f"收费里程(共{distance:.1f}公里)",
                "total_value": f"{distance:.1f}"
            },
            {
                "children": [
                    {
                        "children": [],
                        "data_id": "start_time_fee",
                        "item_type": 0,
                        "money": "0.5",
                        "title": "起步时长(共1分钟)",
                        "total_value": "1"
                    },
                    {
                        "children": [],
                        "data_id": "charge_time_fee",
                        "item_type": 0,
                        "money": f"{time_fee - 0.5:.1f}",
                        "title": f"计费时长(共{duration_minutes:.0f}分钟)",
                        "total_value": f"{duration_minutes:.0f}"
                    }
                ],
                "data_id": "time_fee",
                "money": f"{time_fee:.1f}",
                "title": f"时长费(共{duration_minutes:.0f}分钟)",
                "total_value": f"{duration_minutes:.0f}"
            }
        ]

        # 添加远途费（如果有）
        if return_fee > 0:
            fee_detail.append({
                "children": [
                    {
                        "children": [],
                        "data_id": "return_fee",
                        "item_type": 0,
                        "money": f"{return_fee:.1f}",
                        "title": f"00:00:00-23:59:59(2.00元/1.0公里)",
                        "total_value": f"{distance - 5:.1f}"
                    }
                ],
                "data_id": "return_fee",
                "money": f"{return_fee:.1f}",
                "title": f"远途费(共{distance - 5:.1f}公里)",
                "total_value": f"{distance:.1f}"
            })

        # 添加代叫服务费
        fee_detail.append({
            "children": [],
            "data_id": "extra_server_fee",
            "money": f"{extra_server_fee:.1f}",
            "title": "代叫服务费",
            "total_value": "0"
        })

        # 添加远程司机调度费（如果有）
        if remote_fee > 0:
            fee_detail.append({
                "children": [],
                "data_id": "remote_fee",
                "money": f"{remote_fee:.1f}",
                "title": "远程司机调度费",
                "total_value": "0"
            })

        # 优惠券抵扣明细
        order_fee_deduction = []
        if coupon_deduction > 0:
            order_fee_deduction.append({
                "title": "优惠券抵扣",
                "data_id": "coupon_deduction",
                "money": f"-{coupon_deduction:.1f}"
            })

        # 构造响应数据 - 严格按照官方文档格式
        response_data = {
            "code": "0",
            "message": "success",
            "data": {
                "dynamic": {
                    "dynamic_fee": dynamic_fee,
                    "dynamic_rate": dynamic_rate,
                    "fee_max": fee_max,
                    "fee_type": fee_type
                },
                "fee_detail": fee_detail,
                "is_fixed_price": 0,  # 是否一口价 1:是 0:否
                "total": f"{total_fee:.1f}",
                "need_pay": f"{need_pay:.1f}",
                "order_fee_deduction": order_fee_deduction,
                "priceListUrl": f"https://h5.edaijia.cn/app/price.html?token={token}&estimateId=es{int(time.time())}&cityName=深圳市&cityId=16&latitude={start_lat}&longitude={start_lon}&source=2000&channel=01003&from=01051419",
                "distance": round(distance, 1),
                "duration": round(duration_minutes, 1)
            }
        }

        print(f"[预估费用V2] 响应数据: 总费用={total_fee:.1f}, 需支付={need_pay:.1f}, 距离={distance:.1f}km, 时长={duration_minutes:.1f}分钟")
        return jsonify(response_data)

    except ValueError as e:
        print(f"[预估费用V2] 坐标格式错误: {e}")
        return jsonify({
            "code": "4",
            "message": "坐标格式错误",
            "data": None
        })
    except Exception as e:
        print(f"[预估费用V2] 计算错误: {e}")
        return jsonify({
            "code": "1002",
            "message": f"预估费用计算失败: {str(e)}",
            "data": None
        })


@app.route('/order/getCancelFee', methods=['POST'])
def edj_get_cancel_fee():
    """e代驾查询订单取消费接口 - 完全符合官方文档规范"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 获取请求参数 - 严格按照官方文档
    token = params.get('token', '')
    edj_order_id = params.get('edj_order_id', '')

    print(f"[查询取消费] 请求参数: token={token}, edj_order_id={edj_order_id}")

    # 验证必要参数
    if not token:
        return jsonify({
            "code": "4",
            "message": "token不能为空",
            "data": None
        })

    if not edj_order_id:
        return jsonify({
            "code": "4",
            "message": "edj_order_id不能为空",
            "data": None
        })

    try:
        # 查找订单信息 - 支持动态创建订单记录
        order_found = None
        for _, order_info in MOCK_ORDERS.items():
            if order_info.get('edj_order_id') == edj_order_id:
                order_found = order_info
                break

        if not order_found:
            return jsonify({
                "code": "5",
                "message": "订单不存在",
                "data": None
            })

        # 模拟取消费用计算
        order_age = time.time() - order_found.get('created_time', order_found.get('order_time', time.time()))

        # 根据订单状态和时间计算取消费用
        cancel_fee = 0.0
        wait_fee = 0.0
        wait_time = 0

        # 模拟取消费用规则
        if order_age < 300:  # 5分钟内免费取消
            cancel_fee = 0.0
            wait_fee = 0.0
        elif order_age < 900:  # 15分钟内收取基础取消费
            cancel_fee = 5.0
            wait_fee = 0.0
        else:  # 超过15分钟收取取消费和等候费
            cancel_fee = 5.0
            wait_time = max(0, int(order_age - 900))  # 超过15分钟的等候时间
            wait_fee = wait_time * 0.5 / 60  # 每分钟0.5元等候费

        total_cost = cancel_fee + wait_fee

        # 构造响应数据 - 严格按照官方文档格式
        response_data = {
            "code": "0",
            "message": "success",
            "data": {
                "fee_detail": {
                    "cancel_fee": f"{cancel_fee:.2f}",
                    "wait_fee": f"{wait_fee:.2f}",
                    "wait_time": str(wait_time),
                    "total_cost": f"{total_cost:.2f}"
                }
            }
        }

        print(f"[查询取消费] 响应数据: 取消费={cancel_fee:.2f}, 等候费={wait_fee:.2f}, 总费用={total_cost:.2f}")
        return jsonify(response_data)

    except Exception as e:
        print(f"[查询取消费] 计算错误: {e}")
        return jsonify({
            "code": "1002",
            "message": f"查询取消费失败: {str(e)}",
            "data": None
        })


@app.route('/order/cancel', methods=['POST'])
def edj_cancel_order():
    """e代驾订单取消接口 - 完全符合官方文档规范"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 获取请求参数 - 严格按照官方文档
    token = params.get('token', '')
    edj_order_id = params.get('edj_order_id', '')
    edj_booking_id = params.get('edj_booking_id', '')
    reason_code = params.get('reason_code', '')  # 1平台取消，0用户取消
    reason_detail = params.get('reason_detail', '')  # 取消原因详情

    print(f"[订单取消] 请求参数: token={token}, edj_order_id={edj_order_id}, edj_booking_id={edj_booking_id}")
    print(f"[订单取消] 取消原因: reason_code={reason_code}, reason_detail={reason_detail}")

    # 验证必要参数
    if not token:
        return jsonify({
            "code": "4",
            "message": "token不能为空",
            "data": None
        })

    if not edj_order_id and not edj_booking_id:
        return jsonify({
            "code": "4",
            "message": "edj_order_id和edj_booking_id至少传入一个",
            "data": None
        })

    try:
        # 查找订单信息 - 支持动态创建订单记录
        order_found = None

        if edj_booking_id:
            order_found = MOCK_ORDERS.get(edj_booking_id)
        elif edj_order_id:
            for _, order_info in MOCK_ORDERS.items():
                if order_info.get('edj_order_id') == edj_order_id:
                    order_found = order_info
                    break

        # 如果订单不存在，动态创建一个订单记录（模拟真实场景）
        if not order_found and edj_order_id:
            print(f"[订单取消] 订单 {edj_order_id} 不存在，动态创建订单记录")

            # 创建一个基础的订单记录
            booking_id = f"MOCK_{edj_order_id}"
            order_found = {
                'edj_order_id': edj_order_id,
                'booking_id': booking_id,
                'status': '102',  # 系统派单中
                'status_desc': '系统派单中',
                'order_time': time.time() - 300,  # 假设5分钟前创建
                'customer_name': '测试客户',
                'customer_phone': '13800138000',
                'pickup_address': '测试起点',
                'destination_address': '测试终点',
                'price': 25.0
            }

            # 添加到Mock订单列表
            MOCK_ORDERS[booking_id] = order_found
            print(f"[订单取消] 已动态创建订单记录: {booking_id}")

        if not order_found:
            return jsonify({
                "code": "5",
                "message": "订单不存在",
                "data": None
            })

        # 检查订单是否可以取消
        current_status = order_found.get('status', '0')
        cancellable_statuses = ['0', '102', '180', '301', '302']  # 可取消的状态

        if current_status not in cancellable_statuses:
            return jsonify({
                "code": "6",
                "message": "订单当前状态不允许取消",
                "data": None
            })

        # 计算取消费用（与查询取消费接口逻辑一致）
        order_age = time.time() - order_found.get('created_time', order_found.get('order_time', time.time()))

        cancel_fee = 0.0
        wait_fee = 0.0
        wait_time = 0

        if order_age < 300:  # 5分钟内免费取消
            cancel_fee = 0.0
            wait_fee = 0.0
        elif order_age < 900:  # 15分钟内收取基础取消费
            cancel_fee = 5.0
            wait_fee = 0.0
        else:  # 超过15分钟收取取消费和等候费
            cancel_fee = 5.0
            wait_time = max(0, int(order_age - 900))
            wait_fee = wait_time * 0.5 / 60

        total_cost = cancel_fee + wait_fee

        # 更新订单状态为已取消 - 根据取消原因设置状态码
        # 根据reason_code判断取消类型
        if reason_code in ['0', '1002', '1003']:  # 用户取消相关
            order_found['status'] = '403'  # 客户取消
        else:
            order_found['status'] = '404'  # 司机取消（其他情况）

        order_found['cancel_time'] = int(time.time())
        order_found['cancel_reason_code'] = reason_code
        order_found['cancel_reason_detail'] = reason_detail
        order_found['cancel_fee'] = total_cost

        # 构造响应数据 - 严格按照官方文档格式（幂等）
        response_data = {
            "code": "0",
            "message": "success",
            "data": {
                "fee_detail": {
                    "cancel_fee": f"{cancel_fee:.2f}",
                    "wait_fee": f"{wait_fee:.2f}",
                    "wait_time": str(wait_time),
                    "total_cost": f"{total_cost:.2f}"
                }
            }
        }

        print(f"[订单取消] 取消成功: {edj_order_id or edj_booking_id}, 费用={total_cost:.2f}")
        return jsonify(response_data)

    except Exception as e:
        print(f"[订单取消] 取消失败: {e}")
        return jsonify({
            "code": "1002",
            "message": f"订单取消失败: {str(e)}",
            "data": None
        })


@app.route('/order/cancel/tag', methods=['GET'])
def edj_get_cancel_reasons():
    """e代驾获取订单取消原因接口 - 完全符合官方文档规范"""
    # GET请求，参数在URL中
    edj_order_id = request.args.get('edj_order_id', '')

    print(f"[获取取消原因] 请求参数: edj_order_id={edj_order_id}")

    # 验证必要参数
    if not edj_order_id:
        return jsonify({
            "code": "4",
            "message": "edj_order_id不能为空",
            "data": None
        })

    try:
        # 查找订单信息
        order_found = None
        for _, order_info in MOCK_ORDERS.items():
            if order_info.get('edj_order_id') == edj_order_id:
                order_found = order_info
                break

        if not order_found:
            return jsonify({
                "code": "5",
                "message": "订单不存在",
                "data": None
            })

        # 根据订单状态返回不同的取消原因
        order_status = order_found.get('status', '0')

        # 司机接单到就位的取消原因
        if order_status in ['301', '302']:  # 司机接单、司机就位
            response_data = {
                "code": "0",
                "message": "success",
                "data": {
                    "reason_type_map": {
                        "system": "平台原因",
                        "driver": "司机原因",
                        "customer": "我的原因"
                    },
                    "desc": "请选择您取消代驾的原因（注：私自协商代驾将被封号禁用）",
                    "cancel_reasons": [
                        {
                            "code": 1001,
                            "detail": "我改变行程方式了",
                            "type": "customer"
                        },
                        {
                            "code": 1002,
                            "detail": "暂时不需要代驾了",
                            "type": "customer"
                        },
                        {
                            "code": 1003,
                            "detail": "跟司机协商消单后继续代驾",
                            "type": "customer"
                        },
                        {
                            "code": 1004,
                            "detail": "司机故意拖延时间",
                            "type": "driver"
                        },
                        {
                            "code": 1005,
                            "detail": "服务态度差",
                            "type": "driver"
                        },
                        {
                            "code": 1006,
                            "detail": "代驾收费太贵",
                            "type": "system"
                        }
                    ]
                }
            }
        else:
            # 司机就位后的取消原因（开车后不能取消）
            response_data = {
                "code": "0",
                "message": "success",
                "data": {
                    "desc": "请选择您取消代驾的原因（注：私自协商代驾将被封号禁用）",
                    "cancel_reasons": [
                        {
                            "code": 101,
                            "detail": "司机到位太慢，等不及"
                        },
                        {
                            "code": 102,
                            "detail": "代驾收费太贵"
                        },
                        {
                            "code": 103,
                            "detail": "司机要求取消订单"
                        },
                        {
                            "code": 104,
                            "detail": "找到了其他代驾"
                        },
                        {
                            "code": 105,
                            "detail": "暂时不需要代驾了"
                        }
                    ]
                }
            }

        print(f"[获取取消原因] 响应数据: 订单状态={order_status}, 原因数量={len(response_data['data']['cancel_reasons'])}")
        return jsonify(response_data)

    except Exception as e:
        print(f"[获取取消原因] 查询失败: {e}")
        return jsonify({
            "code": "1002",
            "message": f"获取取消原因失败: {str(e)}",
            "data": None
        })


@app.route('/order/modify/destination', methods=['POST'])
def edj_modify_destination():
    """e代驾修改订单目的地接口 - 完全符合官方文档规范"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 获取请求参数 - 严格按照官方文档
    token = params.get('token', '')
    order_id = params.get('orderId', '')
    destination_address = params.get('destinationAddress', '')
    destination_lat = params.get('destinationLat', '')
    destination_lng = params.get('destinationLng', '')
    gps_type = params.get('gpsType', 'baidu')  # 默认百度

    print(f"[修改目的地] 请求参数: token={token}, orderId={order_id}")
    print(f"[修改目的地] 新目的地: {destination_address} ({destination_lat}, {destination_lng})")

    # 验证必要参数
    required_params = ['token', 'orderId', 'destinationAddress', 'destinationLat', 'destinationLng']
    for param in required_params:
        if not params.get(param):
            return jsonify({
                "code": "4",
                "message": f"缺少必要参数: {param}",
                "data": None
            })

    try:
        # 查找订单信息 - 支持动态创建订单记录
        order_found = None

        for _, order_info in MOCK_ORDERS.items():
            if order_info.get('edj_order_id') == order_id:
                order_found = order_info
                break

        if not order_found:
            return jsonify({
                "code": "5",
                "message": "订单不存在",
                "data": None
            })

        # 检查订单状态是否可以修改
        current_status = order_found.get('status', '0')
        modifiable_statuses = ['102', '180', '301', '302']  # 可修改的状态

        if current_status not in modifiable_statuses:
            return jsonify({
                "code": "6",
                "message": f"订单当前状态({current_status})不允许修改目的地",
                "data": None
            })

        # 验证坐标格式
        try:
            lat = float(destination_lat)
            lng = float(destination_lng)

            # 简单的坐标范围验证（中国境内）
            if not (18 <= lat <= 54 and 73 <= lng <= 135):
                raise ValueError("坐标超出有效范围")

        except ValueError as e:
            return jsonify({
                "code": "4",
                "message": f"坐标格式错误: {str(e)}",
                "data": None
            })

        # 更新订单目的地信息
        order_found['destination_address'] = destination_address
        order_found['destination_latitude'] = lat
        order_found['destination_longitude'] = lng
        order_found['gps_type'] = gps_type
        order_found['modify_time'] = int(time.time())

        # 构造响应数据 - 严格按照官方文档格式
        response_data = {
            "code": "0",
            "message": "success",
            "data": None
        }

        print(f"[修改目的地] 修改成功: {order_id}, 新目的地: {destination_address}")
        return jsonify(response_data)

    except Exception as e:
        print(f"[修改目的地] 修改失败: {e}")
        return jsonify({
            "code": "1002",
            "message": f"修改目的地失败: {str(e)}",
            "data": None
        })


@app.route('/order/estimate/after/modify', methods=['POST'])
def edj_modify_destination_estimate():
    """e代驾修改订单目的地后重新预估接口 - 完全符合官方文档规范"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 获取请求参数 - 严格按照官方文档
    token = params.get('token', '')
    order_id = params.get('orderId', '')
    destination_address = params.get('destinationAddress', '')
    destination_lat = params.get('destinationLat', '')
    destination_lng = params.get('destinationLng', '')

    print(f"[修改后预估] 请求参数: token={token}, orderId={order_id}")
    print(f"[修改后预估] 新目的地: {destination_address} ({destination_lat}, {destination_lng})")

    # 验证必要参数
    required_params = ['token', 'orderId', 'destinationAddress', 'destinationLat', 'destinationLng']
    for param in required_params:
        if not params.get(param):
            return jsonify({
                "code": "4",
                "message": f"缺少必要参数: {param}",
                "data": None
            })

    try:
        # 查找订单信息 - 支持动态创建订单记录
        order_found = None

        for _, order_info in MOCK_ORDERS.items():
            if order_info.get('edj_order_id') == order_id:
                order_found = order_info
                break

        if not order_found:
            return jsonify({
                "code": "5",
                "message": "订单不存在",
                "data": None
            })

        # 验证坐标并计算新的距离和费用
        try:
            new_lat = float(destination_lat)
            new_lng = float(destination_lng)
            start_lat = order_found.get('pickup_latitude', 22.6569)
            start_lng = order_found.get('pickup_longitude', 114.1315)

        except ValueError as e:
            return jsonify({
                "code": "4",
                "message": f"坐标格式错误: {str(e)}",
                "data": None
            })

        # 计算新的距离和时间
        import math

        def calculate_distance(lat1, lon1, lat2, lon2):
            """计算两点间距离（公里）"""
            R = 6371
            lat1_rad = math.radians(lat1)
            lon1_rad = math.radians(lon1)
            lat2_rad = math.radians(lat2)
            lon2_rad = math.radians(lon2)
            dlat = lat2_rad - lat1_rad
            dlon = lon2_rad - lon1_rad
            a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
            return R * c

        new_distance = calculate_distance(start_lat, start_lng, new_lat, new_lng)
        new_duration = max(15, new_distance * 3)  # 预计时间（分钟）

        # 重新计算费用
        start_fee = 5.0
        millage_fee = max(0, (new_distance - 1) * 1.2)
        time_fee = max(0.5, new_duration * 1.0)
        return_fee = max(0, (new_distance - 5) * 2.0) if new_distance > 5 else 0
        extra_server_fee = 1.9

        total_money = start_fee + millage_fee + time_fee + return_fee + extra_server_fee
        discount_money = total_money  # 暂无优惠

        # 构造HTML描述信息
        modify_desc = f"<font color='#19191A' style='font-family: PingFangSC-Regular;font-size: 14px;'>修改后预估金额为</font><font color='#44A7EF' style='font-family: PingFangSC-Semibold;font-size: 18px;font-weight: 600;'>{total_money:.2f}</font><font color='#19191A' style='font-family: PingFangSC-Regular;font-size: 14px;'>元</font>"

        modify_tips = "<font style='font-family: PingFangSC-Regular;font-size: 12px;color: #8C929C;' color='#8C929C'>·修改后本次行程将按实际里程计费<br/>·请提醒司机按新目的地更新导航</font>"

        fee_detail_url = f"https://h5.edaijia.cn/user-app-client/detail-of-charges/index.html?h5_fee_type=1&estimate_id=es{int(time.time())}&city=深圳市&city_id=16&source=5&channel=01003&latitude={new_lat}&longitude={new_lng}"

        # 构造响应数据 - 严格按照官方文档格式
        response_data = {
            "code": "0",
            "message": "success",
            "data": {
                "order_id": order_id,
                "duration": round(new_duration, 1),
                "distance": round(new_distance, 1),
                "modify_desc": modify_desc,
                "modify_tips": modify_tips,
                "fee_detail_url": fee_detail_url,
                "total_money": f"{total_money:.2f}",
                "discount_money": f"{discount_money:.2f}"
            }
        }

        print(f"[修改后预估] 预估成功: {order_id}, 新费用: ¥{total_money:.2f}, 距离: {new_distance:.1f}km")
        return jsonify(response_data)

    except Exception as e:
        print(f"[修改后预估] 预估失败: {e}")
        return jsonify({
            "code": "1002",
            "message": f"修改后预估失败: {str(e)}",
            "data": None
        })


@app.route('/order/commit', methods=['POST'])
def edj_order_commit():
    """e代驾下单API - 完全符合官方规范"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    print(f"e代驾下单请求参数: {params}")

    # 检查必填参数 - 严格按照官方文档
    required_params = [
        'phone', 'token', 'start_address', 'start_longitude', 'start_latitude',
        'end_address', 'end_longitude', 'end_latitude', 'third_order_id'
    ]

    for param in required_params:
        if param not in params or not params[param]:
            return jsonify({
                "code":"4",
                "message": f"缺少必要参数: {param}"
            }), 200

    # 获取订单参数
    phone = params.get('phone')
    contact_phone = params.get('contact_phone', phone)  # 默认为下单人手机号
    third_order_id = params.get('third_order_id')
    cash_only = params.get('cash_only', '0')  # 0本人支付，1乘客现金支付

    # 生成订单ID - 符合e代驾格式
    order_timestamp = int(time.time())
    edj_order_id = f"{order_timestamp}{random.randint(100, 999)}"
    edj_booking_id = hashlib.md5(f"booking_{third_order_id}_{time.time()}".encode()).hexdigest()

    # 获取预估费用（用于后续自动支付）
    estimated_price = 100.0  # 默认费用
    if 'start_longitude' in params and 'end_longitude' in params:
        try:
            # 简单距离计算
            import math
            start_lon = float(params.get('start_longitude', 0))
            start_lat = float(params.get('start_latitude', 0))
            end_lon = float(params.get('end_longitude', 0))
            end_lat = float(params.get('end_latitude', 0))

            # 计算距离并估算费用
            R = 6371  # 地球半径（公里）
            lat1_rad = math.radians(start_lat)
            lon1_rad = math.radians(start_lon)
            lat2_rad = math.radians(end_lat)
            lon2_rad = math.radians(end_lon)
            dlat = lat2_rad - lat1_rad
            dlon = lon2_rad - lon1_rad
            a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
            distance = R * c

            # 简单费用计算
            estimated_price = max(50, 5 + distance * 1.2 + distance * 3 * 1.0 + 1.9)
        except:
            pass

    # 构建响应数据 - 严格按照官方格式
    response_data = {
        "edj_order_id": edj_order_id,
        "edj_booking_id": edj_booking_id,
        "order_time": order_timestamp,
        "timeout": 300  # 派单超时时间(秒)
    }

    # 将订单信息存储到模拟数据库
    order_info = {
        "edj_order_id": edj_order_id,
        "edj_booking_id": edj_booking_id,
        "third_order_id": third_order_id,
        "phone": phone,
        "contact_phone": contact_phone,
        "start_address": params.get('start_address'),
        "end_address": params.get('end_address'),
        "start_longitude": params.get('start_longitude'),
        "start_latitude": params.get('start_latitude'),
        "end_longitude": params.get('end_longitude'),
        "end_latitude": params.get('end_latitude'),
        "cash_only": cash_only,
        "order_time": order_timestamp,
        "status": "0",  # 0:派单中
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "estimated_price": estimated_price,  # 保存预估费用用于自动支付
        "payment_triggered": False  # 支付触发标记
    }

    # 存储到全局订单字典
    MOCK_ORDERS[edj_booking_id] = order_info

    print(f"创建订单成功: {edj_order_id}, booking_id: {edj_booking_id}")

    return jsonify({
        "code":"0",
        "message":"success",
        "data": response_data
    })

@app.route('/location/current', methods=['POST'])
def get_current_location():
    """模拟获取当前位置的API"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 随机决定是否成功获取位置
    success = True if time.time() % 10 > 3 else False  # 70%的概率成功

    if success:
        return jsonify({
            "code":"0",
            "message":"success",
            "data":{
                "address": "北京市朝阳区望京SOHO T1",
                "latitude": 39.9947,
                "longitude": 116.4808,
                "accuracy": 15,
                "timestamp": int(time.time())
            }
        })
    else:
        return jsonify({
            "code":"1001",
            "message":"获取位置失败，请手动输入",
            "data":None
        })

@app.route('/appointment/times', methods=['POST'])
def get_appointment_times():
    """模拟获取可预约时间的API"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 生成未来24小时内的预约时间，每小时一个选项
    now = time.time()
    times = []

    for i in range(1, 25):
        future_time = now + i * 60 * 60
        time_struct = time.localtime(future_time)
        time_str = time.strftime('%Y-%m-%d %H:%M:%S', time_struct)
        times.append({
            "time": time_str,
            "available": True
        })

    return jsonify({
        "code":"0",
        "message":"success",
        "data":{
            "times": times
        }
    })

@app.route('/order/polling', methods=['POST'])
def edj_order_polling():
    """e代驾订单状态查询API - 完全符合官方规范"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 检查必填参数
    edj_booking_id = params.get('edj_booking_id', '')
    token = params.get('token', '')

    if not edj_booking_id:
        return jsonify({"code":"4","message":"edj_booking_id不能为空"}), 200
    if not token:
        return jsonify({"code":"4","message":"token不能为空"}), 200

    # 查找订单信息
    order_info = MOCK_ORDERS.get(edj_booking_id)
    if not order_info:
        return jsonify({"code":"5","message":"订单不存在"}), 200

    # 模拟订单状态变化 - 按照官方状态码
    order_statuses = [
        {"order_status": 0, "state_code": "102", "state_desc": "开始系统派单"},
        {"order_status": 0, "state_code": "180", "state_desc": "系统派单中"},
        {"order_status": 2, "state_code": "301", "state_desc": "司机接单"},
        {"order_status": 2, "state_code": "302", "state_desc": "司机就位"},
        {"order_status": 2, "state_code": "303", "state_desc": "司机开车"},
        {"order_status": 2, "state_code": "304", "state_desc": "代驾结束"},
    ]

    # 根据订单创建时间模拟状态进展
    order_age = time.time() - order_info['order_time']
    if order_age < 60:  # 1分钟内：派单中
        current_status = random.choice(order_statuses[:2])
    elif order_age < 300:  # 5分钟内：司机接单
        current_status = order_statuses[2]
    elif order_age < 600:  # 10分钟内：司机就位
        current_status = order_statuses[3]
    elif order_age < 1800:  # 30分钟内：司机开车
        current_status = order_statuses[4]
    else:  # 30分钟后：代驾结束
        current_status = order_statuses[5]
        # 订单完成时自动触发支付流程
        if current_status["state_code"] == "304" and not order_info.get('payment_triggered'):
            trigger_automatic_payment(order_info)
            order_info['payment_triggered'] = True

    # 构建响应数据 - 严格按照官方格式
    response_data = {
        "edj_order_id": order_info["edj_order_id"],
        "order_status": current_status["order_status"],  # 0:派单中 1:派单失败 2:司机接单
        "channel": "01003",  # 渠道标识
        "cancel_fee_free_minute": 2,  # 免责取消时间
        "wait_free_minute": 2  # 免等候时间
    }

    # 如果司机已接单，添加司机信息
    if current_status["order_status"] == 2:
        response_data["accept_driver_info"] = {
            "driver_id": "BJ9001",
            "name": "张师傅",
            "driver_phone": "13800138000",
            "new_level": "4.8",  # 服务星级
            "year": "5",  # 驾龄
            "service_times": "1250",  # 服务次数
            "order_state_code": current_status["state_code"],
            "latitude": "39.9042",  # 司机实时纬度
            "longitude": "116.4074",  # 司机实时经度
            "picture_small": "https://example.com/driver_avatar.jpg",
            "order_all_states": [
                {
                    "order_state_code": "301",
                    "order_state_timestamp": order_info['order_time'] + 60,
                    "order_state_content": "司机已接单"
                },
                {
                    "order_state_code": current_status["state_code"],
                    "order_state_timestamp": int(time.time()),
                    "order_state_content": current_status["state_desc"]
                }
            ]
        }

    print(f"订单状态查询: {edj_booking_id} -> {current_status['state_desc']}")

    return jsonify({
        "code":"0",
        "message":"success",
        "data": response_data
    })

@app.route('/order/payNotify', methods=['POST'])
def edj_pay_notify():
    """模拟e代驾支付通知接口"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 检查必要的支付参数
    required_params = ['edj_order_id', 'pay_amount', 'total_amount']
    for param in required_params:
        if param not in params:
            return jsonify({"code":"4","message":f"缺少必要参数: {param}"}), 200

    edj_order_id = params.get('edj_order_id')
    pay_amount = params.get('pay_amount')
    total_amount = params.get('total_amount')

    print(f"Received payment notification: orderId={edj_order_id}, payAmount={pay_amount}, totalAmount={total_amount}")

    # 模拟支付验证
    try:
        pay_amount_int = int(pay_amount)
        total_amount_int = int(total_amount)

        if pay_amount_int > total_amount_int:
            return jsonify({"code":"9","message":"支付金额不能大于订单总金额"}), 200

        if pay_amount_int <= 0:
            return jsonify({"code":"9","message":"支付金额必须大于0"}), 200

    except ValueError:
        return jsonify({"code":"9","message":"金额格式错误"}), 200

    # 模拟成功响应
    return jsonify({
        "code":"0",
        "message":"success",
        "data":{
            "confirm_result":"确认支付成功"
        }
    })

@app.route('/order/history/list', methods=['GET'])
def edj_history_list():
    """e代驾历史订单列表API - 完全符合官方规范"""
    # 获取GET参数
    token = request.args.get('token', '')
    page_size = int(request.args.get('pageSize', 10))
    page_no = int(request.args.get('pageNo', 0))

    # 验证系统参数 - GET请求的参数验证
    sys_params = {
        'appkey': request.args.get('appkey', ''),
        'timestamp': request.args.get('timestamp', ''),
        'ver': request.args.get('ver', ''),
        'from': request.args.get('from', ''),
        'sig': request.args.get('sig', '')
    }

    if not edj_check_sys_params(sys_params):
        return jsonify({"code": 3, "message": "系统参数缺失或非法"}), 200
    if not edj_check_sig(sys_params, EDJ_SECRET):
        return jsonify({"code": 7, "message": "sig错误，接口签名失败"}), 200

    if not token:
        return jsonify({"code": 4, "message": "token不能为空"}), 200

    # 从token中提取用户信息（模拟）
    if "MOCK_USER_TOKEN_" in token:
        user_phone = token.split("_")[3]  # 提取手机号
    else:
        user_phone = "13800138000"  # 默认手机号

    # 生成模拟历史订单数据
    history_orders = []
    total_orders = random.randint(15, 50)  # 模拟总订单数

    # 计算分页
    start_index = page_no * page_size
    end_index = min(start_index + page_size, total_orders)

    for i in range(start_index, end_index):
        order_id = 409000000 + i
        create_time = int(time.time()) - random.randint(86400, 7776000)  # 1天到90天前

        # 随机选择订单状态
        status_choice = random.choice([
            {"status": 1, "state": "已完成", "income": f"{random.randint(30, 150)}.00"},
            {"status": 2, "state": "已取消", "income": "0.00"}
        ])

        order = {
            "history_id": f"mock_history_{order_id}",
            "id": order_id,
            "location_start": random.choice([
                "北京朝阳区望京SOHO", "上海浦东新区世纪大道", "深圳南山区科技园",
                "广州天河区珠江新城", "成都高新区天府大道"
            ]),
            "location_end": random.choice([
                "北京首都国际机场", "上海虹桥机场", "深圳宝安机场",
                "广州白云机场", "成都双流机场"
            ]),
            "city_id": random.choice([1, 5, 16, 123]),
            "order_id": f"EDJ{order_id}",
            "start_time": create_time + 1800,  # 开始时间比创建时间晚30分钟
            "create_time": str(create_time),
            "status": status_choice["status"],
            "state": status_choice["state"],
            "income": status_choice["income"],
            "source": 1000,
            "channel": "01003",
            "order_number": f"2024{str(create_time)[-10:]}",
            "type": "1000001051672",
            "owner_phone": user_phone,
            "title": "日常代驾",
            "desc": datetime.fromtimestamp(create_time).strftime("%Y-%m-%d %H:%M:%S"),
            "begin": random.choice([
                "北京朝阳区望京SOHO", "上海浦东新区世纪大道", "深圳南山区科技园"
            ]),
            "end": random.choice([
                "北京首都国际机场", "上海虹桥机场", "深圳宝安机场"
            ]),
            "url": "edaijia://31",
            "is_comment": "N",
            "can_comment": 1 if status_choice["status"] == 1 else 0
        }

        history_orders.append(order)

    print(f"历史订单查询: token={token[:20]}..., pageSize={page_size}, pageNo={page_no}")
    print(f"返回 {len(history_orders)} 条订单，总计 {total_orders} 条")

    return jsonify({
        "code": 0,
        "message": "获取成功",
        "data": {
            "orderList": history_orders,
            "orderCount": total_orders,
            "tip_message": "超过15天不能进行评价"
        }
    })

@app.route('/order/batch-status', methods=['POST'])
def get_batch_order_status():
    """批量查询订单状态API"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    # 获取订单ID列表（逗号分隔）
    order_ids = params.get('order_ids', '').split(',')
    order_ids = [oid.strip() for oid in order_ids if oid.strip()]

    if not order_ids:
        return jsonify({"code":"4","message":"订单ID列表不能为空"}), 200

    # 查找订单状态
    order_statuses = []

    for order_id in order_ids:
        if order_id in MOCK_ORDERS:
            order = MOCK_ORDERS[order_id]
            order_statuses.append({
                "order_id": order_id,
                "status": order.get('status_code', '180'),
                "status_desc": order.get('status_desc', '系统派单中'),
                "driver_name": order.get('driver_name', ''),
                "driver_phone": order.get('driver_phone', ''),
                "price": order.get('price', 0),
                "updated_at": order.get('created_at', '')
            })
        else:
            order_statuses.append({
                "order_id": order_id,
                "status": "404",
                "status_desc": "订单不存在",
                "error": "订单未找到"
            })

    print(f"Batch status query for {len(order_ids)} orders")

    return jsonify({
        "code":"0",
        "message":"success",
        "data":{
            "orders": order_statuses
        }
    })

@app.route('/debug/orders', methods=['GET'])
def debug_orders():
    """调试接口：查看所有模拟订单"""
    return jsonify({
        "total_orders": len(MOCK_ORDERS),
        "orders": list(MOCK_ORDERS.values())[:10],  # 只返回前10个
        "customers": list(MOCK_CUSTOMERS.values())
    })

@app.route('/debug/reset', methods=['POST'])
def debug_reset():
    """调试接口：重置测试数据"""
    global MOCK_ORDERS, MOCK_CUSTOMERS, MOCK_POLLING_SESSIONS
    MOCK_ORDERS.clear()
    MOCK_CUSTOMERS.clear()
    MOCK_POLLING_SESSIONS.clear()
    # 生产环境：不重新初始化测试数据，保持空状态
    return jsonify({
        "message": "测试数据已重置",
        "total_orders": len(MOCK_ORDERS),
        "total_customers": len(MOCK_CUSTOMERS)
    })

@app.route('/geocoding/address', methods=['POST'])
def geocoding_address():
    """地址转经纬度API - 模拟e代驾地理编码服务"""
    params = request.form.to_dict()
    if not edj_check_sys_params(params):
        return jsonify({"code":"3","message":"系统参数缺失或非法"}), 200
    if not edj_check_sig(params, EDJ_SECRET):
        return jsonify({"code":"7","message":"sig错误，接口签名失败"}), 200

    address = params.get('address', '').strip()
    if not address:
        return jsonify({"code":"4","message":"地址不能为空"}), 200

    # 模拟地理编码 - 基于地址关键词匹配
    geocoding_data = get_mock_geocoding(address)

    if geocoding_data:
        return jsonify({
            "code":"0",
            "message":"success",
            "data": geocoding_data
        })
    else:
        return jsonify({
            "code":"1001",
            "message":"地址解析失败，请检查地址是否正确",
            "data": None
        })

def get_mock_geocoding(address):
    """模拟地理编码功能 - 基于关键词匹配返回经纬度"""

    # 预定义的地址库 - 涵盖常见地点
    address_database = {
        # 机场
        "首都机场": {"longitude": 116.5974, "latitude": 40.0799, "formatted_address": "北京首都国际机场"},
        "首都国际机场": {"longitude": 116.5974, "latitude": 40.0799, "formatted_address": "北京首都国际机场"},
        "大兴机场": {"longitude": 116.4109, "latitude": 39.5092, "formatted_address": "北京大兴国际机场"},
        "虹桥机场": {"longitude": 121.3364, "latitude": 31.1979, "formatted_address": "上海虹桥国际机场"},
        "浦东机场": {"longitude": 121.8057, "latitude": 31.1434, "formatted_address": "上海浦东国际机场"},
        "宝安机场": {"longitude": 113.8206, "latitude": 22.6390, "formatted_address": "深圳宝安国际机场"},
        "白云机场": {"longitude": 113.2990, "latitude": 23.3926, "formatted_address": "广州白云国际机场"},

        # 火车站
        "北京站": {"longitude": 116.4268, "latitude": 39.9031, "formatted_address": "北京站"},
        "北京西站": {"longitude": 116.3217, "latitude": 39.8963, "formatted_address": "北京西站"},
        "北京南站": {"longitude": 116.3785, "latitude": 39.8653, "formatted_address": "北京南站"},
        "上海站": {"longitude": 121.4581, "latitude": 31.2495, "formatted_address": "上海站"},
        "上海虹桥站": {"longitude": 121.3197, "latitude": 31.1938, "formatted_address": "上海虹桥火车站"},
        "深圳站": {"longitude": 114.1141, "latitude": 22.5332, "formatted_address": "深圳站"},
        "深圳北站": {"longitude": 114.0301, "latitude": 22.6097, "formatted_address": "深圳北站"},
        "广州站": {"longitude": 113.2644, "latitude": 23.1496, "formatted_address": "广州站"},
        "广州南站": {"longitude": 113.2730, "latitude": 22.9902, "formatted_address": "广州南站"},

        # 商业区
        "王府井": {"longitude": 116.4178, "latitude": 39.9097, "formatted_address": "北京市东城区王府井大街"},
        "西单": {"longitude": 116.3668, "latitude": 39.9069, "formatted_address": "北京市西城区西单"},
        "三里屯": {"longitude": 116.4551, "latitude": 39.9364, "formatted_address": "北京市朝阳区三里屯"},
        "国贸": {"longitude": 116.4614, "latitude": 39.9078, "formatted_address": "北京市朝阳区国贸"},
        "陆家嘴": {"longitude": 121.5057, "latitude": 31.2453, "formatted_address": "上海市浦东新区陆家嘴"},
        "南京路": {"longitude": 121.4737, "latitude": 31.2317, "formatted_address": "上海市黄浦区南京路"},
        "淮海路": {"longitude": 121.4648, "latitude": 31.2198, "formatted_address": "上海市黄浦区淮海路"},
        "科技园": {"longitude": 113.9547, "latitude": 22.5413, "formatted_address": "深圳市南山区科技园"},
        "华强北": {"longitude": 114.0875, "latitude": 22.5454, "formatted_address": "深圳市福田区华强北"},
        "珠江新城": {"longitude": 113.3220, "latitude": 23.1200, "formatted_address": "广州市天河区珠江新城"},

        # 大学
        "清华大学": {"longitude": 116.3264, "latitude": 40.0031, "formatted_address": "北京市海淀区清华大学"},
        "北京大学": {"longitude": 116.3105, "latitude": 39.9926, "formatted_address": "北京市海淀区北京大学"},
        "复旦大学": {"longitude": 121.5010, "latitude": 31.2989, "formatted_address": "上海市杨浦区复旦大学"},
        "深圳大学": {"longitude": 113.9352, "latitude": 22.5342, "formatted_address": "深圳市南山区深圳大学"},

        # 医院
        "协和医院": {"longitude": 116.4122, "latitude": 39.9139, "formatted_address": "北京协和医院"},
        "301医院": {"longitude": 116.3398, "latitude": 39.9075, "formatted_address": "中国人民解放军总医院"},
        "华山医院": {"longitude": 121.4372, "latitude": 31.2118, "formatted_address": "复旦大学附属华山医院"},
        "北大医院": {"longitude": 113.9547, "latitude": 22.5413, "formatted_address": "北京大学深圳医院"},

        # 测试地址 - 用于验证是否使用Mock API
        "火星上的某个地址12345": {"longitude": 999.999, "latitude": 999.999, "formatted_address": "🚀 这是Mock API返回的测试地址"},
    }

    # 模糊匹配地址
    address_lower = address.lower()

    # 直接匹配
    for key, data in address_database.items():
        if key in address or key.lower() in address_lower:
            return {
                "longitude": data["longitude"],
                "latitude": data["latitude"],
                "formatted_address": data["formatted_address"],
                "confidence": 0.9
            }

    # 关键词匹配
    keywords_mapping = {
        "机场": ["首都机场", "虹桥机场", "宝安机场", "白云机场"],
        "火车站": ["北京站", "上海站", "深圳站", "广州站"],
        "高铁站": ["北京南站", "上海虹桥站", "深圳北站", "广州南站"],
        "医院": ["协和医院", "301医院", "华山医院"],
        "大学": ["清华大学", "北京大学", "复旦大学", "深圳大学"]
    }

    for keyword, locations in keywords_mapping.items():
        if keyword in address:
            # 根据城市选择对应的地点
            if "北京" in address:
                location = next((loc for loc in locations if "北京" in address_database.get(loc, {}).get("formatted_address", "")), locations[0])
            elif "上海" in address:
                location = next((loc for loc in locations if "上海" in address_database.get(loc, {}).get("formatted_address", "")), locations[0])
            elif "深圳" in address:
                location = next((loc for loc in locations if "深圳" in address_database.get(loc, {}).get("formatted_address", "")), locations[0])
            elif "广州" in address:
                location = next((loc for loc in locations if "广州" in address_database.get(loc, {}).get("formatted_address", "")), locations[0])
            else:
                location = locations[0]

            if location in address_database:
                data = address_database[location]
                return {
                    "longitude": data["longitude"],
                    "latitude": data["latitude"],
                    "formatted_address": data["formatted_address"],
                    "confidence": 0.7
                }

    # 如果没有匹配到，返回None表示解析失败
    # 注释掉默认fallback，强制使用真实的百度API
    return None

if __name__ == '__main__':
    print("🚀 启动e代驾模拟API服务器...")
    print(f"📊 初始化完成：{len(MOCK_CUSTOMERS)}个客户，{len(MOCK_ORDERS)}个测试订单")
    print("🔗 调试接口：")
    print("   GET  /debug/orders - 查看订单数据")
    print("   POST /debug/reset  - 重置测试数据")
    app.run(host='0.0.0.0', port=5001, debug=True)