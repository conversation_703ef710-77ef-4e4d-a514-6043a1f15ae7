#!/usr/bin/env python3
"""
更新数据库中缺失的manager_name字段
"""

import sqlite3
import sys
from datetime import datetime

def update_missing_manager_names():
    """更新缺失的操作员姓名"""
    try:
        # 连接数据库
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        print("=== 数据库字段更新工具 ===")
        
        # 1. 检查当前状态
        cursor.execute("SELECT COUNT(*) FROM 'order'")
        total_orders = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM 'order' WHERE manager_name IS NULL OR manager_name = ''")
        empty_manager = cursor.fetchone()[0]
        
        print(f"总订单数: {total_orders}")
        print(f"缺少操作员姓名的订单: {empty_manager}")
        
        if empty_manager == 0:
            print("✅ 所有订单都有操作员姓名，无需更新")
            return
        
        # 2. 显示缺失数据的订单
        print("\n缺失操作员姓名的订单:")
        cursor.execute("SELECT edaijia_order_id, store_name, created_at FROM 'order' WHERE manager_name IS NULL OR manager_name = ''")
        missing_orders = cursor.fetchall()
        
        for order_id, store_name, created_at in missing_orders:
            print(f"  {order_id} | {store_name} | {created_at}")
        
        # 3. 更新数据
        print("\n开始更新数据...")
        
        # 根据店面名称设置对应的操作员
        store_manager_map = {
            '龙华店': '周鑫芸',
            '测试门店A': '测试店长',
            '测试门店B': '测试店长B',
            '康众汽配': '默认店长'
        }
        
        updated_count = 0
        for store_name, manager_name in store_manager_map.items():
            cursor.execute("""
                UPDATE 'order' 
                SET manager_name = ? 
                WHERE store_name = ? AND (manager_name IS NULL OR manager_name = '')
            """, (manager_name, store_name))
            
            affected_rows = cursor.rowcount
            if affected_rows > 0:
                print(f"  更新 {store_name} 的 {affected_rows} 个订单，设置操作员为: {manager_name}")
                updated_count += affected_rows
        
        # 对于没有匹配店面的订单，设置默认操作员
        cursor.execute("""
            UPDATE 'order' 
            SET manager_name = '系统管理员' 
            WHERE manager_name IS NULL OR manager_name = ''
        """)
        
        remaining_affected = cursor.rowcount
        if remaining_affected > 0:
            print(f"  为其余 {remaining_affected} 个订单设置默认操作员: 系统管理员")
            updated_count += remaining_affected
        
        # 提交更改
        conn.commit()
        
        # 4. 验证更新结果
        cursor.execute("SELECT COUNT(*) FROM 'order' WHERE manager_name IS NULL OR manager_name = ''")
        remaining_empty = cursor.fetchone()[0]
        
        print(f"\n✅ 更新完成!")
        print(f"总共更新了 {updated_count} 个订单")
        print(f"剩余缺失操作员姓名的订单: {remaining_empty}")
        
        # 5. 显示更新后的最新数据
        print("\n最新的5个订单数据:")
        cursor.execute("SELECT edaijia_order_id, manager_name, store_name, created_at FROM 'order' ORDER BY created_at DESC LIMIT 5")
        recent_orders = cursor.fetchall()
        
        for order_id, manager_name, store_name, created_at in recent_orders:
            print(f"  {order_id} | {manager_name} | {store_name} | {created_at}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    update_missing_manager_names()
