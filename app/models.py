from datetime import datetime, timezone
from app import db

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    f6_customer_id = db.Column(db.String(64), unique=True, nullable=False)
    name = db.Column(db.String(64), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    car_model = db.Column(db.String(64))
    plate_number = db.Column(db.String(20))
    home_address = db.Column(db.String(256))
    remarks = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    store_id = db.Column(db.Integer, db.ForeignKey('store.id', name='fk_customer_store_id'), nullable=False, default=1)
    orders = db.relationship('Order', back_populates='customer', lazy='dynamic')

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    edaijia_order_id = db.Column(db.String(120), index=True, unique=True)

    # 店面信息 - 简化为直接存储店名
    store_name = db.Column(db.String(100), nullable=False, index=True)

    # 客户信息（从F6传递，逐步替代customer_id）
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id', name='fk_order_customer_id'), nullable=True)  # 改为可空，兼容过渡
    customer_name = db.Column(db.String(64))
    customer_phone = db.Column(db.String(20), index=True)  # 添加索引便于搜索
    customer_f6_id = db.Column(db.String(64))

    # 车辆信息（从F6传递）
    car_model = db.Column(db.String(64))
    plate_number = db.Column(db.String(20))

    # 操作员信息
    manager_name = db.Column(db.String(64))
    manager_phone = db.Column(db.String(20))
    manager_employee_id = db.Column(db.String(64))  # F6员工ID

    # 订单基本信息
    pickup_address = db.Column(db.String(255))
    destination_address = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    price = db.Column(db.Float)
    driver_name = db.Column(db.String(100))
    driver_phone = db.Column(db.String(50))
    remarks = db.Column(db.Text)
    edj_booking_id = db.Column(db.String(120), index=True)
    order_time = db.Column(db.BigInteger)
    timeout = db.Column(db.Integer)
    edj_status_code = db.Column(db.String(20))
    edj_status_desc = db.Column(db.String(100))
    channel = db.Column(db.String(20))
    from_source = db.Column(db.String(32))
    dynamic_fee = db.Column(db.Float)
    bonus_sn = db.Column(db.String(64))

    # 支付相关字段
    payment_status = db.Column(db.String(20), default='unpaid')  # unpaid, paid, refunded
    pay_channel = db.Column(db.String(20))  # 支付渠道
    order_fee = db.Column(db.Float)  # 订单金额（元）
    pay_amount = db.Column(db.Integer)  # 实付金额（分）
    total_amount = db.Column(db.Integer)  # 订单总金额（分）
    third_trade_no = db.Column(db.String(64))  # 第三方支付流水号
    payment_time = db.Column(db.DateTime)  # 支付时间

    # 回调相关字段
    driver_id = db.Column(db.String(32))  # 司机工号
    driver_name = db.Column(db.String(64))  # 司机姓名
    driver_phone = db.Column(db.String(20))  # 司机电话
    call_time = db.Column(db.DateTime)  # 司机接单时间
    distance = db.Column(db.Float)  # 订单里程
    income = db.Column(db.Float)  # 订单总金额
    cancel_reason = db.Column(db.String(200))  # 取消原因
    third_order_id = db.Column(db.String(64))  # 第三方订单号

    # F6工单相关字段
    f6_workorder_id = db.Column(db.String(64))  # F6工单ID
    f6_workorder_version = db.Column(db.Integer)  # F6工单版本号
    f6_customer_id = db.Column(db.String(64))  # F6客户ID
    f6_car_id = db.Column(db.String(64))  # F6车辆ID

    customer = db.relationship('Customer', back_populates='orders')

    # e代驾官方状态码显示映射
    EDJ_STATUS_DISPLAY_MAP = {
        '102': '开始系统派单',
        '180': '系统派单中',
        '301': '司机接单',
        '302': '司机就位',
        '303': '司机开车',
        '304': '代驾结束',
        '403': '客户取消',
        '404': '司机取消',
        '501': '司机报单',
        '506': '系统派单失败'
    }

    def get_status_display(self):
        """获取e代驾状态码的显示名称"""
        return self.EDJ_STATUS_DISPLAY_MAP.get(self.edj_status_code, f'未知状态({self.edj_status_code})')

    @staticmethod
    def get_status_display_by_code(status_code):
        """根据状态码获取显示名称（静态方法）"""
        EDJ_STATUS_DISPLAY_MAP = {
            '101': '开始下单',
            '102': '开始系统派单',
            '180': '系统派单中',
            '301': '司机接单',
            '302': '司机就位',
            '303': '司机开车',
            '304': '代驾结束',
            '403': '客户取消',
            '404': '司机取消',
            '501': '司机报单',
            '506': '系统派单失败'
        }
        return EDJ_STATUS_DISPLAY_MAP.get(status_code, f'未知状态({status_code})')

    def __repr__(self):
        return f'<Order {self.edaijia_order_id}>'

class APIStatus(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    service_name = db.Column(db.String(64), unique=True, nullable=False)  # 'f6' or 'edaijia'
    status = db.Column(db.String(20), default='normal')  # normal or error
    last_check = db.Column(db.DateTime, default=datetime.utcnow)
    session_expires_at = db.Column(db.DateTime)
    error_message = db.Column(db.Text)

class Store(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    f6_org_id = db.Column(db.String(64), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    short_name = db.Column(db.String(32))
    api_key = db.Column(db.String(64), nullable=False)
    api_secret = db.Column(db.String(64), nullable=False)
    province = db.Column(db.String(32))
    city = db.Column(db.String(32))
    area = db.Column(db.String(32))
    detail_address = db.Column(db.String(256))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)