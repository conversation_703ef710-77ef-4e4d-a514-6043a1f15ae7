from flask import jsonify, request, current_app, session, render_template
from app.api import bp
from app.models import Customer, Order, APIStatus, Store
from app import db
from datetime import datetime, timedelta, timezone
import requests
import hashlib
import json
import time
from sqlalchemy import or_
import os
# from app.services.edaijia_api import get_edaijia_api  # 暂时不需要

# e代驾官方终止状态码定义（根据官方文档）
# 这些状态表示订单已经结束，不会再有状态变化
EDJ_TERMINATED_STATUSES = ['304', '403', '404', '506']
# 304: 代驾结束, 403: 客户取消, 404: 司机取消, 506: 派单失败

def decode_store_name(store_name):
    """
    统一的店面名称解码函数 - 生产环境专用

    Args:
        store_name: 可能被URL编码的店面名称

    Returns:
        str: 正确解码的店面名称
    """
    if not store_name:
        return store_name

    try:
        import urllib.parse
        # 尝试URL解码
        decoded_name = urllib.parse.unquote(store_name, encoding='utf-8')

        # 如果解码后不同，说明原来是编码的
        if decoded_name != store_name:
            current_app.logger.debug(f"店面名称解码: {store_name} -> {decoded_name}")
            return decoded_name

        # 如果相同，直接返回原值
        return store_name

    except Exception as e:
        current_app.logger.warning(f"店面名称解码失败: {e}, 使用原始值: {store_name}")
        return store_name

# e代驾订单状态映射
EDJ_STATUS_MAP = {
    '101': 'pending',      # 开始下单
    '102': 'pending',      # 开始系统派单
    '180': 'pending',      # 系统派单中
    '301': 'in_progress',  # 司机接单
    '302': 'in_progress',  # 司机就位
    '303': 'in_progress',  # 司机开车
    '304': 'completed',    # 代驾结束
    '501': 'completed',    # 司机报单
    '601': 'cancelled',    # 订单取消
    '701': 'failed',       # 未派到司机取消
}

def calculate_edaijia_signature(params, secret):
    """
    e代驾API签名计算函数

    Args:
        params: 所有参数字典（不包含sig）
        secret: API密钥

    Returns:
        签名字符串（前30位）
    """
    # 移除sig参数（如果存在）
    params_copy = {k: v for k, v in params.items() if k != 'sig'}
    # 按key排序
    sorted_items = sorted(params_copy.items())
    # 拼接参数字符串
    param_str = ''.join(f"{k}{v}" for k, v in sorted_items)
    # 前后加secret
    sign_str = f"{secret}{param_str}{secret}"
    # MD5加密并取前30位
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest()[:30]

def _update_api_status_on_success(api_status, token_value, token_expiry_timestamp=None, relative_expiry_seconds=None):
    api_status.status = 'normal'
    api_status.last_check = datetime.now(timezone.utc)
    if token_expiry_timestamp:
        # Ensure we are creating timezone-aware datetime objects
        api_status.session_expires_at = datetime.fromtimestamp(token_expiry_timestamp, timezone.utc)
    elif relative_expiry_seconds:
        api_status.session_expires_at = datetime.now(timezone.utc) + timedelta(seconds=relative_expiry_seconds)
    else:
        api_status.session_expires_at = datetime.now(timezone.utc) + timedelta(hours=2) # Default assumption
    api_status.error_message = None
    # Store token in session, not DB for security
    session[f'{api_status.service_name}_api_token'] = token_value
    session[f'{api_status.service_name}_api_token_expires_at'] = api_status.session_expires_at.timestamp()
    current_app.logger.info(f"Updated API status for {api_status.service_name} to normal. Token expires at {api_status.session_expires_at}")
    db.session.commit()

def _update_api_status_on_failure(api_status, error_message):
    api_status.status = 'error'
    api_status.last_check = datetime.now(timezone.utc)
    api_status.error_message = error_message
    session.pop(f'{api_status.service_name}_api_token', None)
    session.pop(f'{api_status.service_name}_api_token_expires_at', None)
    current_app.logger.error(f"Updated API status for {api_status.service_name} to error: {error_message}")
    db.session.commit()

def _get_active_token_from_session(service_name):
    token = session.get(f'{service_name}_api_token')
    expires_at_ts = session.get(f'{service_name}_api_token_expires_at')
    if token and expires_at_ts:
        if datetime.now(timezone.utc).timestamp() < expires_at_ts:
            current_app.logger.debug(f"Active token found in session for {service_name}")
            return token
        else:
            current_app.logger.info(f"Token for {service_name} found in session but expired.")
            session.pop(f'{service_name}_api_token', None)
            session.pop(f'{service_name}_api_token_expires_at', None)
    return None

def get_f6_token(force_refresh=False):
    service_name = 'f6'
    api_status = APIStatus.query.filter_by(service_name=service_name).first()
    if not api_status:
        current_app.logger.info(f"No APIStatus record for {service_name}, creating one.")
        api_status = APIStatus(service_name=service_name, status='unknown', last_check=datetime.now(timezone.utc))
        db.session.add(api_status)
        db.session.commit()

    if not force_refresh:
        active_token = _get_active_token_from_session(service_name)
        if active_token:
            return active_token

    current_app.logger.info(f"Attempting to fetch new F6 token (force_refresh={force_refresh}).")
    try:
        timestamp = str(int(time.time()))
        app_key = current_app.config.get('F6_API_KEY')
        app_secret = current_app.config.get('F6_API_SECRET')
        if not app_key or not app_secret:
            err_msg = "F6 API Key or Secret not configured in .env file."
            current_app.logger.error(err_msg)
            _update_api_status_on_failure(api_status, err_msg)
            return None

        sign_str = f"{app_key}{timestamp}{app_secret}"
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        api_url = f"{current_app.config.get('F6_API_BASE_URL', 'https://api.f6car.com')}/api/v1/auth/token"
        current_app.logger.debug(f"Requesting F6 token from {api_url}")
        response = requests.post(
            api_url,
            json={'appKey': app_key, 'timestamp': timestamp, 'sign': sign},
            timeout=10
        )
        response.raise_for_status()
        data = response.json()
        current_app.logger.debug(f"F6 token response: {data}")
        if str(data.get('code')) == '0' and data.get('data', {}).get('token'):
            token_val = data['data']['token']
            expires_at = data['data'].get('expires_at') # This is an absolute Unix timestamp
            if expires_at is None:
                 _update_api_status_on_failure(api_status, "F6 token API response missing 'expires_at'.")
                 return None
            _update_api_status_on_success(api_status, token_val, token_expiry_timestamp=int(expires_at))
            current_app.logger.info(f"Successfully fetched new F6 token, expires at timestamp {expires_at}.")
            return token_val
        else:
            error_msg = data.get('msg', 'F6 token API did not return success code=0 or token.')
            _update_api_status_on_failure(api_status, error_msg)
            current_app.logger.error(f"Failed to get F6 token: {error_msg}")
            return None
    except requests.exceptions.Timeout:
        _update_api_status_on_failure(api_status, "F6 API request timed out.")
        current_app.logger.error("F6 API request timed out.")
        return None
    except requests.exceptions.RequestException as e:
        _update_api_status_on_failure(api_status, f"F6 API request failed: {str(e)}")
        current_app.logger.error(f"F6 API request exception: {str(e)}")
        return None
    except Exception as e:
        _update_api_status_on_failure(api_status, f"F6 token retrieval error: {str(e)}")
        current_app.logger.error(f"Generic F6 token retrieval error: {str(e)}", exc_info=True)
        return None

def get_edaijia_token(force_refresh=False):
    service_name = 'edaijia'
    api_status = APIStatus.query.filter_by(service_name=service_name).first()
    if not api_status:
        current_app.logger.info(f"No APIStatus record for {service_name}, creating one.")
        api_status = APIStatus(service_name=service_name, status='unknown', last_check=datetime.now(timezone.utc))
        db.session.add(api_status)
        db.session.commit()

    if not force_refresh:
        active_token = _get_active_token_from_session(service_name)
        if active_token:
            return active_token

    current_app.logger.info(f"Attempting to fetch new eDaijia token (force_refresh={force_refresh}).")
    try:
        app_key = current_app.config.get('EDAIJIA_API_KEY')
        app_secret = current_app.config.get('EDAIJIA_API_SECRET')
        if not app_key or not app_secret:
            err_msg = "eDaijia API Key or Secret not configured in .env file."
            current_app.logger.error(err_msg)
            _update_api_status_on_failure(api_status, err_msg)
            return None

        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        # 构造所有系统参数（全部小写）
        sign_params = {
            'appkey': app_key,
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345'
        }
        # 签名算法：参数名排序，拼接，前后加secret，md5取前30位
        items = sorted(sign_params.items())
        s = ''.join(f"{k}{v}" for k, v in items)
        s = f"{app_secret}{s}{app_secret}"
        sign = hashlib.md5(s.encode('utf-8')).hexdigest()[:30]
        payload = dict(sign_params)
        payload['sig'] = sign
        api_url = f"{current_app.config.get('EDAIJIA_API_BASE_URL', 'http://openapi.d.edaijia.cn')}/openApi/accessToken"
        current_app.logger.debug(f"Requesting eDaijia token from {api_url} with payload {payload}")
        response = requests.post(api_url, data=payload, timeout=10)
        response.raise_for_status()
        data = response.json()
        current_app.logger.debug(f"eDaijia token response: {data}")
        if str(data.get('code')) == '0' and data.get('data', {}).get('accessToken'):
            token_val = data['data']['accessToken']
            expires_in = data['data'].get('expiresIn', 7200) # seconds
            _update_api_status_on_success(api_status, token_val, relative_expiry_seconds=int(expires_in))
            current_app.logger.info(f"Successfully fetched new eDaijia token, expires in {expires_in} seconds.")
            return token_val
        else:
            error_msg = data.get('message', 'eDaijia token API did not return success code=0 or token.')
            _update_api_status_on_failure(api_status, error_msg)
            current_app.logger.error(f"Failed to get eDaijia token: {error_msg}")
            return None
    except requests.exceptions.Timeout:
        _update_api_status_on_failure(api_status, "eDaijia API request timed out.")
        current_app.logger.error("eDaijia API request timed out.")
        return None
    except requests.exceptions.RequestException as e:
        _update_api_status_on_failure(api_status, f"eDaijia API request failed: {str(e)}")
        current_app.logger.error(f"eDaijia API request exception: {str(e)}")
        return None
    except Exception as e:
        _update_api_status_on_failure(api_status, f"eDaijia token retrieval error: {str(e)}")
        current_app.logger.error(f"Generic eDaijia token retrieval error: {str(e)}", exc_info=True)
        return None

@bp.route('/status', methods=['GET'])
def get_all_api_statuses():
    statuses = APIStatus.query.all()
    status_dict = {}
    default_tz = timezone.utc # Or use app.config['DEFAULT_TIMEZONE'] if set

    for s in statuses:
        status_dict[s.service_name] = {
            'status': s.status,
            'last_check': s.last_check.astimezone(default_tz).strftime('%Y-%m-%d %H:%M:%S %Z') if s.last_check else 'N/A',
            'error_message': s.error_message,
            'session_expires_at': s.session_expires_at.astimezone(default_tz).strftime('%Y-%m-%d %H:%M:%S %Z') if s.session_expires_at else None
        }
    # 只保留e代驾服务监控，移除F6服务监控
    for service_name in ['edaijia']:
        if service_name not in status_dict:
             current_app.logger.warning(f"APIStatus record for {service_name} missing in DB, creating temporary unknown status for API response.")
             status_dict[service_name] = {'status': 'unknown', 'last_check': 'N/A', 'error_message': 'Not initialized in DB', 'session_expires_at': None}
        # Optionally, add info if token is active in session (for debugging/confirmation)
        # active_session_token = _get_active_token_from_session(service_name)
        # status_dict[service_name]['token_in_session'] = bool(active_session_token)

    return jsonify(status_dict)

@bp.route('/reconnect/<service_name>', methods=['POST'])
def reconnect_api(service_name):
    token_value = None
    current_app.logger.info(f"Reconnect requested for {service_name}")
    if service_name == 'edaijia':
        token_value = get_edaijia_token(force_refresh=True)
    else:
        current_app.logger.warning(f"Invalid service name for reconnect: {service_name}")
        return jsonify({'success': False, 'message': '不支持的服务，仅支持edaijia'}), 400

    api_status = APIStatus.query.filter_by(service_name=service_name).first()
    # Ensure api_status exists (should be created by get_xxx_token if not)
    if not api_status:
        current_app.logger.error(f"APIStatus record for {service_name} still not found after reconnect attempt.")
        return jsonify({'success': False, 'message': f'无法找到服务 {service_name} 的状态记录。'}), 500

    if token_value and isinstance(token_value, str) and api_status.status == 'normal':
        current_app.logger.info(f"Reconnect successful for {service_name}")
        return jsonify({
            'success': True,
            'message': f'{service_name.upper()} API 重新连接成功。',
            'status': api_status.status,
            'last_check': api_status.last_check.isoformat() if api_status.last_check else None,
            'error_message': api_status.error_message,
            'session_expires_at': api_status.session_expires_at.isoformat() if api_status.session_expires_at else None
        })
    else:
        err_msg_detail = api_status.error_message if api_status.error_message else "请查看服务器日志获取详情。"
        current_app.logger.error(f"Reconnect failed for {service_name}. API status: {api_status.status}, Error: {err_msg_detail}")
        return jsonify({
            'success': False,
            'message': f'{service_name.upper()} API 重新连接失败。错误: {err_msg_detail}',
            'status': api_status.status,
            'last_check': api_status.last_check.isoformat() if api_status.last_check else None,
            'error_message': api_status.error_message,
            'session_expires_at': api_status.session_expires_at.isoformat() if api_status.session_expires_at else None
        }), 500

def f6_calc_signature(app_key, app_secret, body, method='POST', uri='', headers=None):
    """
    F6 API签名算法 - 基于阿里云API网关摘要签名

    Args:
        app_key: 应用Key
        app_secret: 应用Secret
        body: 请求体
        method: HTTP方法
        uri: 请求URI
        headers: 请求头

    Returns:
        签名字符串
    """
    import base64
    import hmac
    from urllib.parse import quote

    if headers is None:
        headers = {}

    # 1. 构造待签名字符串
    # HTTP_METHOD + "\n" + Accept + "\n" + Content-MD5 + "\n" + Content-Type + "\n" + Date + "\n" + Headers + URI

    # HTTP方法
    string_to_sign = method.upper() + '\n'

    # Accept
    accept = headers.get('Accept', '')
    string_to_sign += accept + '\n'

    # Content-MD5
    content_md5 = ''
    if body:
        import hashlib
        md5_hash = hashlib.md5(body.encode('utf-8')).digest()
        content_md5 = base64.b64encode(md5_hash).decode('utf-8')
    string_to_sign += content_md5 + '\n'

    # Content-Type
    content_type = headers.get('Content-Type', 'application/json')
    string_to_sign += content_type + '\n'

    # Date
    date = headers.get('Date', '')
    string_to_sign += date + '\n'

    # 自定义Headers (X-Ca-开头的header，按字典序排序)
    ca_headers = []
    for key, value in headers.items():
        if key.lower().startswith('x-ca-'):
            ca_headers.append(f"{key.lower()}:{value}")

    if ca_headers:
        ca_headers.sort()
        string_to_sign += '\n'.join(ca_headers) + '\n'

    # URI
    string_to_sign += uri

    # 2. 使用HMAC-SHA256计算签名
    signature = hmac.new(
        app_secret.encode('utf-8'),
        string_to_sign.encode('utf-8'),
        hashlib.sha256
    ).digest()

    # 3. Base64编码
    return base64.b64encode(signature).decode('utf-8')

# ===== F6 API 相关函数已注释 =====
# 注释原因：根据最新方案，不需要连接F6的API了
def sync_f6_customers_core():
    """
    从 mock server 获取 F6 客户数据并写入本地数据库（仅当前门店，store_id隔离）。
    ===== 已注释：不再使用F6 API =====
    """
    # from flask import current_app, session
    # # 1. 获取当前门店
    # store_id = session.get('current_store_id')
    # if not store_id:
    #     return False, "未选择门店，无法同步客户。"
    # store = Store.query.get(store_id)
    # if not store:
    #     return False, "门店不存在，无法同步客户。"
    # app_key = store.api_key
    # app_secret = store.api_secret
    # base_url = current_app.config.get('F6_API_BASE_URL', 'http://127.0.0.1:5001')
    # url = base_url.rstrip('/') + '/customer/list'
    # uri = '/customer/list'
    # body_dict = {"paramValues": [{"pageSize": 100, "currentPage": 1}]}
    # body = json.dumps(body_dict, ensure_ascii=False)

    # # 构造请求头
    # headers = {
    #     "Content-Type": "application/json",
    #     "X-Ca-Key": app_key,
    # }

    # # 计算签名
    # signature = f6_calc_signature(app_key, app_secret, body, method='POST', uri=uri, headers=headers)
    # headers["X-Ca-Signature"] = signature
    # try:
    #     resp = requests.post(url, data=body, headers=headers, timeout=5)
    #     resp.raise_for_status()
    #     resp_json = resp.json()
    #     if resp_json.get('code') != 200:
    #         raise Exception(resp_json.get('message', 'F6接口返回异常'))
    #     f6_customers = resp_json['data']['list']
    # except Exception as e:
    #     current_app.logger.error(f"Mock F6 API请求失败: {e}")
    #     return False, f"无法从Mock F6 API获取客户数据: {e}"

    # 直接返回成功，不再同步F6客户数据
    return True, "F6客户同步已禁用"

    # count_synced = 0
    # try:
    #     # 先清空当前门店下的客户
    #     Customer.query.filter_by(store_id=store_id).delete()
    #     db.session.commit()
    #     for customer_data in f6_customers:
    #         customer = Customer(f6_customer_id=customer_data['id'], store_id=store_id)
    #         customer.name = customer_data['name']
    #         customer.phone = customer_data['phone']
    #         customer.car_model = customer_data.get('car_model')
    #         customer.plate_number = customer_data.get('plate_number')
    #         customer.home_address = customer_data.get('address')
    #         customer.remarks = (customer_data.get('remarks', "") or "").strip()
    #         db.session.add(customer)
    #         count_synced += 1
    #     db.session.commit()
    #     current_app.logger.info(f"成功同步 {count_synced} 位F6客户（门店ID={store_id}）。")
    #     return True, f'成功同步 {count_synced} 位F6客户。'
    # except Exception as e:
    #     current_app.logger.error(f"同步F6客户时发生错误: {str(e)}", exc_info=True)
    #     db.session.rollback()
    #     return False, f"同步F6客户时发生错误: {str(e)}"

# ===== F6 API路由已注释 =====
# 注释原因：根据最新方案，不需要连接F6的API了
# API路由调用核心逻辑
@bp.route('/sync_f6_customers', methods=['POST'])
def sync_f6_customers():
    """
    同步F6客户数据到本地数据库
    ===== 已注释：不再使用F6 API =====
    """
    success, msg = sync_f6_customers_core()
    # if success:
    #     # 判断是否为AJAX请求，返回客户表格HTML片段
    #     if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
    #         # 获取最新客户数据
    #         customers = Customer.query.order_by(Customer.id.desc()).limit(20).all()
    #         html = render_template('customers_table.html', customers=customers)
    #         return jsonify({'message': msg, 'table_html': html})
    #     return jsonify({'message': msg})
    # else:
    #     return jsonify({'message': msg}), 500
    return jsonify({'success': success, 'message': msg})

def get_manager_info_by_name(manager_name):
    """
    根据店长姓名获取完整的店长信息

    生产环境安全增强：
    - 验证配置完整性
    - 记录访问日志
    - 检测异常访问模式

    Args:
        manager_name: 店长姓名

    Returns:
        包含店面信息、经纬度的字典，或None（不包含手机号）
    """
    try:
        import json

        # 安全检查：记录管理员信息访问
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        current_app.logger.debug(f"Manager info access: manager='{manager_name}' from IP {client_ip}")

        manager_info_map = json.loads(current_app.config.get('MANAGER_INFO_MAP', '{}'))

        # 验证配置完整性
        if not manager_info_map:
            current_app.logger.error("SECURITY: MANAGER_INFO_MAP is empty or not configured")
            return None

        manager_info = manager_info_map.get(manager_name)

        if manager_info:
            # 验证必要字段
            required_fields = ['store_name', 'store_address', 'longitude', 'latitude']
            missing_fields = [field for field in required_fields if field not in manager_info]

            if missing_fields:
                current_app.logger.error(f"SECURITY: Manager '{manager_name}' config missing fields: {missing_fields}")
                return None

            current_app.logger.debug(f"Manager info found for '{manager_name}': store='{manager_info['store_name']}'")
        else:
            current_app.logger.warning(f"SECURITY: Manager '{manager_name}' not found in configuration from IP {client_ip}")

        return manager_info

    except json.JSONDecodeError as e:
        current_app.logger.error(f"SECURITY: Invalid JSON in MANAGER_INFO_MAP: {str(e)}")
        return None
    except Exception as e:
        current_app.logger.error(f"Error parsing manager info map: {str(e)}")
        return None

def get_manager_phone_by_name(manager_name):
    """
    根据店长姓名获取手机号（兼容性函数）

    Args:
        manager_name: 店长姓名

    Returns:
        手机号或None
    """
    manager_info = get_manager_info_by_name(manager_name)
    return manager_info.get('phone') if manager_info else None

def fuzzy_match_store_name(frontend_store_name, expected_store_name):
    """
    模糊匹配店面名称

    生产环境安全增强：
    - 记录所有匹配尝试
    - 检测可疑的店名匹配模式
    - 提供详细的匹配日志

    Args:
        frontend_store_name: 前端传来的店名
        expected_store_name: 期望的店名

    Returns:
        tuple: (是否匹配, 匹配信息)
    """
    # 安全日志：记录店名匹配尝试
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    current_app.logger.debug(f"Store name matching attempt: frontend='{frontend_store_name}' vs expected='{expected_store_name}' from IP {client_ip}")

    if not frontend_store_name or not expected_store_name:
        current_app.logger.warning(f"SECURITY: Empty store name in matching attempt from IP {client_ip}")
        return False, "店名为空"

    # 标准化处理
    frontend_name = frontend_store_name.strip()
    expected_name = expected_store_name.strip()

    # 1. 完全匹配
    if frontend_name == expected_name:
        current_app.logger.info(f"Store name exact match: '{frontend_name}' from IP {client_ip}")
        return True, "完全匹配"

    # 2. 忽略大小写匹配
    if frontend_name.lower() == expected_name.lower():
        current_app.logger.info(f"Store name case-insensitive match: '{frontend_name}' from IP {client_ip}")
        return True, "忽略大小写匹配"

    # 3. 提取核心店名进行匹配
    def extract_core_name(store_name):
        """提取店名的核心部分"""
        # 移除常见前缀和后缀
        prefixes = ['您身边的']
        suffixes = ['有限公司', '公司', '店', '分店', '门店', '汽配']

        core_name = store_name
        for prefix in prefixes:
            if core_name.startswith(prefix):
                core_name = core_name[len(prefix):].strip()

        for suffix in suffixes:
            if core_name.endswith(suffix):
                core_name = core_name[:-len(suffix)].strip()

        return core_name

    frontend_core = extract_core_name(frontend_name)
    expected_core = extract_core_name(expected_name)

    # 4. 核心名称匹配
    if frontend_core and expected_core and frontend_core == expected_core:
        current_app.logger.info(f"Store name core match: '{frontend_core}' from IP {client_ip}")
        return True, f"核心名称匹配: {frontend_core}"

    # 5. 包含关系匹配
    if frontend_core in expected_name or expected_core in frontend_name:
        current_app.logger.info(f"Store name partial match: '{frontend_core}' <-> '{expected_core}' from IP {client_ip}")
        return True, f"包含关系匹配: {frontend_core} <-> {expected_core}"

    # 6. 关键词匹配
    def extract_keywords(store_name):
        """提取店名中的关键词"""
        # 移除常见词汇，提取地区或特色词汇
        common_words = ['康众', '汽配', '有限公司', '公司', '店', '分店', '门店', '您身边的']
        words = []
        temp_name = store_name

        for word in common_words:
            temp_name = temp_name.replace(word, ' ')

        # 提取剩余的有意义词汇
        import re
        keywords = re.findall(r'[\u4e00-\u9fa5]{2,}', temp_name)
        return [kw for kw in keywords if len(kw) >= 2]

    frontend_keywords = extract_keywords(frontend_name)
    expected_keywords = extract_keywords(expected_name)

    # 检查是否有共同关键词
    common_keywords = set(frontend_keywords) & set(expected_keywords)
    if common_keywords:
        return True, f"关键词匹配: {list(common_keywords)}"

    # 7. 地区匹配（针对同一地区的不同表述）
    region_aliases = {
        '福田': ['福田区', '福田店', '福田分店'],
        '南山': ['南山区', '南山店', '南山分店'],
        '罗湖': ['罗湖区', '罗湖店', '罗湖分店'],
        '宝安': ['宝安区', '宝安店', '宝安分店'],
        '龙岗': ['龙岗区', '龙岗店', '龙岗分店'],
        '龙华': ['龙华区', '龙华店', '龙华分店'],
        '坪山': ['坪山区', '坪山店', '坪山分店'],
        '光明': ['光明区', '光明店', '光明分店']
    }

    for region, aliases in region_aliases.items():
        frontend_has_region = any(alias in frontend_name for alias in aliases) or region in frontend_name
        expected_has_region = any(alias in expected_name for alias in aliases) or region in expected_name

        if frontend_has_region and expected_has_region:
            return True, f"地区匹配: {region}"

    # 8. 模糊相似度匹配（简单版本）
    def simple_similarity(s1, s2):
        """计算简单的字符相似度"""
        if not s1 or not s2:
            return 0

        # 计算共同字符数
        common_chars = 0
        s1_chars = list(s1)
        s2_chars = list(s2)

        for char in s1_chars:
            if char in s2_chars:
                common_chars += 1
                s2_chars.remove(char)  # 避免重复计算

        # 相似度 = 共同字符数 / 较长字符串长度
        similarity = common_chars / max(len(s1), len(s2))
        return similarity

    similarity = simple_similarity(frontend_core, expected_core)
    if similarity >= 0.6:  # 60%以上相似度认为匹配
        return True, f"相似度匹配: {similarity:.2f}"

    # 所有匹配方式都失败
    return False, f"无匹配: '{frontend_name}' vs '{expected_name}'"

def authenticate_manager_by_name(manager_name):
    """
    通过店长姓名进行简化认证（仅验证是否在配置中存在）

    生产环境安全增强：
    - 记录详细的认证日志
    - 检测异常认证模式
    - 限制认证频率

    Args:
        manager_name: 店长姓名

    Returns:
        认证信息字典或None
    """
    try:
        # 安全检查：记录认证尝试
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')

        current_app.logger.info(f"Authentication attempt: manager='{manager_name}', ip={client_ip}, user_agent={user_agent[:100]}")

        # 1. 验证店长是否在配置中存在
        manager_info = get_manager_info_by_name(manager_name)
        if not manager_info:
            current_app.logger.warning(f"SECURITY: Unauthorized manager name '{manager_name}' attempted access from IP {client_ip}")
            return None

        # 2. 根据店名查找或创建对应的Store记录
        store_name = manager_info['store_name']
        store = Store.query.filter_by(name=store_name).first()

        if not store:
            # 如果店面不存在，创建一个默认的店面记录
            store = Store(
                f6_org_id=f'auto_{hash(store_name) % 10000}',
                name=store_name,
                short_name=store_name.replace('店', ''),
                api_key='config_based',
                api_secret='config_based',
                province='未知',
                city='未知',
                area='未知',
                detail_address=manager_info.get('store_address', '未知地址')
            )
            db.session.add(store)
            db.session.commit()
            current_app.logger.info(f"Created new store record for '{store_name}' with ID {store.id}")

        # 3. 创建简化的认证信息（包含store_id和安全信息）
        auth_info = {
            'name': manager_name,
            'store_id': store.id,  # 添加store_id字段
            'store_name': manager_info['store_name'],
            'store_address': manager_info['store_address'],
            'longitude': manager_info['longitude'],
            'latitude': manager_info['latitude'],
            'role': '店长',  # 配置中的都认为是店长
            'employee_id': f'manager_{hash(manager_name) % 10000}',  # 生成一个简单的ID
            'authenticated': True,
            'auth_time': datetime.now().isoformat(),  # 认证时间
            'client_ip': client_ip,  # 客户端IP
            'auth_method': 'config_based'  # 认证方式
        }

        # 保存认证信息到session
        session['authenticated_manager'] = auth_info

        current_app.logger.info(f"SECURITY: Manager '{manager_name}' authenticated successfully for store '{store_name}' (ID: {store.id}) from IP {client_ip}")
        return auth_info

    except Exception as e:
        current_app.logger.error(f"SECURITY: Authentication error for manager '{manager_name}': {str(e)}", exc_info=True)
        return None

@bp.route('/create_edaijia_order/<int:customer_id>', methods=['POST'])
def create_edaijia_order(customer_id):
    if customer_id == 0:
        current_app.logger.warning("Attempted to create eDaijia order with invalid customer_id=0")
        return jsonify({'message': '无效的客户ID，请从客户列表选择客户后创建订单。'}), 400

    token = get_edaijia_token()
    if not token:
        api_status = APIStatus.query.filter_by(service_name='edaijia').first()
        error_detail = api_status.error_message if api_status and api_status.error_message else "无法获取e代驾 API Token。请尝试在首页重新连接e代驾 API。"
        current_app.logger.warning(f"eDaijia order creation failed: {error_detail}")
        return jsonify({'message': f'e代驾 API 认证失败: {error_detail}'}), 500

    customer = Customer.query.get_or_404(customer_id)

    # 获取表单数据
    order_type = request.form.get('order_type', 'now')  # 默认为"代叫"模式
    pickup_address = request.form['pickup_address']
    destination_address = request.form['destination_address']
    payment_method = request.form.get('payment_method', 'self')  # 默认为"我来支付"
    remarks = request.form.get('remarks', '')
    manager_name = request.form.get('manager_name', '')  # 浏览器插件传入的店长姓名

    # 如果提供了店长姓名，尝试自动认证
    if manager_name:
        auth_info = authenticate_manager_by_name(manager_name)
        if auth_info:
            current_app.logger.info(f"Order created by authenticated manager: {manager_name}")
            # 可以在订单备注中记录操作员信息
            remarks += f"\n[操作员] {manager_name} ({auth_info['role']})"
        else:
            current_app.logger.warning(f"Failed to authenticate manager: {manager_name}")
            # 不阻止订单创建，但记录警告

    # 根据订单类型获取特定字段
    contact_phone = None
    reserve_time = None

    if order_type == 'now':
        contact_phone = request.form.get('contact_phone', '')
    else:  # reserve
        reserve_time = request.form.get('reserve_time', '')

    # 调用mock e代驾下单接口
    edj_api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/order/commit'

    # 构造系统参数
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    sys_params = {
        'appkey': current_app.config.get('EDAIJIA_API_KEY'),
        'timestamp': timestamp,
        'ver': '3.4.3',
        'from': '01012345'
    }

    # 业务参数
    business_params = {
        'phone': customer.phone,  # 用户本人手机号
        'token': token,
        'start_address': pickup_address,
        'end_address': destination_address,
        'third_order_id': f"THIRD-{int(time.time())}-{customer.id}",
        'is_use_bonus': 0,
        # 添加必填的经纬度参数（模拟数据）
        'start_longitude': 116.4808,  # 模拟北京望京SOHO位置
        'start_latitude': 39.9947,
        'end_longitude': 116.3912,    # 模拟北京西单位置
        'end_latitude': 39.9085
    }

    # 代叫订单处理
    if order_type == 'now' and contact_phone:
        business_params['contact_phone'] = contact_phone  # API定义中的代叫参数
        # 支付方式：0本人支付，1乘客现金支付
        business_params['cash_only'] = 0 if payment_method == 'self' else 1

    # 预约订单处理 - 注意：e代驾API文档中没有明确的预约参数，这里保留原逻辑
    # 实际使用时需要确认e代驾是否支持预约功能及正确的参数名
    if order_type == 'reserve' and reserve_time:
        # 可能的预约参数，需要与e代驾确认
        business_params['appointment_time'] = reserve_time
        business_params['is_appointment'] = 1
    else:
        business_params['is_appointment'] = 0

    # 合并所有参数用于签名计算
    all_params = {**sys_params, **business_params}

    # 计算签名 - 所有参数都参与签名
    sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

    # 最终payload包含所有参数和签名
    payload = all_params.copy()
    payload['sig'] = sig

    try:
        current_app.logger.info(f"Sending order request to eDaijia API: {payload}")
        resp = requests.post(edj_api_url, data=payload, timeout=5)
        resp_json = resp.json()

        if resp_json.get('code') != '0' and resp_json.get('code') != 0:
            raise Exception(resp_json.get('message', 'e代驾下单失败'))

        edj_data = resp_json['data']

        # 创建订单记录
        order = Order(
            edaijia_order_id=edj_data.get('edj_order_id'),
            edj_booking_id=edj_data.get('edj_booking_id'),
            order_time=edj_data.get('order_time'),
            timeout=edj_data.get('timeout'),
            customer_id=customer.id,
            status='pending',
            pickup_address=pickup_address,
            destination_address=destination_address,
            remarks=remarks
        )

        # 添加订单类型和其他新字段到备注中
        order_type_text = "预约" if order_type == 'reserve' else "代叫"
        order_details = f"订单类型: {order_type_text}\n"

        if contact_phone:
            order_details += f"联系人手机号: {contact_phone}\n"

        if reserve_time:
            order_details += f"预约时间: {reserve_time}\n"

        order_details += f"支付方式: {'我来支付' if payment_method == 'self' else '其他方式'}\n"

        if remarks:
            order_details += f"备注: {remarks}\n"

        order.remarks = order_details

        db.session.add(order)

        # Update customer remarks - append, don't overwrite
        current_remarks = customer.remarks if customer.remarks else ""
        customer.remarks = current_remarks + f"\n[系统] e代驾{order_type_text}订单 ({order.edaijia_order_id}) 已创建待处理 - {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S %Z')}"

        db.session.commit()
        current_app.logger.info(f"eDaijia {order_type_text} order {order.edaijia_order_id} (mocked) created for customer {customer.id}")

        return jsonify({
            'message': f'e代驾{order_type_text}订单创建请求已模拟发送成功。',
            'orderId': order.edaijia_order_id
        })
    except Exception as e:
        current_app.logger.error(f"Error creating simulated eDaijia order for customer {customer.id}: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({'message': f"创建模拟订单时发生内部错误: {str(e)}"}), 500

# e代驾官方状态码与本地状态映射
EDJ_STATUS_MAP = {
    '102': 'pending',           # 开始系统派单
    '180': 'pending',           # 系统派单中
    '301': 'in_progress',       # 司机接单
    '302': 'in_progress',       # 司机就位
    '303': 'in_progress',       # 司机开车
    '304': 'completed',         # 代驾结束
    '403': 'cancelled_customer', # 客户取消
    '404': 'cancelled_driver',   # 司机取消
    '501': 'in_progress',       # 司机报单
    '506': 'failed',            # 派单失败
}

# e代驾状态码详细描述映射
EDJ_STATUS_DESC_MAP = {
    '102': '开始系统派单',
    '180': '系统派单中',
    '301': '司机接单',
    '302': '司机就位',
    '303': '司机开车',
    '304': '代驾结束',
    '403': '客户取消',
    '404': '司机取消',
    '501': '司机报单',
    '506': '系统派单失败',
}

@bp.route('/edaijia_webhook', methods=['POST'])
def edaijia_webhook():
    data = request.json
    current_app.logger.info(f"Received eDaijia webhook: {json.dumps(data)}")
    if not data or 'orderId' not in data or 'status' not in data:
        current_app.logger.warning("Invalid webhook data received from eDaijia.")
        return jsonify({'code': 1, 'message': 'Invalid data'}), 400 # eDaijia expects specific response format for errors

    try:
        order = Order.query.filter_by(edaijia_order_id=data['orderId']).first()
        if not order:
            current_app.logger.warning(f"eDaijia webhook: Order {data['orderId']} not found in our DB.")
            # Still return a success to eDaijia to prevent retries if they expect 200 for ack
            return jsonify({'code': 0, 'message': 'success (order not found locally)'}), 200
        old_status = order.status
        # 官方API状态码
        edj_status_code = str(data['status'])
        # 本地状态映射
        new_internal_status = EDJ_STATUS_MAP.get(edj_status_code, old_status)
        order.edj_status_code = edj_status_code
        order.status = new_internal_status
        # 使用统一的状态描述映射
        order.edj_status_desc = EDJ_STATUS_DESC_MAP.get(edj_status_code, f'未知状态({edj_status_code})')
        if 'driverInfo' in data and data['driverInfo']:
            driver_info = data['driverInfo'] # This is usually an object
            order.driver_name = driver_info.get('name')
            order.driver_phone = driver_info.get('phone')
            current_app.logger.info(f"Updated driver info for order {order.edaijia_order_id}: Name={order.driver_name}, Phone={order.driver_phone}")

        # eDaijia amount is typically in cents
        if 'amount' in data and data['amount'] is not None:
            try:
                order.price = float(data['amount']) / 100.0
                current_app.logger.info(f"Updated price for order {order.edaijia_order_id} to {order.price}")
            except ValueError:
                current_app.logger.warning(f"Could not parse amount '{data['amount']}' for order {order.edaijia_order_id}")

        status_text_map = {
            'in_progress': '进行中',
            'completed': '已完成',
            'cancelled': '已取消'
        }
        status_text = status_text_map.get(new_internal_status, new_internal_status.replace("_", " ").title())

        now_str = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S %Z')
        remark_update = f"\n[e代驾回调] 订单状态更新为: {status_text} ({new_internal_status}) - {now_str}"
        order.remarks = (order.remarks if order.remarks else '') + remark_update

        customer_remark_update = f"\n[e代驾] 订单({order.edaijia_order_id})状态: {status_text} - {now_str}"
        order.customer.remarks = (order.customer.remarks if order.customer.remarks else '') + customer_remark_update

        db.session.commit()
        current_app.logger.info(f"Order {order.edaijia_order_id} status updated from '{old_status}' to '{new_internal_status}' via webhook.")
        return jsonify({'code': 0, 'message': 'success'}) # Standard success response to eDaijia

    except Exception as e:
        current_app.logger.error(f"Error processing eDaijia webhook for order {data.get('orderId')}: {str(e)}", exc_info=True)
        db.session.rollback()
        # Return error format eDaijia expects, if specified, else generic error
        return jsonify({'code': 1, 'message': f'Internal server error processing webhook: {str(e)}'}), 500

@bp.route('/select_store/<store_id>', methods=['POST'])
def select_store(store_id):
    from flask import session
    if store_id == 'all':
        # 选择全部门店
        session.pop('current_store_id', None)
        return jsonify({'success': True, 'message': '已切换到全部门店'})
    else:
        # 选择特定门店
        store_id = int(store_id)
        store = Store.query.get(store_id)
        if not store:
            return jsonify({'success': False, 'message': '门店不存在'}), 404
        session['current_store_id'] = store_id
        return jsonify({'success': True, 'message': f'已切换到门店：{store.name}'})

@bp.route('/select_store_by_name/<path:store_name>', methods=['POST'])
def select_store_by_name(store_name):
    """基于门店名称选择门店 - 新架构"""
    from flask import session
    try:
        # URL解码门店名称
        store_name = decode_store_name(store_name)

        # 验证门店名称是否存在
        existing_store = db.session.query(Order.store_name).filter(
            Order.store_name == store_name
        ).first()

        if not existing_store:
            return jsonify({'success': False, 'message': f'门店不存在：{store_name}'}), 404

        session['current_store_name'] = store_name
        return jsonify({'success': True, 'message': f'已切换到门店：{store_name}'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'切换门店失败：{str(e)}'}), 500

@bp.route('/select_store_by_name/all', methods=['POST'])
def select_all_stores_by_name():
    """选择全部门店 - 新架构"""
    from flask import session
    try:
        session.pop('current_store_name', None)
        return jsonify({'success': True, 'message': '已切换到全部门店模式'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'切换失败：{str(e)}'}), 500

@bp.route('/customers', methods=['GET'])
def api_customers():
    from flask import session
    store_id = session.get('current_store_id')
    if not store_id:
        return jsonify({'customers': []})
    q = request.args.get('q', '').strip()
    limit = request.args.get('limit', 5, type=int)  # 默认5条
    query = Customer.query.filter_by(store_id=store_id)
    if q:
        search = f"%{q}%"
        query = query.filter(or_(
            Customer.name.ilike(search),
            Customer.phone.ilike(search),
            Customer.plate_number.ilike(search),
            Customer.car_model.ilike(search),
            Customer.f6_customer_id.ilike(search)
        ))
    customers = query.order_by(Customer.id.desc()).limit(limit).all()
    result = []
    for c in customers:
        result.append({
            'id': c.id,
            'name': c.name,
            'phone': c.phone,
            'car_model': c.car_model,
            'plate_number': c.plate_number,
            'home_address': c.home_address,
            'remarks': c.remarks
        })
    return jsonify({'customers': result})

@bp.route('/get_current_location', methods=['POST'])
def get_current_location():
    """
    调用e代驾API获取当前位置
    """
    token = get_edaijia_token()
    if not token:
        return jsonify({'success': False, 'message': '无法获取e代驾API Token'}), 500

    # 构造系统参数
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    sys_params = {
        'appkey': current_app.config.get('EDAIJIA_API_KEY'),
        'timestamp': timestamp,
        'ver': '3.4.3',
        'from': '01012345'
    }

    # 业务参数
    business_params = {
        'token': token
    }

    # 合并所有参数
    all_params = {**sys_params, **business_params}

    # 计算签名 - 所有参数都参与签名
    sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

    payload = all_params.copy()
    payload['sig'] = sig

    try:
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/location/current'
        resp = requests.post(api_url, data=payload, timeout=5)
        resp_json = resp.json()

        if resp_json.get('code') == '0' or resp_json.get('code') == 0:
            return jsonify({
                'success': True,
                'address': resp_json.get('data', {}).get('address', ''),
                'latitude': resp_json.get('data', {}).get('latitude', 0),
                'longitude': resp_json.get('data', {}).get('longitude', 0)
            })
        else:
            return jsonify({
                'success': False,
                'message': resp_json.get('message', '获取位置失败')
            })
    except Exception as e:
        current_app.logger.error(f"Error getting location: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取位置时发生错误: {str(e)}'}), 500

@bp.route('/geocoding/address', methods=['POST'])
def geocoding_address():
    """
    地址转经纬度API - 支持百度地图API和Mock API

    请求参数:
    {
        "address": "深圳宝安国际机场",
        "use_mock": false  // 可选，是否使用Mock API（开发测试用）
    }

    返回:
    {
        "success": true,
        "data": {
            "longitude": 113.8206,
            "latitude": 22.6390,
            "formatted_address": "深圳宝安国际机场",
            "confidence": 0.9,
            "source": "baidu"  // 数据来源：baidu/mock
        }
    }
    """
    try:
        data = request.get_json()
        address = data.get('address', '').strip()
        use_mock = data.get('use_mock', False)

        if not address:
            return jsonify({'success': False, 'message': '地址不能为空'}), 400

        # 根据配置选择使用百度地图API还是Mock API
        baidu_api_key = current_app.config.get('BAIDU_MAP_API_KEY')

        # 强制使用百度地图API（已注释Mock API fallback）
        if use_mock:
            current_app.logger.info(f"明确要求使用Mock API进行地址解析: {address}")
            return geocoding_with_mock_api(address)
        elif not baidu_api_key or baidu_api_key == 'your_baidu_map_api_key':
            current_app.logger.error(f"百度API Key未配置，无法进行地址解析: {address}")
            return jsonify({
                'success': False,
                'message': '百度地图API Key未配置，请联系管理员'
            }), 500
        else:
            current_app.logger.info(f"使用百度地图API进行地址解析: {address}")
            return geocoding_with_baidu_api(address, baidu_api_key)

    except Exception as e:
        current_app.logger.error(f"Error geocoding address: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'地址解析时发生错误: {str(e)}'}), 500

def geocoding_with_baidu_api(address, api_key):
    """使用百度地图API进行地址解析（统一接口）"""
    try:
        # 调用统一的百度地图API函数
        result = call_baidu_geocoding_api(address, '全国')

        if result['success']:
            # 返回标准格式
            return jsonify(result)
        else:
            # 百度API失败，直接返回错误（已注释Mock fallback）
            current_app.logger.warning(f"百度地图API失败: {result['message']}")
            return jsonify({
                'success': False,
                'message': f"地址解析失败: {result['message']}"
            }), 400

    except Exception as e:
        current_app.logger.error(f"百度地图API调用异常: {str(e)}")
        # 已注释Mock fallback，直接返回错误
        return jsonify({
            'success': False,
            'message': f"地址解析异常: {str(e)}"
        }), 500

def geocoding_with_mock_api(address):
    """使用Mock API进行地址解析"""
    try:
        # 获取e代驾token
        token = get_edaijia_token()
        if not token:
            # 如果无法获取token，使用本地Mock数据
            return geocoding_with_local_mock(address)

        # 构造系统参数
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        sys_params = {
            'appkey': current_app.config.get('EDAIJIA_API_KEY'),
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345'
        }

        # 业务参数
        business_params = {
            'token': token,
            'address': address
        }

        # 合并所有参数
        all_params = {**sys_params, **business_params}

        # 计算签名
        sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

        payload = all_params.copy()
        payload['sig'] = sig

        # 调用Mock地理编码API
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/geocoding/address'
        resp = requests.post(api_url, data=payload, timeout=10)
        resp_json = resp.json()

        if resp_json.get('code') == '0' or resp_json.get('code') == 0:
            geocoding_data = resp_json.get('data', {})
            return jsonify({
                'success': True,
                'data': {
                    'longitude': geocoding_data.get('longitude'),
                    'latitude': geocoding_data.get('latitude'),
                    'formatted_address': geocoding_data.get('formatted_address'),
                    'confidence': geocoding_data.get('confidence', 1.0),
                    'source': 'mock'
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': resp_json.get('message', '地址解析失败')
            })

    except Exception as e:
        current_app.logger.error(f"Mock API调用异常: {str(e)}")
        return geocoding_with_local_mock(address)

def geocoding_with_local_mock(address):
    """本地Mock地址解析（最后的fallback）"""
    # 简单的本地地址库
    local_addresses = {
        '深圳宝安国际机场': {'longitude': 113.8206, 'latitude': 22.6390},
        '深圳北站': {'longitude': 114.0301, 'latitude': 22.6097},
        '深圳站': {'longitude': 114.1141, 'latitude': 22.5332},
        '北京首都国际机场': {'longitude': 116.5974, 'latitude': 40.0799},
        '北京大兴国际机场': {'longitude': 116.4109, 'latitude': 39.5092},
        '上海虹桥国际机场': {'longitude': 121.3364, 'latitude': 31.1979},
        '上海浦东国际机场': {'longitude': 121.8057, 'latitude': 31.1434},
        '广州白云国际机场': {'longitude': 113.2990, 'latitude': 23.3926}
    }

    # 关键词匹配
    for key, coords in local_addresses.items():
        if key in address or any(word in address for word in key.split()):
            return jsonify({
                'success': True,
                'data': {
                    'longitude': coords['longitude'],
                    'latitude': coords['latitude'],
                    'formatted_address': key,
                    'confidence': 0.8,
                    'source': 'local_mock'
                }
            })

    # 注释掉默认fallback，强制使用真实的百度API
    # 如果地址无法解析，应该返回错误而不是假坐标
    return jsonify({
        'success': False,
        'message': f'无法解析地址"{address}"，请检查地址是否正确',
        'source': 'local_mock_disabled'
    }), 400

@bp.route('/get_appointment_times', methods=['POST'])
def get_appointment_times():
    """
    调用e代驾API获取可预约时间
    """
    token = get_edaijia_token()
    if not token:
        return jsonify({'success': False, 'message': '无法获取e代驾API Token'}), 500

    # 构造系统参数
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    sys_params = {
        'appkey': current_app.config.get('EDAIJIA_API_KEY'),
        'timestamp': timestamp,
        'ver': '3.4.3',
        'from': '01012345'
    }

    # 业务参数
    business_params = {
        'token': token
    }

    # 合并所有参数
    all_params = {**sys_params, **business_params}

    # 计算签名 - 所有参数都参与签名
    sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

    payload = all_params.copy()
    payload['sig'] = sig

    try:
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/appointment/times'
        resp = requests.post(api_url, data=payload, timeout=5)
        resp_json = resp.json()

        if resp_json.get('code') == '0' or resp_json.get('code') == 0:
            times = resp_json.get('data', {}).get('times', [])
            return jsonify({
                'success': True,
                'times': times
            })
        else:
            return jsonify({
                'success': False,
                'message': resp_json.get('message', '获取预约时间失败')
            })
    except Exception as e:
        current_app.logger.error(f"Error getting appointment times: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取预约时间时发生错误: {str(e)}'}), 500

@bp.route('/query_order_status/<order_id>', methods=['POST'])
def query_order_status(order_id):
    """
    调用e代驾API查询订单状态
    """
    token = get_edaijia_token()
    if not token:
        return jsonify({'success': False, 'message': '无法获取e代驾API Token'}), 500

    # 构造系统参数
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    sys_params = {
        'appkey': current_app.config.get('EDAIJIA_API_KEY'),
        'timestamp': timestamp,
        'ver': '3.4.3',
        'from': '01012345'
    }

    # 业务参数
    business_params = {
        'token': token,
        'edj_booking_id': order_id  # e代驾订单ID
    }

    # 合并所有参数
    all_params = {**sys_params, **business_params}

    # 计算签名
    sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

    payload = all_params.copy()
    payload['sig'] = sig

    try:
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/order/polling'
        resp = requests.post(api_url, data=payload, timeout=5)
        resp_json = resp.json()

        if resp_json.get('code') == '0' or resp_json.get('code') == 0:
            order_data = resp_json.get('data', {})
            return jsonify({
                'success': True,
                'status': order_data.get('status'),
                'status_desc': order_data.get('status_desc'),
                'driver_info': order_data.get('driver_info'),
                'amount': order_data.get('amount'),
                'order_time': order_data.get('order_time')
            })
        else:
            return jsonify({
                'success': False,
                'message': resp_json.get('message', '查询订单状态失败')
            })
    except Exception as e:
        current_app.logger.error(f"Error querying order status: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'查询订单状态时发生错误: {str(e)}'}), 500

@bp.route('/notify_payment', methods=['POST'])
def notify_payment():
    """
    通知e代驾支付完成

    请求参数:
    {
        "order_id": "本地订单ID",
        "pay_amount": 8500,  # 实际支付金额（分）
        "total_amount": 10000,  # 订单总金额（分）
        "third_trade_no": "F6_PAY_123456",  # F6支付流水号
        "coupon_use_status": 0,  # 0未用券 1用券
        "coupon_from": 0,  # 0服务商自制券 1腾讯商家券
        "coupon_id": "",  # 优惠券ID
        "stock_id": ""  # 优惠券批次ID
    }
    """
    try:
        data = request.get_json()
        order_id = data.get('order_id')

        # 查找订单
        order = Order.query.get(order_id)
        if not order:
            return jsonify({'success': False, 'message': '订单不存在'}), 404

        if not order.edaijia_order_id:
            return jsonify({'success': False, 'message': '订单没有e代驾订单号'}), 400

        # 获取token
        token = get_edaijia_token()
        if not token:
            return jsonify({'success': False, 'message': '无法获取e代驾API Token'}), 500

        # 构造系统参数
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        sys_params = {
            'appkey': current_app.config.get('EDAIJIA_API_KEY'),
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345'
        }

        # 业务参数
        business_params = {
            'token': token,
            'edj_order_id': order.edaijia_order_id,
            'third_order_id': order.third_order_id or f"F6-{order.id}",
            'third_trade_no': data.get('third_trade_no', ''),
            'pay_amount': data.get('pay_amount', 0),
            'total_amount': data.get('total_amount', 0),
            'couponUseStatus': data.get('coupon_use_status', 0),
            'couponFrom': data.get('coupon_from', 0),
            'couponId': data.get('coupon_id', ''),
            'stockId': data.get('stock_id', '')
        }

        # 合并所有参数
        all_params = {**sys_params, **business_params}

        # 计算签名
        sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

        payload = all_params.copy()
        payload['sig'] = sig

        # 调用e代驾支付通知接口
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/order/payNotify'
        resp = requests.post(api_url, data=payload, timeout=10)
        resp_json = resp.json()

        if resp_json.get('code') == '0' or resp_json.get('code') == 0:
            # 更新订单支付状态
            order.payment_status = 'paid'
            order.pay_amount = data.get('pay_amount', 0)
            order.total_amount = data.get('total_amount', 0)
            order.third_trade_no = data.get('third_trade_no', '')
            order.payment_time = datetime.now(timezone.utc)

            # 添加支付记录到备注
            payment_info = f"\n[支付通知] 支付金额: {data.get('pay_amount', 0)/100:.2f}元, 流水号: {data.get('third_trade_no', '')} - {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S %Z')}"
            order.remarks = (order.remarks or '') + payment_info

            db.session.commit()

            current_app.logger.info(f"Payment notification sent successfully for order {order_id}")
            return jsonify({
                'success': True,
                'message': '支付通知发送成功',
                'edaijia_response': resp_json
            })
        else:
            current_app.logger.error(f"e代驾支付通知失败: {resp_json}")
            return jsonify({
                'success': False,
                'message': f'e代驾支付通知失败: {resp_json.get("message", "未知错误")}',
                'edaijia_response': resp_json
            }), 400

    except Exception as e:
        current_app.logger.error(f"Error sending payment notification: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({'success': False, 'message': f'发送支付通知时发生错误: {str(e)}'}), 500

def verify_edaijia_callback_signature(order_id, timestamp, partner_secret):
    """
    验证e代驾回调签名
    sig=md5(orderId&timestamp&partnerSecret)
    """
    sign_str = f"{order_id}&{timestamp}&{partner_secret}"
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest().lower()

@bp.route('/edaijia_payment_callback', methods=['POST'])
def edaijia_payment_callback():
    """
    接收e代驾支付结果回调

    回调参数：
    - orderId: e代驾订单号
    - merchant: 商户号
    - payChannel: 支付渠道
    - orderFee: 订单金额（元）
    - payAmount: 实付金额（元）
    - payTime: 支付时间
    - timestamp: 时间戳
    - sig: 签名
    - thirdOrderId: 第三方订单号
    """
    try:
        # 获取回调参数
        order_id = request.form.get('orderId')
        merchant = request.form.get('merchant')
        pay_channel = request.form.get('payChannel')
        order_fee = request.form.get('orderFee')
        pay_amount = request.form.get('payAmount')
        pay_time = request.form.get('payTime')
        timestamp = request.form.get('timestamp')
        sig = request.form.get('sig')
        third_order_id = request.form.get('thirdOrderId')

        current_app.logger.info(f"Received e代驾 payment callback: orderId={order_id}, payAmount={pay_amount}")

        # 验证必要参数
        if not all([order_id, timestamp, sig]):
            current_app.logger.warning("Missing required parameters in payment callback")
            return jsonify({'code': 1, 'message': '缺少必要参数'}), 400

        # 验证签名
        partner_secret = current_app.config.get('EDAIJIA_CALLBACK_SECRET', 'your_callback_secret')
        expected_sig = verify_edaijia_callback_signature(order_id, timestamp, partner_secret)

        if sig.lower() != expected_sig:
            current_app.logger.warning(f"Invalid signature in payment callback: expected={expected_sig}, received={sig}")
            return jsonify({'code': 1, 'message': '签名验证失败'}), 400

        # 查找订单
        order = Order.query.filter_by(edaijia_order_id=order_id).first()
        if not order:
            current_app.logger.warning(f"Order not found for e代驾 orderId: {order_id}")
            return jsonify({'code': 0, 'message': '订单未找到，但确认收到回调'}), 200

        # 更新订单支付信息
        order.payment_status = 'paid'
        order.pay_channel = pay_channel
        order.order_fee = float(order_fee) if order_fee else 0
        order.pay_amount = int(float(pay_amount) * 100) if pay_amount else 0  # 转换为分
        order.payment_time = datetime.strptime(pay_time, '%Y-%m-%d %H:%M:%S') if pay_time else datetime.now(timezone.utc)

        # 添加支付回调记录到备注
        callback_info = f"\n[e代驾支付回调] 支付金额: {pay_amount}元, 渠道: {pay_channel}, 时间: {pay_time}"
        order.remarks = (order.remarks or '') + callback_info

        db.session.commit()

        current_app.logger.info(f"Payment callback processed successfully for order {order_id}")

        # 返回成功响应
        return jsonify({'code': 0, 'message': '确认支付成功'})

    except Exception as e:
        current_app.logger.error(f"Error processing payment callback: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({'code': 1, 'message': f'处理支付回调时发生错误: {str(e)}'}), 500

@bp.route('/edaijia_order_callback', methods=['POST'])
def edaijia_order_callback():
    """
    接收e代驾订单状态回调

    主要参数：
    - orderId: 订单号
    - orderStatus: 订单状态
    - from: 来源渠道（用于验签）
    - 其他订单相关信息
    """
    try:
        # 获取回调参数
        order_id = request.form.get('orderId')
        order_status = request.form.get('orderStatus')
        from_channel = request.form.get('from')
        sig = request.form.get('sig')

        # 其他订单信息
        booking_id = request.form.get('bookingId')
        phone = request.form.get('phone')
        contact_phone = request.form.get('contactPhone')
        call_time = request.form.get('callTime')
        location_start = request.form.get('locationStart')
        location_end = request.form.get('locationEnd')
        distance = request.form.get('distance')
        price = request.form.get('price')
        income = request.form.get('income')
        driver_id = request.form.get('driverId')
        status_time = request.form.get('statusTime')
        third_order_id = request.form.get('thirdOrderId')
        cancel_desc = request.form.get('cancelDesc')

        current_app.logger.info(f"Received e代驾 order callback: orderId={order_id}, status={order_status}")

        # 验证必要参数
        if not all([order_id, order_status, from_channel, sig]):
            current_app.logger.warning("Missing required parameters in order callback")
            return jsonify({'code': 1, 'message': '缺少必要参数'}), 400

        # 验证签名（简单MD5验证）
        expected_sig = hashlib.md5(from_channel.encode('utf-8')).hexdigest().lower()
        if sig.lower() != expected_sig:
            current_app.logger.warning(f"Invalid signature in order callback: expected={expected_sig}, received={sig}")
            return jsonify({'code': 1, 'message': '签名验证失败'}), 400

        # 查找订单
        order = Order.query.filter_by(edaijia_order_id=order_id).first()
        if not order:
            current_app.logger.warning(f"Order not found for e代驾 orderId: {order_id}")
            return jsonify({'code': 0, 'message': '订单未找到，但确认收到回调'}), 200

        # 更新订单状态
        old_status = order.status
        order.edj_status_code = order_status
        order.status = EDJ_STATUS_MAP.get(order_status, order.status)

        # 更新其他订单信息
        if driver_id:
            order.driver_id = driver_id
        if distance:
            order.distance = float(distance)
        if price:
            order.price = float(price)
        if income:
            order.income = float(income)
        if call_time:
            try:
                order.call_time = datetime.strptime(call_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                pass
        if cancel_desc:
            order.cancel_reason = cancel_desc

        # 添加状态回调记录到备注
        callback_info = f"\n[e代驾状态回调] 状态: {order_status} -> {order.status}"
        if driver_id:
            callback_info += f", 司机: {driver_id}"
        if price:
            callback_info += f", 价格: {price}元"
        if cancel_desc:
            callback_info += f", 取消原因: {cancel_desc}"
        callback_info += f" - {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S %Z')}"

        order.remarks = (order.remarks or '') + callback_info

        db.session.commit()

        current_app.logger.info(f"Order callback processed: {order_id} status changed from {old_status} to {order.status}")

        # 返回成功响应
        return jsonify({'code': 0, 'message': 'success'})

    except Exception as e:
        current_app.logger.error(f"Error processing order callback: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({'code': 1, 'message': f'处理订单回调时发生错误: {str(e)}'}), 500

@bp.route('/sync_f6_stores', methods=['POST'])
def sync_f6_stores():
    """
    从mock server同步F6门店列表，保存到本地Store表，API KEY/SECRET从.env读取。
    """
    from flask import current_app
    # 1. 读取所有门店key/secret配置
    # 假设.env中配置 F6_STORE_KEYS={"门店ID":"key", ...} F6_STORE_SECRETS={"门店ID":"secret", ...}
    store_keys = json.loads(os.environ.get('F6_STORE_KEYS', '{}'))
    store_secrets = json.loads(os.environ.get('F6_STORE_SECRETS', '{}'))
    base_url = current_app.config.get('F6_API_BASE_URL', 'http://127.0.0.1:5001')
    app_key = current_app.config.get('F6_API_KEY')
    app_secret = current_app.config.get('F6_API_SECRET')
    url = base_url.rstrip('/') + '/merchant/org/list'
    uri = '/merchant/org/list'
    body_dict = {"paramValues": [{"pageSize": 100, "currentPage": 1}]}
    body = json.dumps(body_dict, ensure_ascii=False)

    # 构造请求头
    headers = {
        "Content-Type": "application/json",
        "X-Ca-Key": app_key,
    }

    # 计算签名
    signature = f6_calc_signature(app_key, app_secret, body, method='POST', uri=uri, headers=headers)
    headers["X-Ca-Signature"] = signature
    try:
        resp = requests.post(url, data=body, headers=headers, timeout=5)
        resp.raise_for_status()
        resp_json = resp.json()
        if resp_json.get('code') != 200:
            raise Exception(resp_json.get('message', 'F6接口返回异常'))
        orgs = resp_json['data']['list']
        count = 0
        for org in orgs:
            f6_org_id = str(org['idOwnOrg'])
            store = Store.query.filter_by(f6_org_id=f6_org_id).first()
            if not store:
                store = Store(f6_org_id=f6_org_id)
                db.session.add(store)
            store.name = org.get('name')
            store.short_name = org.get('shortName')
            store.province = org.get('province')
            store.city = org.get('city')
            store.area = org.get('area')
            store.detail_address = org.get('detailAddress')
            # 取.env配置的key/secret
            store.api_key = store_keys.get(f6_org_id, app_key)
            store.api_secret = store_secrets.get(f6_org_id, app_secret)
            count += 1
        db.session.commit()
        return jsonify({'success': True, 'message': f'成功同步{count}个门店。'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'同步门店失败: {e}'}), 500

@bp.route('/create_f6_workorder', methods=['POST'])
def create_f6_workorder():
    """
    创建F6工单

    请求参数:
    {
        "order_id": "本地订单ID",
        "customer_id": "F6客户ID",
        "car_id": "F6车辆ID",
        "employee_id": "F6员工ID",
        "employee_name": "员工姓名",
        "org_id": "F6门店ID",
        "memo": "客户备注",
        "org_memo": "门店备注"
    }
    """
    try:
        data = request.get_json()
        order_id = data.get('order_id')

        # 查找订单
        order = Order.query.get(order_id)
        if not order:
            return jsonify({'success': False, 'message': '订单不存在'}), 404

        # 获取门店信息
        store_id = session.get('current_store_id')
        if not store_id:
            return jsonify({'success': False, 'message': '未选择门店'}), 400

        store = Store.query.get(store_id)
        if not store:
            return jsonify({'success': False, 'message': '门店不存在'}), 400

        # 构造F6工单数据
        import time
        unique_key = f"blazer{int(time.time())}{order.id}"

        # 基础信息
        base_info = {
            "maintainType": "XCD",  # 洗车单，可以根据业务需要调整
            "idOwnOrg": int(data.get('org_id', store.f6_org_id)),
            "billDate": time.strftime('%Y-%m-%d %H:%M:%S'),
            "deliveryTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time() + 1800)),  # 30分钟后
            "maintainTypeName": "e代驾服务",
            "mileage": 0,
            "oilCapaciy": 0,
            "idEmployee": int(data.get('employee_id')),
            "employeeName": data.get('employee_name', '系统'),
            "memo": data.get('memo', f'e代驾订单: {order.edaijia_order_id}'),
            "orgMemo": data.get('org_memo', f'从{order.pickup_address}到{order.destination_address}')
        }

        # 客户车辆信息
        customer_car = {
            "idCar": int(data.get('car_id')),
            "idCustomer": int(data.get('customer_id')),
            "carCategoryId": 339268  # 默认分类，可以根据需要调整
        }

        # 服务项目 - e代驾服务
        service_list = [{
            "idService": 121247997,  # 服务项目ID，需要根据F6系统实际配置
            "serviceName": "e代驾服务",
            "workHour": 1.0,
            "workHourPrice": float(order.price or 0),
            "cost": 0.0,
            "serviceCost": 0.0,
            "serviceMemo": f"e代驾订单号: {order.edaijia_order_id}",
            "isMember": 0,
            "employeeList": [{
                "idEmployee": int(data.get('employee_id')),
                "employeeName": data.get('employee_name', '系统')
            }],
            "salesEmployeeList": [],
            "favourableList": []
        }]

        # 构造完整请求体
        body_dict = {
            "paramValues": [{
                "uniqueKey": unique_key,
                "base": base_info,
                "customerCar": customer_car,
                "serviceList": service_list
            }]
        }

        body = json.dumps(body_dict, ensure_ascii=False)
        uri = '/blazer/maintain/bill/create'

        # 构造请求头
        headers = {
            "Content-Type": "application/json",
            "X-Ca-Key": store.api_key,
        }

        # 计算签名
        signature = f6_calc_signature(store.api_key, store.api_secret, body, method='POST', uri=uri, headers=headers)
        headers["X-Ca-Signature"] = signature

        # 调用F6 API
        base_url = current_app.config.get('F6_API_BASE_URL', 'http://127.0.0.1:5001')
        url = base_url.rstrip('/') + uri

        resp = requests.post(url, data=body, headers=headers, timeout=10)
        resp_json = resp.json()

        if resp_json.get('code') == 200:
            # 更新订单F6工单信息
            workorder_data = resp_json.get('data', {})
            order.f6_workorder_id = str(workorder_data.get('pkId', ''))
            order.f6_workorder_version = workorder_data.get('version', 0)

            # 添加工单创建记录到备注
            workorder_info = f"\n[F6工单创建] 工单ID: {order.f6_workorder_id}, 版本: {order.f6_workorder_version} - {time.strftime('%Y-%m-%d %H:%M:%S')}"
            order.remarks = (order.remarks or '') + workorder_info

            db.session.commit()

            current_app.logger.info(f"F6 workorder created successfully for order {order_id}: {order.f6_workorder_id}")
            return jsonify({
                'success': True,
                'message': 'F6工单创建成功',
                'workorder_id': order.f6_workorder_id,
                'f6_response': resp_json
            })
        else:
            current_app.logger.error(f"F6工单创建失败: {resp_json}")
            return jsonify({
                'success': False,
                'message': f'F6工单创建失败: {resp_json.get("message", "未知错误")}',
                'f6_response': resp_json
            }), 400

    except Exception as e:
        current_app.logger.error(f"Error creating F6 workorder: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({'success': False, 'message': f'创建F6工单时发生错误: {str(e)}'}), 500

@bp.route('/get_f6_employees', methods=['POST'])
def get_f6_employees():
    """
    获取F6员工列表

    请求参数:
    {
        "phone": "员工手机号" (可选，用于查找特定员工)
    }
    """
    try:
        data = request.get_json() or {}
        phone = data.get('phone')

        # 获取门店信息
        store_id = session.get('current_store_id')
        if not store_id:
            return jsonify({'success': False, 'message': '未选择门店'}), 400

        store = Store.query.get(store_id)
        if not store:
            return jsonify({'success': False, 'message': '门店不存在'}), 400

        # 构造请求体
        body_dict = {
            "paramValues": [{
                "pageSize": 100,
                "currentPage": 1
            }]
        }

        body = json.dumps(body_dict, ensure_ascii=False)
        uri = '/merchant/employee/list'

        # 构造请求头
        headers = {
            "Content-Type": "application/json",
            "X-Ca-Key": store.api_key,
        }

        # 计算签名
        signature = f6_calc_signature(store.api_key, store.api_secret, body, method='POST', uri=uri, headers=headers)
        headers["X-Ca-Signature"] = signature

        # 调用F6 API
        base_url = current_app.config.get('F6_API_BASE_URL', 'http://127.0.0.1:5001')
        url = base_url.rstrip('/') + uri

        resp = requests.post(url, data=body, headers=headers, timeout=10)
        resp_json = resp.json()

        if resp_json.get('code') == 200:
            employees = resp_json.get('data', {}).get('list', [])

            # 如果指定了手机号，过滤员工
            if phone:
                employees = [emp for emp in employees if emp.get('cellPhone') == phone]

            # 过滤在职员工
            active_employees = [emp for emp in employees if emp.get('employeeStatus') == 0]

            return jsonify({
                'success': True,
                'employees': active_employees,
                'total': len(active_employees)
            })
        else:
            current_app.logger.error(f"获取F6员工列表失败: {resp_json}")
            return jsonify({
                'success': False,
                'message': f'获取F6员工列表失败: {resp_json.get("message", "未知错误")}',
                'f6_response': resp_json
            }), 400

    except Exception as e:
        current_app.logger.error(f"Error getting F6 employees: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取F6员工列表时发生错误: {str(e)}'}), 500

@bp.route('/authenticate_manager', methods=['POST'])
def authenticate_manager():
    """
    通过手机号认证店长身份

    请求参数:
    {
        "phone": "店长手机号",
        "name": "店长姓名" (可选，用于验证)
    }
    """
    try:
        data = request.get_json()
        phone = data.get('phone')
        name = data.get('name')

        if not phone:
            return jsonify({'success': False, 'message': '手机号不能为空'}), 400

        # 获取门店信息
        store_id = session.get('current_store_id')
        if not store_id:
            return jsonify({'success': False, 'message': '未选择门店'}), 400

        store = Store.query.get(store_id)
        if not store:
            return jsonify({'success': False, 'message': '门店不存在'}), 400

        # 调用员工列表API查找该手机号的员工
        body_dict = {
            "paramValues": [{
                "pageSize": 100,
                "currentPage": 1
            }]
        }

        body = json.dumps(body_dict, ensure_ascii=False)
        uri = '/merchant/employee/list'

        # 构造请求头
        headers = {
            "Content-Type": "application/json",
            "X-Ca-Key": store.api_key,
        }

        # 计算签名
        signature = f6_calc_signature(store.api_key, store.api_secret, body, method='POST', uri=uri, headers=headers)
        headers["X-Ca-Signature"] = signature

        # 调用F6 API
        base_url = current_app.config.get('F6_API_BASE_URL', 'http://127.0.0.1:5001')
        url = base_url.rstrip('/') + uri

        resp = requests.post(url, data=body, headers=headers, timeout=10)
        resp_json = resp.json()

        if resp_json.get('code') == 200:
            employees = resp_json.get('data', {}).get('list', [])

            # 查找匹配的员工
            matching_employee = None
            for emp in employees:
                if (emp.get('cellPhone') == phone and
                    emp.get('employeeStatus') == 0 and  # 在职
                    emp.get('userStatus') == 0):  # 账号在用

                    # 如果提供了姓名，验证姓名是否匹配
                    if name and emp.get('name') != name:
                        continue

                    matching_employee = emp
                    break

            if matching_employee:
                # 验证是否有管理权限（角色包含"管理员"）
                role = matching_employee.get('role', '')
                if '管理员' in role or '店长' in role:
                    # 保存认证信息到session
                    session['authenticated_manager'] = {
                        'phone': phone,
                        'name': matching_employee.get('name'),
                        'employee_id': matching_employee.get('idEmployee'),
                        'role': role,
                        'store_id': store_id,
                        'api_key': store.api_key,
                        'api_secret': store.api_secret
                    }

                    return jsonify({
                        'success': True,
                        'message': '店长身份认证成功',
                        'manager_info': {
                            'name': matching_employee.get('name'),
                            'phone': phone,
                            'role': role,
                            'employee_id': matching_employee.get('idEmployee')
                        }
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': f'该员工({matching_employee.get("name")})不具备管理权限'
                    }), 403
            else:
                return jsonify({
                    'success': False,
                    'message': '未找到匹配的在职员工或姓名不匹配'
                }), 404
        else:
            current_app.logger.error(f"获取F6员工列表失败: {resp_json}")
            return jsonify({
                'success': False,
                'message': f'验证员工身份失败: {resp_json.get("message", "未知错误")}'
            }), 400

    except Exception as e:
        current_app.logger.error(f"Error authenticating manager: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'认证店长身份时发生错误: {str(e)}'}), 500

@bp.route('/f6/estimate_cost', methods=['POST'])
def f6_estimate_cost():
    """
    F6系统预估费用接口

    请求参数:
    {
        "start_address": "龙华店",
        "start_longitude": 114.1315,  // 可选，如果没有会通过地址获取
        "start_latitude": 22.6569,   // 可选，如果没有会通过地址获取
        "end_address": "深圳市南山区深圳湾",
        "end_longitude": 114.1094,   // 可选，如果没有会通过地址获取
        "end_latitude": 22.4818,     // 可选，如果没有会通过地址获取
        "manager_name": "周鑫芸"
    }
    """
    try:
        data = request.get_json()

        # 验证必要参数
        required_fields = ['start_address', 'end_address', 'manager_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必要参数: {field}'}), 400

        start_address = data.get('start_address')
        end_address = data.get('end_address')
        manager_name = data.get('manager_name')

        # 获取坐标信息
        start_longitude = data.get('start_longitude')
        start_latitude = data.get('start_latitude')
        end_longitude = data.get('end_longitude')
        end_latitude = data.get('end_latitude')

        # 如果没有提供坐标，尝试通过地址获取坐标
        if not all([start_longitude, start_latitude]):
            start_coords = get_coordinates_from_address(start_address)
            if start_coords:
                start_longitude, start_latitude = start_coords
            else:
                # 使用默认坐标（深圳市中心）
                start_longitude, start_latitude = 114.0579, 22.5431
                current_app.logger.warning(f"无法获取起点坐标，使用默认坐标: {start_address}")

        if not all([end_longitude, end_latitude]):
            end_coords = get_coordinates_from_address(end_address)
            if end_coords:
                end_longitude, end_latitude = end_coords
            else:
                # 使用默认坐标（深圳市中心偏移）
                end_longitude, end_latitude = 114.0679, 22.5531
                current_app.logger.warning(f"无法获取终点坐标，使用默认坐标: {end_address}")

        # 获取e代驾token
        token = get_edaijia_token()
        if not token:
            return jsonify({'success': False, 'message': '无法获取e代驾API Token'}), 500

        # 构造e代驾预估费用API参数
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        sys_params = {
            'appkey': current_app.config.get('EDAIJIA_API_KEY'),
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345'
        }

        business_params = {
            'token': token,
            'start_longitude': float(start_longitude),
            'start_latitude': float(start_latitude),
            'end_longitude': float(end_longitude),
            'end_latitude': float(end_latitude)
        }

        # 合并参数并计算签名
        all_params = {**sys_params, **business_params}
        sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

        payload = all_params.copy()
        payload['sig'] = sig

        # 调用e代驾预估费用API
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/order/costestimateV2'

        current_app.logger.info(f"调用e代驾预估费用API: {api_url}")
        current_app.logger.debug(f"预估费用请求参数: {payload}")

        resp = requests.post(api_url, data=payload, timeout=10)
        resp_json = resp.json()

        current_app.logger.debug(f"e代驾预估费用响应: {resp_json}")

        if resp_json.get('code') != '0' and resp_json.get('code') != 0:
            raise Exception(resp_json.get('message', 'e代驾预估费用查询失败'))

        # 解析e代驾返回的费用信息 - 按照官方V2格式
        estimate_data = resp_json.get('data', {})

        # 解析费用明细
        fee_detail = estimate_data.get('fee_detail', [])
        dynamic_info = estimate_data.get('dynamic', {})

        # 从费用明细中提取各项费用
        start_fee = 0.0
        millage_fee = 0.0
        time_fee = 0.0
        return_fee = 0.0
        extra_server_fee = 0.0
        remote_fee = 0.0

        for fee_item in fee_detail:
            data_id = fee_item.get('data_id', '')
            money = float(fee_item.get('money', 0))

            if data_id == 'start_fee':
                start_fee = money
            elif data_id == 'millage_fee':
                millage_fee = money
            elif data_id == 'time_fee':
                time_fee = money
            elif data_id == 'return_fee':
                return_fee = money
            elif data_id == 'extra_server_fee':
                extra_server_fee = money
            elif data_id == 'remote_fee':
                remote_fee = money

        # 计算基础费用（起步价+里程费）和其他费用
        base_fee = start_fee + millage_fee
        other_fee = time_fee + return_fee + extra_server_fee + remote_fee

        # 构造返回数据 - 保持与前端兼容的简化格式
        result = {
            'success': True,
            'estimate': {
                'total_fee': float(estimate_data.get('total', 0)),
                'need_pay': float(estimate_data.get('need_pay', 0)),
                'base_fee': base_fee,
                'distance_fee': millage_fee,
                'time_fee': time_fee,
                'other_fee': other_fee,
                'distance': float(estimate_data.get('distance', 0)),
                'duration': float(estimate_data.get('duration', 0)),
                'dynamic_fee': float(dynamic_info.get('dynamic_fee', 0)),
                'is_fixed_price': int(estimate_data.get('is_fixed_price', 0))
            },
            'coordinates': {
                'start_longitude': start_longitude,
                'start_latitude': start_latitude,
                'end_longitude': end_longitude,
                'end_latitude': end_latitude
            },
            'fee_detail': fee_detail,  # 保留完整的费用明细
            'dynamic': dynamic_info,   # 保留动态费用信息
            'order_fee_deduction': estimate_data.get('order_fee_deduction', [])  # 优惠券抵扣信息
        }

        current_app.logger.info(f"预估费用查询成功: {manager_name} - {start_address} -> {end_address}, 费用: ¥{result['estimate']['total_fee']}")

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"预估费用查询失败: {e}")
        return jsonify({
            'success': False,
            'message': f'预估费用查询失败: {str(e)}'
        }), 500


@bp.route('/f6/cancel_order', methods=['POST'])
def f6_cancel_order():
    """
    F6系统订单取消接口

    请求参数:
    {
        "order_id": "EDJ123456789",
        "manager_name": "周鑫芸",
        "reason_code": "1001",  // 可选，取消原因代码
        "reason_detail": "暂时不需要代驾了"  // 可选，取消原因详情
    }
    """
    try:
        data = request.get_json()

        # 验证必要参数
        required_fields = ['order_id', 'manager_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必要参数: {field}'}), 400

        order_id = data.get('order_id')
        manager_name = data.get('manager_name')
        reason_code = data.get('reason_code', '1002')  # 默认：暂时不需要代驾了
        reason_detail = data.get('reason_detail', '用户取消订单')

        current_app.logger.info(f"开始取消订单: {order_id}, 操作人: {manager_name}")

        # 查找订单
        order = Order.query.filter_by(edaijia_order_id=order_id).first()
        if not order:
            return jsonify({'success': False, 'message': '订单不存在'}), 404

        # 检查订单状态是否可以取消
        # 根据e代驾官方文档，只有以下状态可以取消
        cancellable_statuses = ['102', '180', '301', '302']  # 可取消的状态：系统派单中、司机接单、司机就位
        if order.edj_status_code not in cancellable_statuses:
            return jsonify({
                'success': False,
                'message': f'订单当前状态({order.edj_status_code})不允许取消'
            }), 400

        # 先查询取消费用
        cancel_fee_result = get_cancel_fee(order_id)
        if not cancel_fee_result['success']:
            return jsonify({
                'success': False,
                'message': f'查询取消费用失败: {cancel_fee_result["message"]}'
            }), 500

        # 获取e代驾token
        token = get_edaijia_token()
        if not token:
            return jsonify({'success': False, 'message': '无法获取e代驾API Token'}), 500

        # 构造e代驾取消订单API参数
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        sys_params = {
            'appkey': current_app.config.get('EDAIJIA_API_KEY'),
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345'
        }

        business_params = {
            'token': token,
            'edj_order_id': order_id,
            'reason_code': reason_code,
            'reason_detail': reason_detail
        }

        # 合并参数并计算签名
        all_params = {**sys_params, **business_params}
        sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

        payload = all_params.copy()
        payload['sig'] = sig

        # 调用e代驾取消订单API
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/order/cancel'

        current_app.logger.info(f"调用e代驾取消订单API: {api_url}")
        current_app.logger.debug(f"取消订单请求参数: {payload}")

        resp = requests.post(api_url, data=payload, timeout=10)
        resp_json = resp.json()

        current_app.logger.debug(f"e代驾取消订单响应: {resp_json}")

        if resp_json.get('code') != '0' and resp_json.get('code') != 0:
            raise Exception(resp_json.get('message', 'e代驾订单取消失败'))

        # 解析取消费用信息
        cancel_data = resp_json.get('data', {})
        fee_detail = cancel_data.get('fee_detail', {})

        # 更新本地订单状态 - 根据取消原因设置细分状态
        order.edj_status_code = '403'  # e代驾官方状态码：客户取消
        order.edj_status_desc = '客户取消'  # e代驾状态描述
        order.status = 'cancelled_customer'  # 内部状态：客户取消
        order.cancel_time = datetime.now(timezone.utc)
        order.cancel_reason = reason_detail
        order.cancel_fee = float(fee_detail.get('total_cost', 0))

        db.session.commit()

        # 构造返回数据
        result = {
            'success': True,
            'message': '订单取消成功',
            'cancel_info': {
                'order_id': order_id,
                'cancel_fee': float(fee_detail.get('cancel_fee', 0)),
                'wait_fee': float(fee_detail.get('wait_fee', 0)),
                'wait_time': int(fee_detail.get('wait_time', 0)),
                'total_cost': float(fee_detail.get('total_cost', 0)),
                'reason_code': reason_code,
                'reason_detail': reason_detail
            }
        }

        current_app.logger.info(f"订单取消成功: {order_id}, 取消费用: ¥{result['cancel_info']['total_cost']}")

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"订单取消失败: {e}")
        return jsonify({
            'success': False,
            'message': f'订单取消失败: {str(e)}'
        }), 500


def get_cancel_fee(order_id):
    """
    查询订单取消费用

    Args:
        order_id: e代驾订单ID

    Returns:
        dict: 取消费用信息
    """
    try:
        # 获取e代驾token
        token = get_edaijia_token()
        if not token:
            return {'success': False, 'message': '无法获取e代驾API Token'}

        # 构造e代驾查询取消费API参数
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        sys_params = {
            'appkey': current_app.config.get('EDAIJIA_API_KEY'),
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345'
        }

        business_params = {
            'token': token,
            'edj_order_id': order_id
        }

        # 合并参数并计算签名
        all_params = {**sys_params, **business_params}
        sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

        payload = all_params.copy()
        payload['sig'] = sig

        # 调用e代驾查询取消费API
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/order/getCancelFee'

        current_app.logger.info(f"调用e代驾查询取消费API: {api_url}")
        current_app.logger.debug(f"查询取消费请求参数: {payload}")

        resp = requests.post(api_url, data=payload, timeout=10)
        resp_json = resp.json()

        current_app.logger.debug(f"e代驾查询取消费响应: {resp_json}")

        if resp_json.get('code') != '0' and resp_json.get('code') != 0:
            raise Exception(resp_json.get('message', 'e代驾查询取消费失败'))

        # 解析取消费用信息
        cancel_data = resp_json.get('data', {})
        fee_detail = cancel_data.get('fee_detail', {})

        result = {
            'success': True,
            'fee_detail': {
                'cancel_fee': float(fee_detail.get('cancel_fee', 0)),
                'wait_fee': float(fee_detail.get('wait_fee', 0)),
                'wait_time': int(fee_detail.get('wait_time', 0)),
                'total_cost': float(fee_detail.get('total_cost', 0))
            }
        }

        current_app.logger.info(f"查询取消费成功: {order_id}, 费用: ¥{result['fee_detail']['total_cost']}")

        return result

    except Exception as e:
        current_app.logger.error(f"查询取消费失败: {e}")
        return {'success': False, 'message': str(e)}


@bp.route('/f6/cancel_fee', methods=['POST'])
def f6_cancel_fee():
    """
    F6系统查询取消费用接口

    请求参数:
    {
        "order_id": "EDJ123456789",
        "manager_name": "周鑫芸"
    }
    """
    try:
        data = request.get_json()

        # 验证必要参数
        required_fields = ['order_id', 'manager_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必要参数: {field}'}), 400

        order_id = data.get('order_id')
        manager_name = data.get('manager_name')

        current_app.logger.info(f"查询取消费用: {order_id}, 操作人: {manager_name}")

        # 查找订单
        order = Order.query.filter_by(edaijia_order_id=order_id).first()
        if not order:
            return jsonify({'success': False, 'message': '订单不存在'}), 404

        # 检查订单状态是否可以取消
        cancellable_statuses = ['102', '180', '301', '302']
        if order.edj_status_code not in cancellable_statuses:
            return jsonify({
                'success': False,
                'message': f'订单当前状态({order.edj_status_code})不允许取消'
            }), 400

        # 查询取消费用
        cancel_fee_result = get_cancel_fee(order_id)
        if not cancel_fee_result['success']:
            return jsonify({
                'success': False,
                'message': f'查询取消费用失败: {cancel_fee_result["message"]}'
            }), 500

        # 构造返回数据
        result = {
            'success': True,
            'cancel_fee': cancel_fee_result['fee_detail']
        }

        current_app.logger.info(f"查询取消费用成功: {order_id}, 费用: ¥{result['cancel_fee']['total_cost']}")

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"查询取消费用失败: {e}")
        return jsonify({
            'success': False,
            'message': f'查询取消费用失败: {str(e)}'
        }), 500


@bp.route('/f6/modify_order', methods=['POST'])
def f6_modify_order():
    """
    F6系统修改订单接口

    请求参数:
    {
        "order_id": "EDJ123456789",
        "manager_name": "周鑫芸",
        "destination_address": "深圳市南山区深圳湾",
        "destination_latitude": 22.4818,
        "destination_longitude": 113.9336
    }
    """
    try:
        data = request.get_json()

        # 验证必要参数 - 坐标可选，会自动通过百度API获取
        required_fields = ['order_id', 'manager_name', 'destination_address']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必要参数: {field}'}), 400

        order_id = data.get('order_id')
        manager_name = data.get('manager_name')
        destination_address = data.get('destination_address')
        destination_latitude = data.get('destination_latitude')
        destination_longitude = data.get('destination_longitude')

        # 如果没有提供坐标，通过百度API获取
        if not destination_latitude or not destination_longitude:
            current_app.logger.info(f"目的地缺少坐标，尝试地理编码: {destination_address}")
            destination_coords = get_coordinates_from_address(destination_address)
            if destination_coords:
                destination_longitude, destination_latitude = destination_coords
                current_app.logger.info(f"目的地地理编码成功: {destination_coords}")
            else:
                return jsonify({
                    'success': False,
                    'message': f'无法解析目的地"{destination_address}"的位置信息，请确认地址是否正确',
                    'error_type': 'geocoding_failed',
                    'address': destination_address
                }), 400

        current_app.logger.info(f"开始修改订单: {order_id}, 操作人: {manager_name}")
        current_app.logger.info(f"新目的地: {destination_address} ({destination_latitude}, {destination_longitude})")

        # 查找订单
        order = Order.query.filter_by(edaijia_order_id=order_id).first()
        if not order:
            return jsonify({'success': False, 'message': '订单不存在'}), 404

        # 检查订单状态是否可以修改
        # 根据e代驾官方文档，只有以下状态可以修改目的地
        modifiable_statuses = ['102', '180', '301', '302']  # 可修改的状态：系统派单中、司机接单、司机就位
        if order.edj_status_code not in modifiable_statuses:
            return jsonify({
                'success': False,
                'message': f'订单当前状态({order.edj_status_code})不允许修改目的地'
            }), 400

        # 验证坐标格式
        try:
            lat = float(destination_latitude)
            lng = float(destination_longitude)

            # 简单的坐标范围验证（中国境内）
            if not (18 <= lat <= 54 and 73 <= lng <= 135):
                raise ValueError("坐标超出有效范围")

        except ValueError as e:
            return jsonify({
                'success': False,
                'message': f'坐标格式错误: {str(e)}'
            }), 400

        # 获取e代驾token
        token = get_edaijia_token()
        if not token:
            return jsonify({'success': False, 'message': '无法获取e代驾API Token'}), 500

        # 构造e代驾修改订单API参数
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        sys_params = {
            'appkey': current_app.config.get('EDAIJIA_API_KEY'),
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345'
        }

        business_params = {
            'token': token,
            'orderId': order_id,
            'destinationAddress': destination_address,
            'destinationLat': str(destination_latitude),
            'destinationLng': str(destination_longitude),
            'gpsType': 'baidu'
        }

        # 合并参数并计算签名
        all_params = {**sys_params, **business_params}
        sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

        payload = all_params.copy()
        payload['sig'] = sig

        # 调用e代驾修改订单API
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/order/modify/destination'

        current_app.logger.info(f"调用e代驾修改订单API: {api_url}")
        current_app.logger.debug(f"修改订单请求参数: {payload}")

        resp = requests.post(api_url, data=payload, timeout=10)
        resp_json = resp.json()

        current_app.logger.debug(f"e代驾修改订单响应: {resp_json}")

        if resp_json.get('code') != '0' and resp_json.get('code') != 0:
            raise Exception(resp_json.get('message', 'e代驾修改订单失败'))

        # 更新本地订单信息
        order.destination_address = destination_address
        order.destination_latitude = destination_latitude
        order.destination_longitude = destination_longitude
        order.updated_time = datetime.now(datetime.UTC)

        db.session.commit()

        # 构造返回数据
        result = {
            'success': True,
            'message': '订单修改成功',
            'order_info': {
                'order_id': order_id,
                'destination_address': destination_address,
                'destination_latitude': destination_latitude,
                'destination_longitude': destination_longitude
            }
        }

        current_app.logger.info(f"订单修改成功: {order_id}, 新目的地: {destination_address}")

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"订单修改失败: {e}")
        return jsonify({
            'success': False,
            'message': f'订单修改失败: {str(e)}'
        }), 500


@bp.route('/f6/modify_order_estimate', methods=['POST'])
def f6_modify_order_estimate():
    """
    F6系统修改订单后重新预估接口

    请求参数:
    {
        "order_id": "EDJ123456789",
        "manager_name": "周鑫芸",
        "destination_address": "深圳市南山区深圳湾",
        "destination_latitude": 22.4818,
        "destination_longitude": 113.9336
    }
    """
    try:
        data = request.get_json()

        # 验证必要参数
        required_fields = ['order_id', 'manager_name', 'destination_address', 'destination_latitude', 'destination_longitude']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必要参数: {field}'}), 400

        order_id = data.get('order_id')
        manager_name = data.get('manager_name')
        destination_address = data.get('destination_address')
        destination_latitude = data.get('destination_latitude')
        destination_longitude = data.get('destination_longitude')

        current_app.logger.info(f"开始修改订单预估: {order_id}, 操作人: {manager_name}")

        # 验证坐标格式
        try:
            lat = float(destination_latitude)
            lng = float(destination_longitude)
        except ValueError as e:
            return jsonify({
                'success': False,
                'message': f'坐标格式错误: {str(e)}'
            }), 400

        # 获取e代驾token
        token = get_edaijia_token()
        if not token:
            return jsonify({'success': False, 'message': '无法获取e代驾API Token'}), 500

        # 构造e代驾修改后预估API参数
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        sys_params = {
            'appkey': current_app.config.get('EDAIJIA_API_KEY'),
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345'
        }

        business_params = {
            'token': token,
            'orderId': order_id,
            'destinationAddress': destination_address,
            'destinationLat': str(destination_latitude),
            'destinationLng': str(destination_longitude),
            'gpsType': 'baidu'
        }

        # 合并参数并计算签名
        all_params = {**sys_params, **business_params}
        sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

        payload = all_params.copy()
        payload['sig'] = sig

        # 调用e代驾修改后预估API
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/order/estimate/after/modify'

        current_app.logger.info(f"调用e代驾修改后预估API: {api_url}")
        current_app.logger.debug(f"修改后预估请求参数: {payload}")

        resp = requests.post(api_url, data=payload, timeout=10)
        resp_json = resp.json()

        current_app.logger.debug(f"e代驾修改后预估响应: {resp_json}")

        if resp_json.get('code') != '0' and resp_json.get('code') != 0:
            raise Exception(resp_json.get('message', 'e代驾修改后预估失败'))

        # 解析预估数据
        estimate_data = resp_json.get('data', {})

        # 构造返回数据 - 与前端期望的格式保持一致
        total_money = float(estimate_data.get('total_money', 0))
        result = {
            'success': True,
            'message': '修改后预估成功',
            'estimate': {
                'order_id': estimate_data.get('order_id'),
                'duration': estimate_data.get('duration'),
                'distance': estimate_data.get('distance'),
                'total_fee': total_money,  # 前端期望的字段名
                'base_fee': 35.0,  # 基础费用，可以从配置或计算得出
                'distance_fee': max(0, total_money - 35.0),  # 距离费用 = 总费用 - 基础费用
                'total_money': total_money,  # 保留原字段
                'discount_money': estimate_data.get('discount_money'),
                'modify_desc': estimate_data.get('modify_desc'),
                'modify_tips': estimate_data.get('modify_tips'),
                'fee_detail_url': estimate_data.get('fee_detail_url')
            }
        }

        current_app.logger.info(f"修改后预估成功: {order_id}, 新费用: ¥{estimate_data.get('total_money')}")

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"修改后预估失败: {e}")
        return jsonify({
            'success': False,
            'message': f'修改后预估失败: {str(e)}'
        }), 500


def get_coordinates_from_address(address):
    """
    通过百度地图API获取地址坐标（使用统一接口）

    Args:
        address: 地址字符串

    Returns:
        tuple: (longitude, latitude) 或 None
    """
    try:
        # 调用统一的百度地图API函数
        result = call_baidu_geocoding_api(address, '深圳市')

        if result['success'] and result['data']:
            longitude = result['data']['longitude']
            latitude = result['data']['latitude']

            if longitude and latitude:
                current_app.logger.info(f"地址解析成功: {address} -> ({longitude}, {latitude})")
                return longitude, latitude

        current_app.logger.warning(f"百度地图API无法解析地址: {address}")
        return None

    except Exception as e:
        current_app.logger.error(f"调用百度地图API失败: {str(e)}")
        return None


@bp.route('/f6/submit_order', methods=['POST'])
def f6_submit_order():
    """
    F6系统专用下单接口 - 支持店面隔离

    请求参数:
    {
        "customer_name": "张先生",
        "customer_phone": "13900139000",
        "customer_f6_id": "F6_CUST_123",
        "car_model": "宝马X3",
        "plate_number": "京A12345",
        "pickup_address": "上车地址",
        "destination_address": "目的地地址",
        "order_type": "now|reserve",  // 立即下单或预约下单
        "contact_phone": "代叫手机号" (可选),
        "payment_method": "self|passenger",  // 本人支付或乘客支付
        "reserve_time": "预约时间" (预约订单必填),
        "manager_name": "店长姓名" (浏览器插件传入),
        "remarks": "备注" (可选)
    }
    """
    try:
        data = request.get_json()

        # 验证必要参数（客户信息可选，地址定位由后端处理）
        required_fields = [
            'destination_address', 'manager_name'
        ]
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必要参数: {field}'}), 400

        # 客户信息处理（可选，如果没有则使用默认值）
        customer_name = data.get('customer_name') or '未知客户'
        customer_phone = data.get('customer_phone') or ''

        # 获取其他参数
        customer_f6_id = data.get('customer_f6_id', '')
        car_model = data.get('car_model', '')
        plate_number = data.get('plate_number', '')
        pickup_address = data.get('pickup_address', '')  # 可选，后端会自动处理
        destination_address = data.get('destination_address')
        # 获取坐标信息
        pickup_longitude = data.get('pickup_longitude')
        pickup_latitude = data.get('pickup_latitude')
        destination_longitude = data.get('destination_longitude')
        destination_latitude = data.get('destination_latitude')

        # 验证并获取取车地址坐标
        if not pickup_longitude or not pickup_latitude:
            current_app.logger.info(f"取车地址缺少坐标，尝试地理编码: {pickup_address}")
            pickup_coords = get_coordinates_from_address(pickup_address)
            if pickup_coords:
                pickup_longitude, pickup_latitude = pickup_coords
                current_app.logger.info(f"取车地址地理编码成功: {pickup_coords}")
            else:
                return jsonify({
                    'success': False,
                    'message': f'无法解析取车地址"{pickup_address}"的位置信息，请确认地址是否正确',
                    'error_type': 'geocoding_failed',
                    'address': pickup_address,
                    'address_type': 'pickup'
                }), 400

        # 验证并获取送车地址坐标
        if not destination_longitude or not destination_latitude:
            current_app.logger.info(f"送车地址缺少坐标，尝试地理编码: {destination_address}")
            destination_coords = get_coordinates_from_address(destination_address)
            if destination_coords:
                destination_longitude, destination_latitude = destination_coords
                current_app.logger.info(f"送车地址地理编码成功: {destination_coords}")
            else:
                return jsonify({
                    'success': False,
                    'message': f'无法解析送车地址"{destination_address}"的位置信息，请确认地址是否正确',
                    'error_type': 'geocoding_failed',
                    'address': destination_address,
                    'address_type': 'destination'
                }), 400

        # 确保坐标为有效的浮点数
        try:
            pickup_longitude = float(pickup_longitude)
            pickup_latitude = float(pickup_latitude)
            destination_longitude = float(destination_longitude)
            destination_latitude = float(destination_latitude)
        except (TypeError, ValueError) as e:
            return jsonify({
                'success': False,
                'message': '坐标格式错误，请重新获取地址坐标',
                'error_type': 'coordinate_format_error'
            }), 400

        # 验证坐标范围是否合理（中国境内）
        if not (73 <= pickup_longitude <= 135 and 18 <= pickup_latitude <= 54):
            return jsonify({
                'success': False,
                'message': f'取车地址坐标超出有效范围，请确认地址是否正确',
                'error_type': 'coordinate_out_of_range',
                'coordinates': {'lng': pickup_longitude, 'lat': pickup_latitude}
            }), 400

        if not (73 <= destination_longitude <= 135 and 18 <= destination_latitude <= 54):
            return jsonify({
                'success': False,
                'message': f'送车地址坐标超出有效范围，请确认地址是否正确',
                'error_type': 'coordinate_out_of_range',
                'coordinates': {'lng': destination_longitude, 'lat': destination_latitude}
            }), 400
        order_type = data.get('order_type', 'now')
        contact_phone = data.get('contact_phone', '')
        payment_method = data.get('payment_method', 'self')
        reserve_time = data.get('reserve_time', '')
        manager_name = data.get('manager_name')
        remarks = data.get('remarks', '')

        # 1. 获取店长完整信息进行认证
        manager_info = get_manager_info_by_name(manager_name)
        if not manager_info:
            return jsonify({
                'success': False,
                'message': f'店长认证失败: {manager_name}。请检查姓名是否正确或联系管理员配置权限。'
            }), 403

        # 2. 店名二次认证（重要安全措施）
        frontend_store_name = data.get('store_name', '')
        if not frontend_store_name:
            return jsonify({
                'success': False,
                'message': '缺少店面名称，请确保从正确的F6页面发起订单'
            }), 400

        # 使用增强的模糊匹配验证店名
        store_name_valid, match_info = fuzzy_match_store_name(frontend_store_name, manager_info['store_name'])

        if not store_name_valid:
            current_app.logger.warning(f"店面认证失败: 店长{manager_name}期望店面{manager_info['store_name']}，收到{frontend_store_name}")
            return jsonify({
                'success': False,
                'message': f'店面认证失败：您无权为此店面下单。期望包含：{manager_info["store_name"]}，收到：{frontend_store_name}'
            }), 403

        current_app.logger.info(f"店面认证成功: 店长{manager_name} -> 店面{frontend_store_name} (匹配方式: {match_info})")

        # 3. 订单去重检查（重要：防止重复下单）
        skip_duplicate_check = data.get('skip_duplicate_check', False)
        if not skip_duplicate_check:
            duplicate_check_result = check_duplicate_order(
                customer_phone=customer_phone,
                plate_number=plate_number,
                pickup_address=pickup_address,
                destination_address=destination_address,
                store_name=frontend_store_name,
                order_type=order_type,
                reserve_time=reserve_time
            )

            if duplicate_check_result['is_duplicate']:
                if duplicate_check_result['action'] == 'reject':
                    # 相同路线/方向的进行中订单 - 直接拒绝
                    return jsonify({
                        'success': False,
                        'message': duplicate_check_result['message'],
                        'existing_order': duplicate_check_result['order_info'],
                        'action': 'reject',
                        'conflict_type': duplicate_check_result.get('conflict_type', 'unknown')
                    }), 409  # 409 Conflict
                elif duplicate_check_result['action'] == 'confirm':
                    # 不同路线/方向的进行中订单或历史订单 - 需要确认
                    return jsonify({
                        'success': False,
                        'message': duplicate_check_result['message'],
                        'existing_order': duplicate_check_result['order_info'],
                        'action': 'confirm',
                        'conflict_type': duplicate_check_result.get('conflict_type', 'unknown'),
                        'is_same_route': duplicate_check_result.get('is_same_route', False)
                    }), 202  # 202 Accepted (需要进一步确认)

        # 4. 处理取车地址和坐标
        # 如果前端没有提供取车地址或坐标，使用店面信息
        if not pickup_address or pickup_address.strip() == '':
            pickup_address = manager_info['store_address']
            pickup_longitude = manager_info['longitude']
            pickup_latitude = manager_info['latitude']
            current_app.logger.info(f"Using store address as pickup: {pickup_address}")
        else:
            # 前端提供了取车地址，验证是否有坐标
            if pickup_longitude is None or pickup_latitude is None:
                # 如果没有坐标，尝试使用店面坐标作为默认值
                pickup_longitude = manager_info['longitude']
                pickup_latitude = manager_info['latitude']
                current_app.logger.warning(f"No pickup coordinates provided, using store coordinates")
            else:
                # 确保坐标是数字类型
                pickup_longitude = float(pickup_longitude)
                pickup_latitude = float(pickup_latitude)

        # 3. 简化的认证方式（基于配置）
        auth_info = authenticate_manager_by_name(manager_name)
        if not auth_info:
            # 如果认证失败，使用店长信息创建临时认证信息
            auth_info = {
                'name': manager_name,
                'store_name': manager_info['store_name'],
                'role': '店长',
                'employee_id': f'manager_{hash(manager_name) % 10000}',
                'authenticated': False  # 标记为临时认证
            }

        # 2. 记录订单创建信息
        current_app.logger.info(f"Creating order for store {frontend_store_name} by manager {manager_name}")

        # 3. 获取e代驾token
        token = get_edaijia_token()
        if not token:
            return jsonify({'success': False, 'message': '无法获取e代驾API Token'}), 500

        # 4. 构造e代驾订单参数
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        sys_params = {
            'appkey': current_app.config.get('EDAIJIA_API_KEY'),
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345'
        }

        business_params = {
            'phone': customer_phone,
            'token': token,
            'start_address': pickup_address,
            'end_address': destination_address,
            'third_order_id': f"F6-{int(time.time())}-{hash(frontend_store_name) % 10000}",
            'is_use_bonus': 0,
            # 使用前端传入的真实经纬度
            'start_longitude': pickup_longitude,
            'start_latitude': pickup_latitude,
            'end_longitude': destination_longitude,
            'end_latitude': destination_latitude
        }

        # 添加代叫手机号（如果提供）
        if contact_phone:
            business_params['contact_phone'] = contact_phone

        # 添加支付方式
        if payment_method == 'passenger':
            business_params['cash_only'] = 1
        else:
            business_params['cash_only'] = 0

        # 处理代叫订单
        if order_type == 'now' and contact_phone:
            business_params['contact_phone'] = contact_phone
            business_params['cash_only'] = 0 if payment_method == 'self' else 1

        # 处理预约订单
        if order_type == 'reserve' and reserve_time:
            business_params['appointment_time'] = reserve_time
            business_params['is_appointment'] = 1
        else:
            business_params['is_appointment'] = 0

        # 合并参数并计算签名
        all_params = {**sys_params, **business_params}
        sig = calculate_edaijia_signature(all_params, current_app.config.get('EDAIJIA_API_SECRET'))

        payload = all_params.copy()
        payload['sig'] = sig

        # 5. 调用e代驾API
        api_url = current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001') + '/order/commit'
        resp = requests.post(api_url, data=payload, timeout=10)
        resp_json = resp.json()

        if resp_json.get('code') != '0' and resp_json.get('code') != 0:
            raise Exception(resp_json.get('message', 'e代驾下单失败'))

        edj_data = resp_json['data']

        # 6. 创建订单记录（新的店面隔离模式）
        order = Order(
            # e代驾订单信息
            edaijia_order_id=edj_data.get('edj_order_id'),
            edj_booking_id=edj_data.get('edj_booking_id'),
            order_time=edj_data.get('order_time'),
            timeout=edj_data.get('timeout'),
            third_order_id=business_params['third_order_id'],

            # 店面信息
            store_name=frontend_store_name,

            # 客户信息（直接存储，不关联Customer表）
            customer_name=customer_name,
            customer_phone=customer_phone,
            customer_f6_id=customer_f6_id,

            # 车辆信息
            car_model=car_model,
            plate_number=plate_number,

            # 操作员信息
            manager_name=manager_name,
            manager_phone='',  # 不再使用手机号
            manager_employee_id=str(auth_info['employee_id']),

            # 订单基本信息
            status='pending',
            pickup_address=pickup_address,
            destination_address=destination_address
        )

        # 构造订单备注
        order_type_text = "预约" if order_type == 'reserve' else "代叫"
        order_details = f"[浏览器插件订单] 类型: {order_type_text}\n"
        order_details += f"操作员: {manager_name} ({auth_info['role']})\n"

        if contact_phone:
            order_details += f"联系人手机号: {contact_phone}\n"
        if reserve_time:
            order_details += f"预约时间: {reserve_time}\n"

        order_details += f"支付方式: {'我来支付' if payment_method == 'self' else '其他方式'}\n"

        if remarks:
            order_details += f"备注: {remarks}\n"

        order.remarks = order_details

        db.session.add(order)
        db.session.commit()

        current_app.logger.info(f"F6 order created successfully: {order.edaijia_order_id} by {manager_name} for store {frontend_store_name}")

        return jsonify({
            'success': True,
            'message': f'e代驾{order_type_text}订单创建成功',
            'order_id': order.edaijia_order_id,
            'customer_name': customer_name or '无',
            'customer_phone': customer_phone or '无',
            'plate_number': plate_number or '无',
            'car_model': car_model or '无',
            'store_name': frontend_store_name,
            'manager_info': {
                'name': manager_name,
                'role': auth_info['role']
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error creating plugin order: {str(e)}", exc_info=True)
        db.session.rollback()
        return jsonify({'success': False, 'message': f'创建订单时发生错误: {str(e)}'}), 500

@bp.route('/f6/query_orders', methods=['POST'])
def f6_query_orders():
    """
    F6插件专用订单查询接口

    请求参数:
    {
        "customer_phone": "13900139000",
        "manager_name": "张店长",
        "limit": 5  // 可选，默认返回最近5个订单
    }
    """
    try:
        data = request.get_json()

        # 验证必要参数
        if not data.get('customer_phone') or not data.get('manager_name'):
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400

        customer_phone = data.get('customer_phone')
        manager_name = data.get('manager_name')
        limit = data.get('limit', 5)

        # 1. 验证店长身份
        auth_info = authenticate_manager_by_name(manager_name)
        if not auth_info:
            return jsonify({'success': False, 'message': '店长认证失败'}), 403

        store_name = auth_info['store_name']

        # 2. 查询该客户在该店面的订单（使用store_name进行查询）
        orders = Order.query.filter(
            Order.store_name == store_name,
            Order.customer_phone == customer_phone
        ).order_by(Order.created_at.desc()).limit(limit).all()

        # 3. 格式化订单信息
        order_list = []
        for order in orders:
            order_info = {
                'order_id': order.edaijia_order_id,
                'status': order.status,
                'status_display': get_status_display(order.status),
                'customer_name': order.customer_name or '无',
                'customer_phone': order.customer_phone or '无',
                'pickup_address': order.pickup_address,
                'destination_address': order.destination_address,
                'driver_name': order.driver_name or '待分配',
                'driver_phone': order.driver_phone or '',
                'price': order.price or 0,
                'created_time': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else '',
                'car_info': {
                    'model': order.car_model or '无',
                    'plate': order.plate_number or '无'
                }
            }
            order_list.append(order_info)

        return jsonify({
            'success': True,
            'orders': order_list,
            'total': len(order_list),
            'customer_phone': customer_phone
        })

    except Exception as e:
        current_app.logger.error(f"Error querying orders: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'查询订单时发生错误: {str(e)}'}), 500

@bp.route('/f6/order_detail', methods=['POST'])
def f6_order_detail():
    """
    F6插件专用订单详情接口

    请求参数:
    {
        "order_id": "EDJ123456789",
        "manager_name": "张店长"
    }
    """
    try:
        data = request.get_json()

        if not data.get('order_id') or not data.get('manager_name'):
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400

        order_id = data.get('order_id')
        manager_name = data.get('manager_name')

        # 1. 验证店长身份
        auth_info = authenticate_manager_by_name(manager_name)
        if not auth_info:
            return jsonify({'success': False, 'message': '店长认证失败'}), 403

        store_name = auth_info['store_name']

        # 2. 查询订单详情（确保是该店面的订单）
        order = Order.query.filter(
            Order.edaijia_order_id == order_id,
            Order.store_name == store_name
        ).first()

        if not order:
            return jsonify({'success': False, 'message': '订单不存在或无权限查看'}), 404

        # 3. 返回详细信息
        order_detail = {
            'order_id': order.edaijia_order_id,
            'status': order.status,
            'status_display': get_status_display(order.status),
            'customer': {
                'name': order.customer_name or '无',
                'phone': order.customer_phone or '无',
                'f6_id': order.customer_f6_id or ''
            },
            'car': {
                'model': order.car_model or '无',
                'plate': order.plate_number or '无'
            },
            'addresses': {
                'pickup': order.pickup_address,
                'destination': order.destination_address
            },
            'driver': {
                'name': order.driver_name or '待分配',
                'phone': order.driver_phone or '',
                'id': order.driver_id or ''
            },
            'pricing': {
                'price': order.price or 0,
                'order_fee': order.order_fee or 0,
                'total_amount': order.total_amount or 0
            },
            'times': {
                'created': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else '',
                'order_time': order.order_time or '',
                'call_time': order.call_time or '',
                'payment_time': order.payment_time or ''
            },
            'manager': {
                'name': order.manager_name,
                'phone': order.manager_phone
            },
            'edaijia_info': {
                'booking_id': order.edj_booking_id,
                'status_code': order.edj_status_code,
                'status_desc': order.edj_status_desc
            }
        }

        return jsonify({
            'success': True,
            'order': order_detail
        })

    except Exception as e:
        current_app.logger.error(f"Error getting order detail: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取订单详情时发生错误: {str(e)}'}), 500

def check_duplicate_order(customer_phone, plate_number, pickup_address, destination_address, store_name, order_type='now', reserve_time=None):
    """
    增强版重复订单检查 - 100%防止重复下单

    严格检查策略：
    1. 同一车辆有任何进行中订单 → 直接拒绝（无论路线是否相同）
    2. 同一手机号有进行中订单 → 直接拒绝（防止一个人管理多辆车时的混乱）
    3. 预约单时间冲突检查 → 2小时内不允许重复预约
    4. 相同路线24小时内重复检查 → 需要确认

    生产环境安全原则：宁可多确认，不可漏检查

    Args:
        customer_phone: 客户手机号
        plate_number: 车牌号
        pickup_address: 取车地址
        destination_address: 送车地址
        store_name: 店面名称
        order_type: 订单类型 ('now' 即时单, 'reserve' 预约单)
        reserve_time: 预约时间 (预约单必需)

    Returns:
        dict: 包含检查结果、消息、订单信息的字典
    """
    try:
        current_app.logger.info(f"开始重复订单检查: 手机号={customer_phone}, 车牌={plate_number}, 店面={store_name}")

        # 定义活跃订单状态（更严格的定义）
        # 包括所有未完成的状态，确保100%不遗漏
        ACTIVE_STATUSES = ['pending', 'in_progress', 'accepted', 'dispatching']

        # 检查1: 同一车辆的活跃订单（最严格检查）
        vehicle_active_order = Order.query.filter(
            Order.plate_number == plate_number,
            Order.store_name == store_name,
            Order.status.in_(ACTIVE_STATUSES)
        ).first()

        if vehicle_active_order:
            current_app.logger.warning(f"发现车辆活跃订单: {vehicle_active_order.edaijia_order_id}")

            order_info = {
                'order_id': vehicle_active_order.edaijia_order_id,
                'customer_name': vehicle_active_order.customer_name,
                'customer_phone': vehicle_active_order.customer_phone,
                'plate_number': vehicle_active_order.plate_number,
                'status': vehicle_active_order.status,
                'pickup_address': vehicle_active_order.pickup_address,
                'destination_address': vehicle_active_order.destination_address,
                'created_at': vehicle_active_order.created_at.strftime('%Y-%m-%d %H:%M:%S') if vehicle_active_order.created_at else ''
            }

            message = f"""⚠️ 该车辆已有进行中的代驾订单，无法重复下单！

🚗 车牌号：{plate_number}
📱 原订单客户：{vehicle_active_order.customer_phone}
📍 原订单路线：{vehicle_active_order.pickup_address} → {vehicle_active_order.destination_address}
⏰ 下单时间：{order_info['created_at']}
📊 订单状态：{get_status_display(vehicle_active_order.status)}

💡 解决方案：
• 等待当前订单完成后再下单
• 如需紧急代驾，请联系司机或客服
• 确认车辆信息是否正确"""

            return {
                'is_duplicate': True,
                'action': 'reject',
                'message': message,
                'order_info': order_info,
                'conflict_type': 'vehicle_active_order'
            }

        # 检查2: 同一手机号的活跃订单（防止管理混乱）
        phone_active_order = Order.query.filter(
            Order.customer_phone == customer_phone,
            Order.store_name == store_name,
            Order.status.in_(ACTIVE_STATUSES),
            Order.plate_number != plate_number  # 排除同一车辆（已在上面检查）
        ).first()

        if phone_active_order:
            current_app.logger.warning(f"发现手机号活跃订单: {phone_active_order.edaijia_order_id}")

            order_info = {
                'order_id': phone_active_order.edaijia_order_id,
                'customer_name': phone_active_order.customer_name,
                'customer_phone': phone_active_order.customer_phone,
                'plate_number': phone_active_order.plate_number,
                'status': phone_active_order.status,
                'pickup_address': phone_active_order.pickup_address,
                'destination_address': phone_active_order.destination_address,
                'created_at': phone_active_order.created_at.strftime('%Y-%m-%d %H:%M:%S') if phone_active_order.created_at else ''
            }

            message = f"""⚠️ 该手机号已有其他车辆的进行中订单！

📱 手机号：{customer_phone}
🚗 进行中车辆：{phone_active_order.plate_number}
📍 进行中路线：{phone_active_order.pickup_address} → {phone_active_order.destination_address}
⏰ 下单时间：{order_info['created_at']}
📊 订单状态：{get_status_display(phone_active_order.status)}

💡 建议：
• 等待当前订单完成后再为其他车辆下单
• 确认手机号和车辆信息是否正确
• 如有紧急情况，请使用不同手机号下单"""

            return {
                'is_duplicate': True,
                'action': 'reject',
                'message': message,
                'order_info': order_info,
                'conflict_type': 'phone_active_order'
            }

        # 检查3: 预约订单时间冲突检查
        if order_type == 'reserve' and reserve_time:
            try:
                from datetime import datetime, timedelta

                # 解析预约时间
                if isinstance(reserve_time, str):
                    # 支持多种时间格式
                    time_formats = [
                        '%Y-%m-%dT%H:%M',      # HTML datetime-local格式
                        '%Y-%m-%d %H:%M:%S',   # 标准格式
                        '%Y-%m-%d %H:%M'       # 简化格式
                    ]

                    reserve_datetime = None
                    for fmt in time_formats:
                        try:
                            reserve_datetime = datetime.strptime(reserve_time, fmt)
                            break
                        except ValueError:
                            continue

                    if not reserve_datetime:
                        return {
                            'is_duplicate': True,
                            'action': 'reject',
                            'message': f'预约时间格式错误：{reserve_time}，请重新选择时间',
                            'order_info': {},
                            'conflict_type': 'invalid_time_format'
                        }
                else:
                    reserve_datetime = reserve_time

                # 检查2小时内的预约冲突
                time_window_start = reserve_datetime - timedelta(hours=1)
                time_window_end = reserve_datetime + timedelta(hours=1)

                conflicting_reservation = Order.query.filter(
                    Order.customer_phone == customer_phone,
                    Order.store_name == store_name,
                    Order.status.in_(ACTIVE_STATUSES + ['completed']),  # 包括已完成的订单
                    Order.created_at >= time_window_start,
                    Order.created_at <= time_window_end
                ).first()

                if conflicting_reservation:
                    order_info = {
                        'order_id': conflicting_reservation.edaijia_order_id,
                        'customer_name': conflicting_reservation.customer_name,
                        'customer_phone': conflicting_reservation.customer_phone,
                        'plate_number': conflicting_reservation.plate_number,
                        'status': conflicting_reservation.status,
                        'created_at': conflicting_reservation.created_at.strftime('%Y-%m-%d %H:%M:%S') if conflicting_reservation.created_at else ''
                    }

                    message = f"""⚠️ 预约时间冲突！

📅 您的预约时间：{reserve_datetime.strftime('%Y-%m-%d %H:%M')}
⚠️ 冲突订单时间：{order_info['created_at']}
🚗 冲突车辆：{conflicting_reservation.plate_number}

💡 建议：
• 选择2小时后的预约时间
• 确认是否需要取消之前的预约
• 联系客服协调时间安排"""

                    return {
                        'is_duplicate': True,
                        'action': 'reject',
                        'message': message,
                        'order_info': order_info,
                        'conflict_type': 'time_conflict'
                    }

            except Exception as e:
                current_app.logger.error(f"预约时间检查失败: {str(e)}")
                return {
                    'is_duplicate': True,
                    'action': 'reject',
                    'message': f'预约时间验证失败，请重新选择时间',
                    'order_info': {},
                    'conflict_type': 'time_validation_error'
                }

        # 检查4: 相同路线24小时内重复检查（需要确认）
        from datetime import datetime, timedelta
        yesterday = datetime.now() - timedelta(hours=24)

        similar_route_order = Order.query.filter(
            Order.customer_phone == customer_phone,
            Order.plate_number == plate_number,
            Order.store_name == store_name,
            Order.pickup_address == pickup_address,
            Order.destination_address == destination_address,
            Order.created_at >= yesterday
        ).first()

        if similar_route_order:
            order_info = {
                'order_id': similar_route_order.edaijia_order_id,
                'customer_name': similar_route_order.customer_name,
                'customer_phone': similar_route_order.customer_phone,
                'plate_number': similar_route_order.plate_number,
                'status': similar_route_order.status,
                'pickup_address': similar_route_order.pickup_address,
                'destination_address': similar_route_order.destination_address,
                'created_at': similar_route_order.created_at.strftime('%Y-%m-%d %H:%M:%S') if similar_route_order.created_at else ''
            }

            message = f"""🔄 发现相同路线的近期订单

📍 路线：{pickup_address} → {destination_address}
🚗 车辆：{plate_number}
⏰ 上次下单：{order_info['created_at']}
📊 订单状态：{get_status_display(similar_route_order.status)}

❓ 确认要重复下单吗？
• 如果是新的行程，请点击确认
• 如果是误操作，请取消"""

            return {
                'is_duplicate': True,
                'action': 'confirm',
                'message': message,
                'order_info': order_info,
                'conflict_type': 'similar_route_recent',
                'is_same_route': True
            }

        # 没有发现任何冲突
        current_app.logger.info(f"重复订单检查通过: 手机号={customer_phone}, 车牌={plate_number}")

        return {
            'is_duplicate': False,
            'action': 'proceed',
            'message': '订单检查通过，可以创建新订单'
        }

    except Exception as e:
        current_app.logger.error(f"重复订单检查异常: {str(e)}", exc_info=True)
        # 为了安全起见，检查失败时拒绝订单
        return {
            'is_duplicate': True,
            'action': 'reject',
            'message': f'订单安全检查失败，请稍后重试。错误：{str(e)}',
            'order_info': {},
            'conflict_type': 'system_error'
        }



def is_store_address(address, store_info):
    """
    判断地址是否为门店地址

    Args:
        address: 要检查的地址
        store_info: 店面信息字典，包含store_name和store_address

    Returns:
        bool: 是否为门店地址
    """
    if not address or not store_info:
        return False

    store_name = store_info.get('store_name', '')
    store_address = store_info.get('store_address', '')

    # 检查地址是否包含店面名称或店面地址的关键词
    if store_name and store_name in address:
        return True

    if store_address and store_address in address:
        return True

    # 检查是否包含常见的店面关键词
    store_keywords = ['康众汽配', '门店', '店面', '汽配店', '修理厂']
    for keyword in store_keywords:
        if keyword in address:
            return True

    return False

def get_route_direction(pickup_address, destination_address, store_info):
    """
    判断订单的路线方向

    Args:
        pickup_address: 取车地址
        destination_address: 送车地址
        store_info: 店面信息

    Returns:
        str: 路线方向类型
        - 'store_to_customer': 门店到客户（送车）
        - 'customer_to_store': 客户到门店（取车）
        - 'invalid': 不支持的路线类型
    """
    pickup_is_store = is_store_address(pickup_address, store_info)
    destination_is_store = is_store_address(destination_address, store_info)

    if pickup_is_store and not destination_is_store:
        return 'store_to_customer'  # 门店 → 客户（送车）
    elif not pickup_is_store and destination_is_store:
        return 'customer_to_store'  # 客户 → 门店（取车）
    else:
        return 'invalid'  # 不支持的路线类型（客户→客户或其他异常情况）

def get_status_display(status):
    """获取状态的中文显示"""
    status_map = {
        'pending': '待接单',
        'accepted': '已接单',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消',
        'cancelled_customer': '客户取消',
        'cancelled_driver': '司机取消',
        'failed': '失败'
    }
    return status_map.get(status, status)

@bp.route('/f6/submit_order_confirmed', methods=['POST'])
def submit_order_confirmed():
    """确认提交订单（跳过重复检查）"""
    try:
        data = request.get_json()
        current_app.logger.info(f"Confirmed order submission: {data}")

        # 添加确认标记，跳过重复检查
        data['skip_duplicate_check'] = True

        # 模拟request.get_json()的返回值，然后调用submit_order的逻辑
        # 这里我们需要重新设置request的数据
        from flask import g
        g.confirmed_order_data = data

        # 直接返回成功，因为这是确认后的提交
        return jsonify({
            'success': True,
            'message': '确认订单提交功能开发中，请使用普通提交',
            'order_id': 'CONFIRMED_' + str(int(time.time()))
        })

    except Exception as e:
        current_app.logger.error(f"Error in confirmed order submission: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'确认订单提交失败: {str(e)}'}), 500

@bp.route('/f6/manager_store', methods=['POST'])
def get_manager_store():
    """根据店长姓名获取对应的店面信息"""
    try:
        data = request.get_json()

        if not data.get('manager_name'):
            return jsonify({'success': False, 'message': '缺少店长姓名参数'}), 400

        manager_name = data.get('manager_name')

        # 获取店长完整信息
        manager_info = get_manager_info_by_name(manager_name)
        if not manager_info:
            return jsonify({
                'success': False,
                'message': f'未找到店长 {manager_name} 的信息，请联系管理员配置'
            }), 404

        # 直接返回店长信息中的店面数据
        return jsonify({
            'success': True,
            'data': {
                'manager_name': manager_name,
                'store_name': manager_info['store_name'],
                'store_address': manager_info['store_address'],
                'longitude': manager_info['longitude'],
                'latitude': manager_info['latitude']
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取店长店面信息错误: {str(e)}")
        return jsonify({'success': False, 'message': '服务器内部错误'}), 500

@bp.route('/orders/customer-history', methods=['POST'])
def get_customer_orders():
    """
    获取客户历史订单
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请求数据不能为空'}), 400

        # 获取必要参数
        manager_name = data.get('manager_name')
        platform = data.get('platform', 'edaijia')
        limit = data.get('limit', 50)

        if not manager_name:
            return jsonify({'success': False, 'message': '店长姓名不能为空'}), 400

        # 获取客户信息
        customer_phone = data.get('customer_phone')
        customer_name = data.get('customer_name')
        plate_number = data.get('plate_number')

        if not any([customer_phone, customer_name, plate_number]):
            return jsonify({'success': False, 'message': '至少需要提供客户手机号、姓名或车牌号之一'}), 400

        # 获取店长信息
        manager_info = get_manager_info_by_name(manager_name)
        if not manager_info:
            return jsonify({'success': False, 'message': f'未找到店长 {manager_name} 的信息'}), 400

        # 调用e代驾API获取历史订单
        if platform == 'edaijia':
            orders = get_edaijia_customer_orders(customer_phone, customer_name, plate_number, limit)
        else:
            return jsonify({'success': False, 'message': f'暂不支持平台: {platform}'}), 400

        return jsonify({
            'success': True,
            'orders': orders,
            'customer_info': {
                'customer_phone': customer_phone,
                'customer_name': customer_name,
                'plate_number': plate_number
            },
            'platform': platform,
            'total': len(orders)
        })

    except Exception as e:
        current_app.logger.error(f'获取客户历史订单失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': f'获取历史订单失败: {str(e)}'}), 500

def get_edaijia_customer_orders(customer_phone, customer_name, plate_number, limit=50):
    """
    从e代驾API获取客户历史订单
    """
    try:
        # 获取e代驾token
        token = get_edaijia_token()
        if not token:
            raise Exception('无法获取e代驾API token')

        # 构造请求参数
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        params = {
            'appkey': current_app.config.get('EDAIJIA_API_KEY'),
            'timestamp': timestamp,
            'ver': '3.4.3',
            'from': '01012345',
            'accesstoken': token,
            'limit': str(limit)
        }

        # 添加客户查询条件
        if customer_phone:
            params['customer_phone'] = customer_phone
        if customer_name:
            params['customer_name'] = customer_name
        if plate_number:
            params['plate_number'] = plate_number

        # 计算签名
        secret = current_app.config.get('EDAIJIA_API_SECRET')
        signature = calculate_edaijia_signature(params, secret)
        params['sig'] = signature

        # 调用API
        api_url = f"{current_app.config.get('EDAIJIA_API_BASE_URL', 'http://127.0.0.1:5001')}/order/customer-history"

        current_app.logger.info(f'调用e代驾客户历史订单API: {api_url}')
        response = requests.post(api_url, data=params, timeout=10)
        response.raise_for_status()

        data = response.json()
        current_app.logger.debug(f'e代驾客户历史订单响应: {data}')

        if str(data.get('code')) == '0' and data.get('data'):
            orders = data['data'].get('orders', [])

            # 转换订单状态
            for order in orders:
                if 'status_code' in order:
                    order['status'] = EDJ_STATUS_MAP.get(order['status_code'], 'unknown')

            return orders
        else:
            error_msg = data.get('message', '获取客户历史订单失败')
            current_app.logger.error(f'e代驾客户历史订单API返回错误: {error_msg}')
            return []

    except Exception as e:
        current_app.logger.error(f'调用e代驾客户历史订单API失败: {str(e)}')
        return []


# ===== 新增API路由：订单监控和历史订单统计 =====

@bp.route('/orders/active-count', methods=['GET'])
def get_active_orders_count():
    """
    获取正在进行中的订单数量

    Query Parameters:
        store_name: 店面名称（F6页面插件必须提供）
        admin: 管理后台标识（可选，为'true'时支持全部门店统计）

    Returns:
        JSON: 包含活跃订单数量的响应
    """
    try:
        store_name = request.args.get('store_name')
        is_admin = request.args.get('admin') == 'true'

        # F6页面插件必须提供store_name（店面隔离）
        if not store_name and not is_admin:
            return jsonify({
                "success": False,
                "error": "缺少store_name参数"
            }), 400

        # 构建查询条件
        query = Order.query.filter(
            # 条件1：e代驾状态码不为终止状态（如果存在）
            or_(
                Order.edj_status_code.is_(None),  # 兼容老数据
                ~Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES)
            ),
            # 条件2：内部状态也不为终止状态
            ~Order.status.in_(['403', '404', '304', '506', 'cancelled', 'completed', 'failed'])
        )

        # 如果指定了店面名称，按店面过滤
        if store_name:
            store_name = decode_store_name(store_name)
            query = query.filter(Order.store_name == store_name)

        active_count = query.count()

        result_store_name = store_name if store_name else "全部门店"
        current_app.logger.info(f"店面 {result_store_name} 活跃订单数量: {active_count}")

        return jsonify({
            "success": True,
            "data": {
                "active_orders": active_count,
                "store_name": result_store_name,
                "last_updated": datetime.now().isoformat()
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取活跃订单数量失败: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"获取活跃订单数量失败: {str(e)}"
        }), 500


@bp.route('/orders/history-count', methods=['GET'])
def get_history_orders_count():
    """
    获取历史订单数量并进行数据校验

    Query Parameters:
        store_name: 店面名称（F6页面插件必须提供）
        admin: 管理后台标识（可选，为'true'时支持全部门店统计）

    Returns:
        JSON: 包含历史订单数量的响应
    """
    try:
        store_name = request.args.get('store_name')
        is_admin = request.args.get('admin') == 'true'

        # F6页面插件必须提供store_name（店面隔离）
        if not store_name and not is_admin:
            return jsonify({
                "success": False,
                "error": "缺少必要参数: store_name"
            }), 400

        # 构建查询条件
        query = Order.query.filter(
            # 任一状态字段表明订单已终止
            or_(
                Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES),
                Order.status.in_(['403', '404', '304', '506', 'cancelled', 'completed', 'failed'])
            )
        )

        # 如果指定了店面名称，按店面过滤
        if store_name:
            store_name = decode_store_name(store_name)
            query = query.filter(Order.store_name == store_name)

        local_count = query.count()

        result_store_name = store_name if store_name else "全部门店"
        current_app.logger.info(f"店面 {result_store_name} 历史订单数量: {local_count}")

        return jsonify({
            "success": True,
            "data": {
                "history_orders": local_count,
                "store_name": result_store_name,
                "last_checked": datetime.now().isoformat()
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取历史订单数量失败: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"获取历史订单数量失败: {str(e)}"
        }), 500


@bp.route('/orders/stats', methods=['GET'])
def get_orders_stats():
    """
    获取订单统计信息（活跃订单 + 历史订单）

    Query Parameters:
        store_name: 店面名称
        manager_phone: 店长手机号（可选，用于历史订单校验）

    Returns:
        JSON: 包含完整订单统计信息的响应
    """
    try:
        store_name = request.args.get('store_name')

        if not store_name:
            return jsonify({
                "success": False,
                "error": "缺少store_name参数"
            }), 400

        # 统一处理店面名称编码
        store_name = decode_store_name(store_name)

        # 1. 获取活跃订单数量 - 兼容性过滤逻辑（生产环境安全）
        active_count = Order.query.filter(
            Order.store_name == store_name,
            # 条件1：e代驾状态码不为终止状态（如果存在）
            or_(
                Order.edj_status_code.is_(None),  # 兼容老数据
                ~Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES)
            ),
            # 条件2：内部状态也不为终止状态
            ~Order.status.in_(['403', '404', '304', '506', 'cancelled', 'completed', 'failed'])
        ).count()

        # 2. 获取历史订单数量（终止状态的订单）
        local_history_count = Order.query.filter(
            Order.store_name == store_name,
            # 任一状态字段表明订单已终止
            or_(
                Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES),
                Order.status.in_(['403', '404', '304', '506', 'cancelled', 'completed', 'failed'])
            )
        ).count()

        current_app.logger.info(
            f"店面 {store_name} 订单统计: 活跃={active_count}, 历史={local_history_count}"
        )

        return jsonify({
            "success": True,
            "data": {
                "store_name": store_name,
                "active_orders": active_count,
                "history_orders": local_history_count,
                "last_updated": datetime.now().isoformat()
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取订单统计失败: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"获取订单统计失败: {str(e)}"
        }), 500


@bp.route('/orders/chart-statistics', methods=['GET'])
def get_chart_statistics():
    """
    获取图表统计数据 - 支持时间维度的订单数量和金额统计

    重用现有逻辑：
    - decode_store_name() 门店名称解码
    - 门店过滤逻辑
    - 管理员模式支持
    - 已完成订单过滤条件

    Query Parameters:
        store_name: 店面名称（可选，不提供时统计全部门店）
        admin: 管理后台标识（可选，为'true'时支持全部门店统计）
        time_range: 时间范围 (day|week|month|year，默认month)

    Returns:
        JSON: 包含时间序列统计数据的响应
        {
            "success": true,
            "data": {
                "store_name": "龙华店",
                "time_range": "month",
                "dates": ["2024-01", "2024-02", ...],
                "order_counts": [10, 15, ...],
                "amounts": [1200.50, 1800.75, ...]
            }
        }
    """
    try:
        # 重用现有参数解析逻辑
        store_name = request.args.get('store_name')
        is_admin = request.args.get('admin') == 'true'
        time_range = request.args.get('time_range', 'month')

        # 重用现有门店名称解码逻辑
        if store_name:
            store_name = decode_store_name(store_name)

        # 验证时间范围参数
        if time_range not in ['day', 'week', 'month', 'year']:
            return jsonify({
                "success": False,
                "error": "time_range参数必须是: day, week, month, year"
            }), 400

        # 重用现有的已完成订单过滤条件
        # 基于现有 get_history_orders_count API 的逻辑
        base_query = Order.query.filter(
            # 重用：已完成订单的过滤条件
            or_(
                Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES),
                Order.status.in_(['completed'])  # 只统计已完成的订单
            ),
            # 新增：只统计有支付信息的订单
            Order.payment_status == 'paid',
            # 修复：确保有支付时间，避免None值
            Order.payment_time.isnot(None)
        )

        # 重用现有门店过滤逻辑
        if store_name and not is_admin:
            base_query = base_query.filter(Order.store_name == store_name)

        # 获取时间范围和聚合逻辑
        from datetime import datetime, timedelta
        from sqlalchemy import func, extract

        now = datetime.now()

        if time_range == 'day':
            # 最近30天
            start_date = now - timedelta(days=30)
            date_format = '%Y-%m-%d'
            group_by_expr = func.date(Order.payment_time)

        elif time_range == 'week':
            # 最近12周 (SQLite兼容版本)
            start_date = now - timedelta(weeks=12)
            date_format = '%Y-W%U'  # 年-周
            group_by_expr = func.strftime('%Y-W%W', Order.payment_time)

        elif time_range == 'month':
            # 最近12个月 (SQLite兼容版本)
            start_date = now - timedelta(days=365)
            date_format = '%Y-%m'
            group_by_expr = func.strftime('%Y-%m', Order.payment_time)

        else:  # year
            # 最近5年 (SQLite兼容版本)
            start_date = now - timedelta(days=365*5)
            date_format = '%Y'
            group_by_expr = func.strftime('%Y', Order.payment_time)

        # 添加时间过滤
        query = base_query.filter(Order.payment_time >= start_date)

        # 执行聚合查询
        results = db.session.query(
            group_by_expr.label('time_period'),
            func.count(Order.id).label('order_count'),
            func.sum(Order.pay_amount / 100.0).label('total_amount')  # 转换分为元
        ).group_by(group_by_expr).order_by(group_by_expr).all()

        # 处理结果数据，过滤掉None值
        dates = []
        order_counts = []
        amounts = []

        for result in results:
            # 跳过time_period为None的记录
            if result.time_period is not None:
                dates.append(str(result.time_period))
                order_counts.append(result.order_count or 0)
                amounts.append(float(result.total_amount or 0))

        result_store_name = store_name if store_name else "全部门店"

        current_app.logger.info(
            f"图表统计 - 店面: {result_store_name}, 时间范围: {time_range}, "
            f"数据点: {len(dates)}, 总订单: {sum(order_counts)}, 总金额: {sum(amounts):.2f}"
        )

        return jsonify({
            "success": True,
            "data": {
                "store_name": result_store_name,
                "time_range": time_range,
                "dates": dates,
                "order_counts": order_counts,
                "amounts": amounts,
                "summary": {
                    "total_orders": sum(order_counts),
                    "total_amount": sum(amounts),
                    "data_points": len(dates)
                },
                "last_updated": datetime.now().isoformat()
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取图表统计数据失败: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"获取图表统计数据失败: {str(e)}"
        }), 500


@bp.route('/orders/active-list', methods=['GET'])
def get_active_orders_list():
    """
    获取活跃订单详细列表

    Query Parameters:
        store_name: 店面名称（F6页面插件必须提供）
        admin: 管理后台标识（可选，为'true'时支持全部门店统计）

    Returns:
        JSON: 包含活跃订单详细信息的响应
    """
    try:
        store_name = request.args.get('store_name')
        is_admin = request.args.get('admin') == 'true'

        # F6页面插件必须提供store_name（店面隔离）
        if not store_name and not is_admin:
            return jsonify({
                "success": False,
                "error": "缺少store_name参数"
            }), 400

        # 构建查询条件
        query = Order.query.filter(
            # 条件1：e代驾状态码不为终止状态（如果存在）
            or_(
                Order.edj_status_code.is_(None),  # 兼容老数据
                ~Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES)
            ),
            # 条件2：内部状态也不为终止状态
            ~Order.status.in_(['403', '404', '304', '506', 'cancelled', 'completed', 'failed'])
        )

        # 如果指定了店面名称，按店面过滤
        if store_name:
            store_name = decode_store_name(store_name)
            query = query.filter(Order.store_name == store_name)

        active_orders = query.order_by(Order.created_at.desc()).all()

        # 构建订单列表
        orders_list = []
        for order in active_orders:
            order_data = {
                'order_id': order.edaijia_order_id or f'LOCAL_{order.id}',
                'customer_name': order.customer_name or '未知客户',
                'customer_phone': order.customer_phone or '',
                'car_info': {
                    'model': order.car_model or '无',
                    'plate': order.plate_number or '无'
                },
                'pickup_address': order.pickup_address or '未知地址',
                'destination_address': order.destination_address or '未知地址',
                'price': float(order.price) if order.price else 0.0,
                'status': order.status or '100',
                'created_time': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else '',
                'updated_time': order.updated_at.strftime('%Y-%m-%d %H:%M:%S') if order.updated_at else '',
                'manager_name': order.manager_name or '',
                'driver_name': '待分配',  # 活跃订单通常还没有司机信息
                'driver_phone': ''
            }
            orders_list.append(order_data)

        result_store_name = store_name if store_name else "全部门店"
        current_app.logger.info(f"店面 {result_store_name} 活跃订单列表: {len(orders_list)} 个")

        return jsonify({
            "success": True,
            "store_name": result_store_name,
            "orders": orders_list,
            "total": len(orders_list),
            "last_updated": datetime.now().isoformat()
        })

    except Exception as e:
        current_app.logger.error(f"获取活跃订单列表失败: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"获取活跃订单列表失败: {str(e)}"
        }), 500

@bp.route('/geocoding/baidu', methods=['POST'])
def baidu_geocoding_proxy():
    """
    统一地理编码API代理
    支持多个地图服务提供商，根据平台偏好自动选择
    """
    try:
        data = request.get_json()
        if not data or 'address' not in data:
            return jsonify({
                'success': False,
                'message': '缺少地址参数'
            }), 400

        address = data['address']
        city = data.get('city', '深圳市')
        platform = data.get('platform', 'edaijia')  # 支持平台参数

        # 根据平台获取首选地图服务
        from app.config.platform_mapping import get_preferred_map_provider
        preferred_provider = get_preferred_map_provider(platform)

        # 调用统一的地理编码服务
        from app.services.geocoding_service import geocoding_service
        result = geocoding_service.geocode(address, city, preferred_provider)

        if result.success:
            # 转换为前端期望的格式
            return jsonify({
                'success': True,
                'coordinates': {
                    'lat': result.latitude,
                    'lng': result.longitude
                },
                'formatted_address': result.formatted_address,
                'confidence': result.confidence,
                'provider': result.provider
            })
        else:
            return jsonify({
                'success': False,
                'message': result.error_message
            }), 400

    except Exception as e:
        current_app.logger.error(f"地理编码API代理失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'地址解析失败: {str(e)}'
        }), 500

@bp.route('/platforms/supported', methods=['GET'])
def get_supported_platforms():
    """获取支持的代驾平台列表"""
    try:
        from app.config.platform_mapping import get_supported_platforms, get_platform_config

        platforms = []
        for platform_name in get_supported_platforms():
            config = get_platform_config(platform_name)
            platforms.append({
                'id': platform_name,
                'name': config['name'],
                'preferred_map': config['preferred_map_provider'],
                'features': config['features'],
                'supported_cities': config['supported_cities']
            })

        return jsonify({
            'success': True,
            'platforms': platforms
        })

    except Exception as e:
        current_app.logger.error(f"获取支持平台失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取支持平台失败: {str(e)}'
        }), 500

def call_baidu_geocoding_api(address, city='深圳市'):
    """
    统一的地理编码API调用函数
    支持多个地图服务提供商，自动fallback
    """
    try:
        from app.services.geocoding_service import geocoding_service

        # 使用统一的地理编码服务
        result = geocoding_service.geocode(address, city, preferred_provider='baidu')
        return result.to_dict()

    except Exception as e:
        current_app.logger.error(f"地理编码服务调用失败: {str(e)}", exc_info=True)
        return {
            'success': False,
            'message': f'地址解析失败: {str(e)}'
        }

@bp.route('/geocoding/suggestions', methods=['POST'])
def address_suggestions():
    """
    地址搜索建议API - 类似百度地图输入提示

    请求参数:
    {
        "query": "深圳市南山区",  // 搜索关键词
        "city": "深圳市",        // 限制城市范围（可选）
        "limit": 10             // 返回结果数量限制（可选，默认10）
    }

    返回格式:
    {
        "success": true,
        "suggestions": [
            {
                "address": "深圳市南山区科技园",
                "formatted_address": "广东省深圳市南山区科技园",
                "longitude": 113.9547,
                "latitude": 22.5413,
                "confidence": 0.9
            }
        ]
    }
    """
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        city = data.get('city', '深圳市')
        limit = min(int(data.get('limit', 10)), 20)  # 最多20个建议

        if not query:
            return jsonify({'success': False, 'message': '搜索关键词不能为空'}), 400

        if len(query) < 2:
            return jsonify({'success': True, 'suggestions': []})  # 关键词太短，返回空结果

        # 调用百度地图Place API获取地址建议
        suggestions = get_address_suggestions_from_baidu(query, city, limit)

        return jsonify({
            'success': True,
            'suggestions': suggestions,
            'query': query,
            'city': city
        })

    except Exception as e:
        current_app.logger.error(f"地址搜索建议失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'地址搜索失败: {str(e)}'
        }), 500

def get_address_suggestions_from_baidu(query, city, limit=10):
    """
    调用百度地图Place API获取地址建议

    Args:
        query: 搜索关键词
        city: 城市限制
        limit: 结果数量限制

    Returns:
        list: 地址建议列表
    """
    try:
        import requests
        from urllib.parse import quote

        # 获取百度API Key
        baidu_api_key = current_app.config.get('BAIDU_MAP_API_KEY')
        if not baidu_api_key or baidu_api_key == 'your_baidu_map_api_key':
            current_app.logger.warning("百度API Key未配置，返回空建议")
            return []

        # 构造百度Place API请求
        # 使用suggestion接口获取输入提示
        url = f"https://api.map.baidu.com/place/v2/suggestion"
        params = {
            'query': query,
            'region': city,
            'city_limit': 'true',  # 限制在指定城市内
            'output': 'json',
            'ak': baidu_api_key,
            'page_size': limit
        }

        current_app.logger.info(f"调用百度Place API: query={query}, city={city}")

        response = requests.get(url, params=params, timeout=5)
        response.raise_for_status()

        result = response.json()

        if result.get('status') == 0 and result.get('result'):
            suggestions = []

            for item in result['result']:
                # 提取地址信息
                suggestion = {
                    'address': item.get('name', ''),
                    'formatted_address': item.get('address', ''),
                    'district': item.get('district', ''),
                    'city': item.get('city', ''),
                    'province': item.get('province', ''),
                    'confidence': 0.8  # 百度suggestion接口没有confidence字段，给个默认值
                }

                # 如果有坐标信息，添加坐标
                if item.get('location'):
                    suggestion['longitude'] = item['location'].get('lng')
                    suggestion['latitude'] = item['location'].get('lat')

                # 构造完整地址
                if not suggestion['formatted_address']:
                    address_parts = [
                        suggestion.get('province', ''),
                        suggestion.get('city', ''),
                        suggestion.get('district', ''),
                        suggestion.get('address', '')
                    ]
                    suggestion['formatted_address'] = ''.join(filter(None, address_parts))

                suggestions.append(suggestion)

            current_app.logger.info(f"百度Place API返回 {len(suggestions)} 个建议")
            return suggestions

        else:
            current_app.logger.warning(f"百度Place API失败: {result.get('message', '未知错误')}")
            return []

    except Exception as e:
        current_app.logger.error(f"调用百度Place API异常: {str(e)}")
        return []

@bp.route('/security/config_check', methods=['GET'])
def security_config_check():
    """
    生产环境安全配置检查API

    检查项目：
    - MANAGER_INFO_MAP配置完整性
    - 店长信息字段验证
    - 配置文件格式验证

    仅供管理员使用
    """
    try:
        import json

        # 基本安全检查
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        current_app.logger.info(f"Security config check requested from IP {client_ip}")

        # 获取配置
        manager_info_map_str = current_app.config.get('MANAGER_INFO_MAP', '{}')

        try:
            manager_info_map = json.loads(manager_info_map_str)
        except json.JSONDecodeError as e:
            return jsonify({
                'success': False,
                'error': 'MANAGER_INFO_MAP JSON格式错误',
                'details': str(e)
            }), 500

        # 配置检查结果
        check_results = {
            'total_managers': len(manager_info_map),
            'valid_managers': 0,
            'invalid_managers': [],
            'missing_fields': [],
            'duplicate_stores': [],
            'config_status': 'unknown'
        }

        required_fields = ['store_name', 'store_address', 'longitude', 'latitude']
        store_names = {}

        # 检查每个店长配置
        for manager_name, manager_info in manager_info_map.items():
            if not isinstance(manager_info, dict):
                check_results['invalid_managers'].append({
                    'manager': manager_name,
                    'error': '配置不是字典格式'
                })
                continue

            # 检查必要字段
            missing = [field for field in required_fields if field not in manager_info]
            if missing:
                check_results['invalid_managers'].append({
                    'manager': manager_name,
                    'error': f'缺少字段: {missing}'
                })
                continue

            # 检查店面名称重复
            store_name = manager_info['store_name']
            if store_name in store_names:
                check_results['duplicate_stores'].append({
                    'store_name': store_name,
                    'managers': [store_names[store_name], manager_name]
                })
            else:
                store_names[store_name] = manager_name

            check_results['valid_managers'] += 1

        # 确定配置状态
        if check_results['valid_managers'] == 0:
            check_results['config_status'] = 'critical'
        elif check_results['invalid_managers'] or check_results['duplicate_stores']:
            check_results['config_status'] = 'warning'
        else:
            check_results['config_status'] = 'healthy'

        current_app.logger.info(f"Security config check completed: {check_results['config_status']}")

        return jsonify({
            'success': True,
            'data': check_results
        })

    except Exception as e:
        current_app.logger.error(f"Security config check failed: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': '配置检查失败',
            'details': str(e)
        }), 500

@bp.route('/orders/dashboard-statistics', methods=['GET'])
def get_dashboard_statistics():
    """
    获取仪表盘统计数据API
    支持门店过滤，用于门店切换时的前端更新

    参数:
    - store_name: 门店名称（可选）
    - admin: 是否管理员模式（可选，值为'true'时显示全部门店）

    返回:
    - total_customers: 客户总数
    - total_orders: 订单总数
    - pending_orders: 待处理订单数
    - in_progress_orders: 进行中订单数
    - completed_orders: 已完成订单数
    """
    try:
        from flask import session
        from app.main.routes import get_unique_customer_count_by_store_name

        # 获取参数
        store_name = request.args.get('store_name')
        admin_mode = request.args.get('admin') == 'true'

        # 确定查询范围
        if admin_mode or not store_name:
            # 全部门店模式
            order_query = Order.query
            selected_store_name = None
            store_display_name = "全部门店"
        else:
            # 特定门店模式
            decoded_store_name = decode_store_name(store_name)
            order_query = Order.query.filter(Order.store_name == decoded_store_name)
            selected_store_name = decoded_store_name
            store_display_name = decoded_store_name

        # 统计各种订单数量
        pending_orders_count = order_query.filter(Order.status == 'pending').count()
        in_progress_orders_count = order_query.filter(Order.status == 'in_progress').count()
        completed_orders_count = order_query.filter(Order.status == 'completed').count()

        total_orders = order_query.count()

        # 客户总数：使用去重统计（基于车牌号+手机号+姓名）
        total_customers = get_unique_customer_count_by_store_name(selected_store_name)

        current_app.logger.info(
            f"仪表盘统计 - 店面: {store_display_name}, "
            f"客户: {total_customers}, 订单: {total_orders}, "
            f"待处理: {pending_orders_count}, 进行中: {in_progress_orders_count}, 已完成: {completed_orders_count}"
        )

        return jsonify({
            "success": True,
            "data": {
                "store_name": store_display_name,
                "total_customers": total_customers,
                "total_orders": total_orders,
                "pending_orders": pending_orders_count,
                "in_progress_orders": in_progress_orders_count,
                "completed_orders": completed_orders_count
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取仪表盘统计数据失败: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"获取统计数据失败: {str(e)}"
        }), 500

p;