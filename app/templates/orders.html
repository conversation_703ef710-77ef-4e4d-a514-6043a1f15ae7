{% extends "base.html" %}

{% block app_content %}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><i class="fas fa-clipboard-list"></i> 订单管理</h1>
        {# Optional: Add a button here if needed, e.g., "Create Generic Order" if applicable #}
    </div>

    <div class="row mb-3">
        <div class="col-md-8">
            <form method="GET" action="{{ url_for('main.orders') }}" class="form-inline">
                <div class="input-group w-100">
                    <input type="search" name="q" class="form-control" placeholder="搜索订单ID、客户名/电话、地址..." value="{{ search_query or '' }}">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-outline-secondary"><i class="fas fa-search"></i> 搜索</button>
                    </div>
                </div>
                <input type="hidden" name="status" value="{{ current_status or 'all' }}"> {# Keep status filter if active #}
            </form>
        </div>
        <div class="col-md-4">
            <form method="GET" action="{{ url_for('main.orders') }}" class="form-inline float-md-right">
                 <input type="hidden" name="q" value="{{ search_query or '' }}"> {# Keep search query if active #}
                <div class="input-group">
                    <select name="status" class="form-control" onchange="this.form.submit()">
                        <option value="all" {% if current_status == 'all' %}selected{% endif %}>所有状态</option>
                        <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>待处理</option>
                        <option value="in_progress" {% if current_status == 'in_progress' %}selected{% endif %}>进行中</option>
                        <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>已完成</option>
                        <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>已取消</option>
                        <option value="failed" {% if current_status == 'failed' %}selected{% endif %}>失败</option>
                    </select>
                </div>
            </form>
        </div>
    </div>

    {% if orders %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="thead-light">
                    <tr>
                        <th><i class="fas fa-receipt"></i> e代驾订单ID</th>
                        <th><i class="fas fa-user-tie"></i> 客户姓名</th>
                        <th><i class="fas fa-phone-alt"></i> 客户电话</th>
                        <th><i class="fas fa-car"></i> 车牌号</th>
                        <th><i class="fas fa-map-pin"></i> 出发地</th>
                        <th><i class="fas fa-flag-checkered"></i> 目的地</th>
                        <th><i class="fas fa-tasks"></i> 状态</th>
                        <th><i class="fas fa-motorcycle"></i> 司机</th>
                        <th><i class="fas fa-money-bill-wave"></i> 金额</th>
                        <th><i class="fas fa-clock"></i> 创建时间</th>
                        <th style="min-width: 150px;"><i class="fas fa-clock"></i> 完成时间</th>
                        <th style="min-width: 100px;"><i class="fas fa-info-circle"></i> 操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in orders %}
                        <tr>
                            <td>{{ order.edaijia_order_id }}</td>
                            <td>{{ order.customer_name or (order.customer.name if order.customer else '无') }}</td>
                            <td>{{ order.customer_phone or (order.customer.phone if order.customer else '无') }}</td>
                            <td>{{ order.plate_number or '无' }}</td>
                            <td>{{ order.pickup_address }}</td>
                            <td>{{ order.destination_address }}</td>
                            <td>
                                <span class="badge badge-{{
                                    'warning' if order.edj_status_code in ['102', '180'] else
                                    'info' if order.edj_status_code in ['301', '302', '303'] else
                                    'success' if order.edj_status_code == '304' else
                                    'danger' if order.edj_status_code in ['403', '404'] else
                                    'danger' if order.edj_status_code == '506' else 'light'
                                }}" title="e代驾状态码: {{ order.edj_status_code or '无' }} - {{ order.edj_status_desc or '无描述' }}">
                                    {{ order.get_status_display() }}
                                </span>
                            </td>
                            <td>{{ order.driver_name or '-' }} ({{ order.driver_phone or 'N/A' }})</td>
                            <td>¥{{ '%.2f'|format(order.price) if order.price is not none else '-' }}</td>
                            <td>{{ order.created_at.strftime('%Y-%m-%d %H:%M') if order.created_at else '-' }}</td>
                            <td>{{ orders_json[loop.index0].finish_time or '-' }}</td>
                            <td>
                                <button class="btn btn-sm btn-info order-detail-btn"
                                    data-order='{{ orders_json[loop.index0]|tojson }}'>详情</button>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if pagination and pagination.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ prev_url or '#' }}"><i class="fas fa-angle-left"></i> 上一页</a>
                </li>
                <li class="page-item disabled"><span class="page-link">第 {{ current_page }} 页 / {{ pagination.pages }} 页</span></li>
                <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ next_url or '#' }}">下一页 <i class="fas fa-angle-right"></i></a>
                </li>
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="alert alert-info">
            {% if search_query %}
                没有找到符合条件 "{{ search_query }}" 的订单。
            {% else %}
                当前状态没有订单。
            {% endif %}
        </div>
    {% endif %}

    <!-- 订单详情Modal -->
    <div class="modal fade" id="orderDetailModal" tabindex="-1" role="dialog" aria-labelledby="orderDetailModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="orderDetailModalLabel"><i class="fas fa-info-circle"></i> 订单详情</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <table class="table table-bordered table-sm mb-0">
              <tbody id="orderDetailTableBody">
                <!-- 动态填充 -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
$(function(){
  $('.order-detail-btn').click(function(){
    var order = $(this).data('order');
    var fields = [
      {label: 'e代驾订单号', key: 'edaijia_order_id'},
      {label: 'e代驾预约号', key: 'edj_booking_id'},
      {label: '客户姓名', key: 'customer_name', render: function(o){return o.customer_name || (o.customer ? o.customer.name : '无');}},
      {label: '客户电话', key: 'customer_phone', render: function(o){return o.customer_phone || (o.customer ? o.customer.phone : '无');}},
      {label: '车牌号', key: 'plate_number', render: function(o){return o.plate_number || '无';}},
      {label: '车型', key: 'car_model', render: function(o){return o.car_model || '无';}},
      {label: '起点', key: 'pickup_address'},
      {label: '终点', key: 'destination_address'},
      {label: '状态', key: 'status_display'},
      {label: '司机', key: 'driver_name'},
      {label: '司机电话', key: 'driver_phone'},
      {label: '价格', key: 'price', render: function(o){return o.price !== undefined ? '¥' + o.price.toFixed(2) : '-';}},
      {label: '下单时间', key: 'order_time', render: function(o){return o.order_time ? new Date(o.order_time * 1000).toLocaleString() : '-';}},
      {label: '派单超时(秒)', key: 'timeout'},
      {label: '渠道', key: 'channel'},
      {label: '业务来源', key: 'from_source'},
      {label: '动态调价', key: 'dynamic_fee'},
      {label: '优惠券码', key: 'bonus_sn'},
      {label: 'e代驾状态码', key: 'edj_status_code'},
      {label: 'e代驾状态描述', key: 'edj_status_desc'}
    ];
    var html = '';
    fields.forEach(function(f){
      var val = f.render ? f.render(order) : (order[f.key] || '-');
      html += '<tr><th style="width:160px;">'+f.label+'</th><td>'+val+'</td></tr>';
    });
    $('#orderDetailTableBody').html(html);
    $('#orderDetailModal').modal('show');
  });
});
</script>
{% endblock %}
