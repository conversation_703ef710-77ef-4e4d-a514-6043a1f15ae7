{% extends "base.html" %}
{% import "bootstrap/wtf.html" as wtf %}

{% block app_content %}
    <div class="container mt-4">
        {% if customer %}
            <h2>为 <i class="fas fa-user-circle"></i> {{ customer.name }} ({{ customer.phone }}) 创建e代驾订单</h2>
            <p>
                <strong>车型:</strong> {{ customer.car_model or '未提供' }} <br>
                <strong>车牌:</strong> {{ customer.plate_number or '未提供' }} <br>
                <strong>常住地址:</strong> {{ customer.home_address or '未提供' }}
            </p>
            <hr>
        {% else %}
            <h2><i class="fas fa-plus-circle"></i> 创建新的e代驾订单</h2>
            <p class="text-muted">请填写以下订单信息。如果直接从此页面创建，订单将不会自动关联到现有客户。</p>
            {% if not form.customer_id %}{# Check if a field for unlinked customer name/phone should be here #}
            {% endif %}
        {% endif %}

        {# Pass customer.id to the form if customer exists, otherwise pass 0 or empty #}
        <form id="createOrderForm" method="POST" class="needs-validation" novalidate data-customer-id="{{ customer.id if customer else 0 }}">
            {{ form.hidden_tag() }} {# For CSRF token #}

            <!-- 订单类型选择 -->
            <div class="form-group">
                <label class="control-label">{{ form.order_type.label }}</label>
                <div class="btn-group btn-group-toggle" data-toggle="buttons" style="display: block; margin-bottom: 15px;">
                    <label class="btn btn-outline-primary active">
                        <input type="radio" name="order_type" id="order_type_now" value="now" checked> 代叫
                    </label>
                    <label class="btn btn-outline-primary">
                        <input type="radio" name="order_type" id="order_type_reserve" value="reserve"> 预约
                    </label>
                </div>
            </div>

            <!-- 代叫模式字段 -->
            <div id="now_order_fields">
                <div class="form-group">
                    {{ wtf.form_field(form.contact_phone, class="form-control", placeholder="请输入联系人的手机号，如不需要可留空") }}
                </div>
            </div>

            <!-- 预约模式字段 -->
            <div id="reserve_order_fields" style="display: none;">
                <div class="form-group">
                    <label class="control-label">{{ form.reserve_time.label }}</label>
                    <select class="form-control" name="reserve_time" id="reserve_time">
                        <option value="">加载中...</option>
                    </select>
                    <small class="form-text text-muted">{{ form.reserve_time.description }}</small>
                </div>
            </div>

            <!-- 位置信息 -->
            <div class="form-group">
                <label class="control-label">{{ form.pickup_address.label }}</label>
                <div class="input-group">
                    <input type="text" class="form-control address-input" name="pickup_address" id="pickup_address" placeholder="例如：北京市朝阳区XX路XX号" required autocomplete="off">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="button" id="get_location_btn">
                            <i class="fas fa-map-marker-alt"></i> 获取位置
                        </button>
                        <button class="btn btn-outline-info" type="button" id="toggle_pickup_suggestions" title="切换地址建议">
                            <i class="fas fa-lightbulb"></i>
                        </button>
                    </div>
                </div>
                <div id="location_status" class="mt-1"></div>
                <div id="pickup_suggestions" class="address-suggestions" style="display: none;"></div>
            </div>

            <div class="form-group">
                <label class="control-label">{{ form.destination_address.label }}</label>
                <div class="input-group">
                    <input type="text" class="form-control address-input" name="destination_address" id="destination_address" placeholder="例如：北京市海淀区XX大厦" required autocomplete="off">
                    <div class="input-group-append">
                        <button class="btn btn-outline-info" type="button" id="toggle_dest_suggestions" title="切换地址建议">
                            <i class="fas fa-lightbulb"></i>
                        </button>
                    </div>
                </div>
                <div id="dest_suggestions" class="address-suggestions" style="display: none;"></div>
            </div>

            <!-- 支付方式 -->
            <div class="form-group">
                <label class="control-label">{{ form.payment_method.label }}</label>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="payment_method" id="payment_self" value="self" checked>
                    <label class="form-check-label" for="payment_self">
                        我来支付
                    </label>
                </div>
            </div>

            <div class="form-group">
                {{ wtf.form_field(form.remarks, class="form-control", rows="3", placeholder="可选：输入订单备注，如具体楼层、等待偏好等") }}
            </div>

            <button type="submit" class="btn btn-primary"><i class="fas fa-paper-plane"></i> 模拟下单</button>
            {% if customer %}
            <a href="{{ url_for('main.customers') }}" class="btn btn-secondary"><i class="fas fa-times"></i> 取消</a>
            {% else %}
            <a href="{{ url_for('main.index') }}" class="btn btn-secondary"><i class="fas fa-times"></i> 返回首页</a>
            {% endif %}
        </form>
    </div>
{% endblock %}

{% block styles %}
    {{ super() }}
    <style>
        .address-suggestions {
            position: relative;
            z-index: 1000;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
        }

        .suggestion-item:hover {
            background-color: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-main {
            font-weight: 500;
            color: #333;
        }

        .suggestion-detail {
            font-size: 0.85em;
            color: #666;
            margin-top: 2px;
        }

        .suggestions-loading {
            padding: 12px;
            text-align: center;
            color: #666;
        }

        .suggestions-empty {
            padding: 12px;
            text-align: center;
            color: #999;
            font-style: italic;
        }

        .address-input.suggestions-active {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }

        .btn-outline-info.active {
            background-color: #17a2b8;
            color: white;
        }
    </style>
{% endblock %}

{% block scripts %}
    {{ super() }}
    <script>
        $(document).ready(function(){
            var $form = $("#createOrderForm");
            var customerId = parseInt($form.data('customer-id')) || 0;

            // 地址建议功能状态
            var addressSuggestionsEnabled = {
                pickup: false,
                destination: false
            };

            // 防抖计时器
            var suggestionTimers = {
                pickup: null,
                destination: null
            };

            // 店面信息（用于判断是否为店面地址）
            var storeInfo = {
                name: '{{ session.get("current_store_name", "") }}',
                address: '{{ session.get("current_store_address", "") }}'
            };

            // 自动填充送达地址
            {% if customer and customer.home_address %}
            $form.find('[name="destination_address"]').val({{ customer.home_address|tojson }});
            {% endif %}

            // 订单类型切换
            $('input[name="order_type"]').change(function() {
                var orderType = $(this).val();
                if (orderType === 'now') {
                    $('#now_order_fields').show();
                    $('#reserve_order_fields').hide();
                } else {
                    $('#now_order_fields').hide();
                    $('#reserve_order_fields').show();
                    // 加载预约时间选项
                    loadReserveTimes();
                }
            });

            // 地址建议切换按钮事件
            $('#toggle_pickup_suggestions').click(function() {
                toggleAddressSuggestions('pickup');
            });

            $('#toggle_dest_suggestions').click(function() {
                toggleAddressSuggestions('destination');
            });

            // 地址输入事件
            $('#pickup_address').on('input', function() {
                handleAddressInput('pickup', $(this).val());
            });

            $('#destination_address').on('input', function() {
                handleAddressInput('destination', $(this).val());
            });

            // 点击其他地方隐藏建议
            $(document).click(function(e) {
                if (!$(e.target).closest('.form-group').length) {
                    hideAllSuggestions();
                }
            });

            // 获取位置按钮点击事件
            $('#get_location_btn').click(function() {
                getLocation();
            });

            // 页面加载时自动尝试获取位置
            setTimeout(function() {
                getLocation();
            }, 500);

            // 加载预约时间选项
            function loadReserveTimes() {
                var $reserveTime = $('#reserve_time');
                $reserveTime.html('<option value="">加载中...</option>');

                // 调用后端API获取预约时间
                $.ajax({
                    url: "{{ url_for('api.get_appointment_times') }}",
                    type: "POST",
                    data: {},
                    success: function(response) {
                        $reserveTime.empty();
                        $reserveTime.append('<option value="">请选择预约时间</option>');

                        if (response.success && response.times && response.times.length > 0) {
                            $.each(response.times, function(i, timeObj) {
                                if (timeObj.available) {
                                    $reserveTime.append('<option value="' + timeObj.time + '">' + timeObj.time + '</option>');
                                }
                            });
                        } else {
                            $reserveTime.append('<option value="" disabled>无可用预约时间</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        $reserveTime.html('<option value="">获取预约时间失败</option>');
                        console.error("Error getting appointment times:", error);
                    }
                });
            }

            // 获取位置函数
            function getLocation() {
                var $locationStatus = $('#location_status');
                $locationStatus.html('<span class="text-info"><i class="fas fa-spinner fa-spin"></i> 正在获取位置...</span>');

                // 调用后端API获取位置
                $.ajax({
                    url: "{{ url_for('api.get_current_location') }}",
                    type: "POST",
                    data: {},
                    success: function(response) {
                        if (response.success && response.address) {
                            $('#pickup_address').val(response.address);
                            $locationStatus.html('<span class="text-success"><i class="fas fa-check-circle"></i> 位置获取成功</span>');
                        } else {
                            $locationStatus.html('<span class="text-danger"><i class="fas fa-exclamation-circle"></i> ' +
                                                (response.message || '位置获取失败，请手动输入') + '</span>');
                        }
                    },
                    error: function(xhr, status, error) {
                        $locationStatus.html('<span class="text-danger"><i class="fas fa-exclamation-circle"></i> 位置获取失败，请手动输入</span>');
                        console.error("Error getting location:", error);
                    }
                });
            }

            // 切换地址建议功能
            function toggleAddressSuggestions(type) {
                var isEnabled = addressSuggestionsEnabled[type];
                addressSuggestionsEnabled[type] = !isEnabled;

                var $toggleBtn = $('#toggle_' + type + '_suggestions');
                var $suggestionsDiv = $('#' + type + '_suggestions');

                if (addressSuggestionsEnabled[type]) {
                    $toggleBtn.addClass('active').attr('title', '关闭地址建议');
                    $toggleBtn.find('i').removeClass('fas fa-lightbulb').addClass('fas fa-lightbulb-on');
                } else {
                    $toggleBtn.removeClass('active').attr('title', '开启地址建议');
                    $toggleBtn.find('i').removeClass('fas fa-lightbulb-on').addClass('fas fa-lightbulb');
                    $suggestionsDiv.hide();
                    $('#' + type + '_address').removeClass('suggestions-active');
                }
            }

            // 处理地址输入
            function handleAddressInput(type, value) {
                if (!addressSuggestionsEnabled[type]) {
                    return;
                }

                // 清除之前的计时器
                if (suggestionTimers[type]) {
                    clearTimeout(suggestionTimers[type]);
                }

                // 如果输入为空或太短，隐藏建议
                if (!value || value.trim().length < 2) {
                    hideSuggestions(type);
                    return;
                }

                // 检查是否为店面地址
                if (isStoreAddress(value)) {
                    hideSuggestions(type);
                    return;
                }

                // 防抖处理，500ms后执行搜索
                suggestionTimers[type] = setTimeout(function() {
                    searchAddressSuggestions(type, value.trim());
                }, 500);
            }

            // 判断是否为店面地址
            function isStoreAddress(address) {
                if (!address || !storeInfo.name) {
                    return false;
                }

                // 检查是否包含店面名称
                if (address.includes(storeInfo.name)) {
                    return true;
                }

                // 检查是否包含店面地址
                if (storeInfo.address && address.includes(storeInfo.address)) {
                    return true;
                }

                // 检查常见店面关键词
                var storeKeywords = ['康众汽配', '门店', '店面', '汽配店', '修理厂'];
                for (var i = 0; i < storeKeywords.length; i++) {
                    if (address.includes(storeKeywords[i])) {
                        return true;
                    }
                }

                return false;
            }

            // 辅助函数：补零
            function padZero(num) {
                return num < 10 ? '0' + num : num;
            }

            // 搜索地址建议
            function searchAddressSuggestions(type, query) {
                var $suggestionsDiv = $('#' + type + '_suggestions');
                var $addressInput = $('#' + type + '_address');

                // 显示加载状态
                showSuggestionsLoading(type);
                $addressInput.addClass('suggestions-active');

                $.ajax({
                    url: '/api/geocoding/suggestions',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        query: query,
                        city: '深圳市',
                        limit: 8
                    }),
                    success: function(response) {
                        if (response.success && response.suggestions && response.suggestions.length > 0) {
                            showAddressSuggestions(type, response.suggestions);
                        } else {
                            showSuggestionsEmpty(type);
                        }
                    },
                    error: function(xhr) {
                        console.error('获取地址建议失败:', xhr);
                        showSuggestionsEmpty(type);
                    }
                });
            }

            // 显示地址建议
            function showAddressSuggestions(type, suggestions) {
                var $suggestionsDiv = $('#' + type + '_suggestions');
                var html = '';

                suggestions.forEach(function(suggestion) {
                    var mainAddress = suggestion.address || suggestion.formatted_address || '';
                    var detailAddress = suggestion.formatted_address || '';

                    // 如果主地址和详细地址相同，只显示一个
                    if (mainAddress === detailAddress) {
                        detailAddress = '';
                    }

                    html += '<div class="suggestion-item" data-address="' +
                            (suggestion.formatted_address || suggestion.address || '') + '">';
                    html += '<div class="suggestion-main">' + mainAddress + '</div>';
                    if (detailAddress) {
                        html += '<div class="suggestion-detail">' + detailAddress + '</div>';
                    }
                    html += '</div>';
                });

                $suggestionsDiv.html(html).show();

                // 绑定点击事件
                $suggestionsDiv.find('.suggestion-item').click(function() {
                    var address = $(this).data('address');
                    $('#' + type + '_address').val(address);
                    hideSuggestions(type);

                    // 地址选择完成后，触发估值（如果两个地址都有值）
                    checkAndEstimate();
                });
            }

            // 显示加载状态
            function showSuggestionsLoading(type) {
                var $suggestionsDiv = $('#' + type + '_suggestions');
                $suggestionsDiv.html('<div class="suggestions-loading"><i class="fas fa-spinner fa-spin"></i> 搜索中...</div>').show();
            }

            // 显示空结果
            function showSuggestionsEmpty(type) {
                var $suggestionsDiv = $('#' + type + '_suggestions');
                $suggestionsDiv.html('<div class="suggestions-empty">未找到相关地址</div>').show();

                // 3秒后自动隐藏
                setTimeout(function() {
                    hideSuggestions(type);
                }, 3000);
            }

            // 隐藏建议
            function hideSuggestions(type) {
                $('#' + type + '_suggestions').hide();
                $('#' + type + '_address').removeClass('suggestions-active');
            }

            // 隐藏所有建议
            function hideAllSuggestions() {
                hideSuggestions('pickup');
                hideSuggestions('destination');
            }

            // 检查并进行估值
            function checkAndEstimate() {
                var pickupAddress = $('#pickup_address').val().trim();
                var destAddress = $('#destination_address').val().trim();

                // 只有当两个地址都输入完整且不为空时才进行估值
                if (pickupAddress && destAddress && pickupAddress.length > 5 && destAddress.length > 5) {
                    console.log('地址输入完成，可以进行估值:', {
                        pickup: pickupAddress,
                        destination: destAddress
                    });
                    // 这里可以添加估值逻辑
                    // estimatePrice(pickupAddress, destAddress);
                }
            }

            $form.submit(function(event){
                event.preventDefault();

                // 表单验证
                if (!validateForm()) {
                    return false;
                }

                var $submitButton = $form.find('button[type="submit"]');
                $submitButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span> 处理中...');

                var orderType = $('input[name="order_type"]:checked').val();

                var formData = {
                    order_type: orderType,
                    pickup_address: $form.find('[name="pickup_address"]').val(),
                    destination_address: $form.find('[name="destination_address"]').val(),
                    payment_method: $form.find('input[name="payment_method"]:checked').val(),
                    remarks: $form.find('[name="remarks"]').val()
                };

                // 根据订单类型添加不同的字段
                if (orderType === 'now') {
                    formData.contact_phone = $form.find('[name="contact_phone"]').val();
                } else {
                    formData.reserve_time = $form.find('[name="reserve_time"]').val();
                }

                var apiUrl = "{{ url_for('api.create_edaijia_order', customer_id=0) }}".replace("/0", "/" + customerId);

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    success: function(response){
                        alert(response.message || '订单已成功模拟创建！');
                        window.location.href = "{{ url_for('main.orders') }}";
                    },
                    error: function(xhr, status, error){
                        var errorMsg = "订单创建失败: ";
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg += xhr.responseJSON.message;
                        } else {
                            errorMsg += (error || '未知错误');
                        }
                        alert(errorMsg);
                        $submitButton.prop('disabled', false).html('<i class="fas fa-paper-plane"></i> 模拟下单');
                    }
                });
            });

            // 表单验证函数
            function validateForm() {
                var isValid = true;
                var orderType = $('input[name="order_type"]:checked').val();

                // 检查取车地址
                if (!$('#pickup_address').val().trim()) {
                    alert('请输入取车地址');
                    $('#pickup_address').focus();
                    return false;
                }

                // 检查送达地址
                if (!$('#destination_address').val().trim()) {
                    alert('请输入送达地址');
                    $('#destination_address').focus();
                    return false;
                }

                // 预约模式下检查预约时间
                if (orderType === 'reserve' && !$('#reserve_time').val()) {
                    alert('请选择预约时间');
                    $('#reserve_time').focus();
                    return false;
                }

                // 代叫模式下检查联系人手机号格式（如果填写了）
                if (orderType === 'now') {
                    var contactPhone = $('#contact_phone').val().trim();
                    if (contactPhone && !/^1[3-9]\d{9}$/.test(contactPhone)) {
                        alert('请输入有效的手机号码');
                        $('#contact_phone').focus();
                        return false;
                    }
                }

                return isValid;
            }
        });
    </script>
{% endblock %}