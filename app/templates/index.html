{% extends "base.html" %}

{% block app_content %}
<div class="row">
    <!-- 门店选择和API状态 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-store"></i> 门店选择</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="font-weight-bold mb-2">选择门店：</label><br>
                    <label class="mr-3">
                        <input type="radio" name="storeRadio" value="all" {% if not selected_store_name %}checked{% endif %}>
                        <span class="badge badge-primary">全部门店</span>
                    </label>
                    {% for store in stores %}
                        <label class="mr-3 mb-2">
                            <input type="radio" name="storeRadio" value="{{ store.name }}" {% if store.name == selected_store_name %}checked{% endif %}>
                            {{ store.name }}
                        </label>
                    {% endfor %}
                    {% if config['ENV'] == 'development' %}
                    <br><button id="generateMockDataBtn" class="btn btn-sm btn-outline-secondary mt-2"><i class="fas fa-magic"></i> 生成Mock数据</button>
                    {% endif %}
                </div>

                <!-- 简化的API状态 -->
                <div class="mt-3">
                    <h6><i class="fas fa-cogs"></i> API 状态</h6>
                    {% for status_item in api_statuses %}
                        {% if status_item.service_name == 'edaijia' %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <span class="text-muted"><i class="fas fa-car-alt"></i> e代驾 API</span>
                            </div>
                            <div>
                                <span class="badge badge-{{ {'normal': 'success', 'error': 'danger', 'unknown': 'secondary', 'disabled': 'secondary'}.get(status_item.status, 'secondary') }} badge-pill">
                                    {{ {'normal': '正常', 'error': '异常', 'unknown': '未知', 'disabled': '已禁用'}.get(status_item.status, '未知') }}
                                </span>
                                {% if status_item.status == 'error' or status_item.status == 'unknown' %}
                                <button class="btn btn-sm btn-warning reconnect-api-btn ml-1" data-service="{{ status_item.service_name }}" title="尝试重新连接">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    {% endfor %}
                    <small class="text-muted">F6 API已禁用 - 根据最新方案不再需要</small>
                </div>
            </div>
        </div>
    </div>
    <!-- 订单统计图表 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><i class="fas fa-chart-bar"></i> 已完成付款订单统计</h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary time-range-btn" data-range="day">日</button>
                    <button type="button" class="btn btn-outline-secondary time-range-btn active" data-range="month">月</button>
                    <button type="button" class="btn btn-outline-secondary time-range-btn" data-range="year">年</button>
                </div>
            </div>
            <div class="card-body" style="height: 350px; position: relative;">
                <div id="chartLoadingIndicator" class="text-center text-muted py-4">
                    <div class="spinner-border spinner-border-sm" role="status"></div>
                    <div class="mt-2">加载统计数据中...</div>
                </div>
                <div id="chartContainer" style="height: 300px; width: 100%; display: none;">
                    <canvas id="orderStatisticsChart"></canvas>
                </div>
                <!-- 图表汇总信息 - 图表下方 -->
                <div id="chartSummary" style="display: none; margin-top: 8px; padding: 8px 12px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: 1px solid #dee2e6; border-radius: 6px; font-size: 13px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="font-weight: 600; color: #495057;" id="summaryTimeRange">最近12个月</div>
                        <div style="display: flex; gap: 16px; color: #6c757d;">
                            <span style="display: flex; align-items: center; gap: 4px;">
                                <i class="fas fa-chart-bar" style="color: #007bff;"></i>
                                <span id="summaryTotalOrders">0</span>单
                            </span>
                            <span style="display: flex; align-items: center; gap: 4px;">
                                <i class="fas fa-coins" style="color: #28a745;"></i>
                                ¥<span id="summaryTotalAmount">0</span>
                            </span>
                        </div>
                    </div>
                </div>
                <div id="chartNoData" class="text-center text-muted py-4" style="display: none;">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <div>暂无已完成付款订单数据</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 核心指标卡片 -->
    <div class="col-md-4 mb-4">
        <div class="card text-center h-100 shadow-sm">
            <div class="card-body">
                <div class="mb-2"><i class="fas fa-users fa-2x text-primary"></i></div>
                <h5 class="card-title">下单客户数</h5>
                <h2 class="font-weight-bold text-primary mb-0" id="totalCustomersCount">{{ total_customers }}</h2>
                <small class="text-muted">基于订单去重统计（车牌+手机+姓名）</small>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card text-center h-100 shadow-sm">
            <div class="card-body">
                <div class="mb-2"><i class="fas fa-clipboard-list fa-2x text-info"></i></div>
                <h5 class="card-title">订单总数</h5>
                <a href="{{ url_for('main.orders') }}" class="stretched-link text-decoration-none">
                    <h2 class="font-weight-bold text-info mb-0" id="totalOrdersCount">{{ total_orders }}</h2>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card text-center h-100 shadow-sm">
            <div class="card-body">
                <div class="mb-2"><i class="fas fa-chart-bar fa-2x text-success"></i></div>
                <h5 class="card-title">订单状态统计</h5>
                <div class="d-flex justify-content-around mt-3">
                    <div class="text-center">
                        <a href="{{ url_for('main.orders', status='pending') }}" class="text-decoration-none">
                            <div class="badge badge-warning">待处理</div>
                            <div class="font-weight-bold" id="pendingOrdersCount">{{ pending_orders }}</div>
                        </a>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('main.orders', status='in_progress') }}" class="text-decoration-none">
                            <div class="badge badge-info">进行中</div>
                           <div class="font-weight-bold" id="inProgressOrdersCount">{{ in_progress_orders }}</div>
                        </a>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('main.orders', status='completed') }}" class="text-decoration-none">
                            <div class="badge badge-success">已完成</div>
                            <div class="font-weight-bold" id="completedOrdersCount">{{ completed_orders }}</div>
                        </a>
                    </div>
                    <div class="text-center">
                        <a href="{{ url_for('main.orders', status='cancelled') }}" class="text-decoration-none">
                            <div class="badge badge-danger">已取消</div>
                            <div class="font-weight-bold" id="cancelledOrdersCount">{{ cancelled_orders }}</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 浏览器插件使用说明 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-success shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-puzzle-piece"></i> 浏览器插件下单方式 (推荐)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6><i class="fas fa-rocket text-success"></i> 新的下单方式</h6>
                        <p class="mb-2">现在可以直接在F6系统中通过浏览器插件下单e代驾，无需手动创建客户！</p>

                        <h6><i class="fas fa-cogs text-info"></i> 工作流程</h6>
                        <ol class="mb-2">
                            <li>店长在F6车辆列表中选择需要e代驾的车辆</li>
                            <li>点击"e代驾订单"按钮，传递客户、车辆信息</li>
                            <li>插件自动识别店长身份和所属门店</li>
                            <li>系统按门店隔离创建订单记录</li>
                        </ol>

                        <h6><i class="fas fa-shield-alt text-warning"></i> 店面隔离</h6>
                        <p class="mb-2">每个门店的订单完全隔离，店长只能看到自己门店的数据。</p>

                        <h6><i class="fas fa-user-shield text-success"></i> 认证机制</h6>
                        <p class="mb-0">通过店长姓名映射手机号，调用F6 API验证身份和权限。</p>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-browser fa-3x text-success mb-3"></i>
                            <h6>API接口</h6>
                            <code class="small">/api/f6/submit_order</code>
                            <hr>
                            <h6>店面隔离</h6>
                            <span class="badge badge-warning">按门店分离</span>
                            <hr>
                            <h6>认证方式</h6>
                            <span class="badge badge-success">F6员工验证</span>
                            <hr>
                            <h6>数据管理</h6>
                            <span class="badge badge-info">订单直存</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近订单 -->
    <div class="col-md-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0"><i class="fas fa-history"></i> 最近订单 (最后5条)</h5>
            </div>
            <div class="card-body p-0">
                {% if recent_orders %}
                <ul class="list-group list-group-flush">
                    {% for order in recent_orders %}
                    <li class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">订单: {{ order.edaijia_order_id or 'N/A' }}</h6>
                            <small class="text-muted">{{ order.created_at.strftime('%Y-%m-%d %H:%M') if order.created_at else '' }}</small>
                        </div>
                        <p class="mb-1"><i class="fas fa-user-tag mr-1"></i>客户: {{ order.customer.name }} ({{ order.customer.phone }})</p>
                        <small>
                            状态: <span class="badge badge-{{ {'pending': 'warning', 'in_progress': 'info', 'completed': 'success', 'cancelled': 'danger', 'cancelled_customer': 'danger', 'cancelled_driver': 'danger'}.get(order.status, 'secondary') }}">
                                {{ {'pending': '待处理', 'in_progress': '进行中', 'completed': '已完成', 'cancelled': '已取消', 'cancelled_customer': '客户取消', 'cancelled_driver': '司机取消'}.get(order.status, order.status) }}
                            </span>
                        </small>
                        <a href="{{ url_for('main.orders', q=order.edaijia_order_id) }}" class="stretched-link" title="查看该订单"></a>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <div class="text-center p-3 text-muted">
                    <i class="fas fa-folder-open fa-2x mb-2"></i><br>
                    暂无最近订单记录。
                </div>
                {% endif %}
            </div>
            {% if recent_orders and recent_orders|length > 0 %}
            <div class="card-footer text-center">
                <a href="{{ url_for('main.orders') }}" class="btn btn-sm btn-outline-primary">查看所有订单 <i class="fas fa-arrow-circle-right ml-1"></i></a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 快速创建订单模态框 -->
<div class="modal fade" id="quickOrderModal" tabindex="-1" role="dialog" aria-labelledby="quickOrderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="quickOrderModalLabel"><i class="fas fa-plus-circle"></i> 快速创建e代驾订单</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="customerSearchInput">检索客户（姓名/电话/车牌）</label>
          <input type="text" class="form-control" id="customerSearchInput" placeholder="输入姓名、电话、车牌号...">
        </div>
        <div class="form-group">
          <label>选择客户</label>
          <div id="customerList" style="max-height:200px;overflow-y:auto;border:1px solid #eee;border-radius:4px;padding:8px 0;"></div>
        </div>
        <form id="quickOrderForm" autocomplete="off">
          <input type="hidden" name="customer_id" id="quickOrderCustomerId">
          <div class="form-group">
            <label for="quickPickup">取车地址</label>
            <div class="input-group">
              <input type="text" class="form-control address-input" id="quickPickup" name="pickup_address" required autocomplete="off">
              <div class="input-group-append">
                <button class="btn btn-outline-info btn-sm" type="button" id="toggleQuickPickupSuggestions" title="切换地址建议">
                  <i class="fas fa-lightbulb"></i>
                </button>
              </div>
            </div>
            <div id="quickPickupSuggestions" class="address-suggestions" style="display: none;"></div>
          </div>
          <div class="form-group">
            <label for="quickDest">送达地址</label>
            <div class="input-group">
              <input type="text" class="form-control address-input" id="quickDest" name="destination_address" required autocomplete="off">
              <div class="input-group-append">
                <button class="btn btn-outline-info btn-sm" type="button" id="toggleQuickDestSuggestions" title="切换地址建议">
                  <i class="fas fa-lightbulb"></i>
                </button>
              </div>
            </div>
            <div id="quickDestSuggestions" class="address-suggestions" style="display: none;"></div>
          </div>
          <div class="form-group">
            <label for="quickRemarks">订单备注</label>
            <textarea class="form-control" id="quickRemarks" name="remarks" rows="2"></textarea>
          </div>
          <button type="submit" class="btn btn-primary">提交订单</button>
        </form>
      </div>
    </div>
  </div>
</div>

{% set has_api_error = false %}
{% for status_item in api_statuses %}
    {% if status_item.status == 'error' or status_item.status == 'unknown' %}
        {% set has_api_error = true %}
    {% endif %}
{% endfor %}

{% if has_api_error %}
<div class="alert alert-warning d-flex align-items-center" role="alert" style="font-size:1.05rem;">
    <i class="fas fa-exclamation-triangle mr-2"></i>
    <div>
        <b>API接口异常：</b><br>
        {% for status_item in api_statuses %}
            {% if status_item.status == 'error' or status_item.status == 'unknown' %}
                <span class="text-danger">{{ status_item.service_name|upper }}：</span>
                {{ status_item.error_message|default('未知错误')|replace('\n', '<br>')|safe }}<br>
                <button class="btn btn-sm btn-warning reconnect-api-btn mt-1" data-service="{{ status_item.service_name }}"><i class="fas fa-sync-alt"></i> 重连</button>
                <button class="btn btn-sm btn-info mt-1" onclick="alert('1. 检查Mock服务是否已启动（如：python mock_api_server.py）\n2. 检查.env配置是否正确指向Mock API\n3. 点击\'重连\'按钮尝试自动恢复\n如仍有问题请联系管理员。')"><i class="fas fa-question-circle"></i> 查看帮助</button>
                <br>
            {% endif %}
        {% endfor %}
    </div>
</div>
{% endif %}

{% endblock %}

{% block styles %}
    {{ super() }}
    <style>
        .address-suggestions {
            position: relative;
            z-index: 1050;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
        }

        .suggestion-item:hover {
            background-color: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-main {
            font-weight: 500;
            color: #333;
        }

        .suggestion-detail {
            font-size: 0.85em;
            color: #666;
            margin-top: 2px;
        }

        .suggestions-loading {
            padding: 12px;
            text-align: center;
            color: #666;
        }

        .suggestions-empty {
            padding: 12px;
            text-align: center;
            color: #999;
            font-style: italic;
        }

        .address-input.suggestions-active {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }

        .btn-outline-info.active {
            background-color: #17a2b8;
            color: white;
        }
    </style>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- Chart.js库 -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(function(){
  // ===== 订单统计图表功能 =====
  let orderChart = null;
  let currentTimeRange = 'month';
  let loadingTimer = null;  // 加载计时器
  let isLoading = false;    // 加载状态标记

  // 初始化图表
  function initChart() {
    loadChartData(currentTimeRange);
  }

  // 加载图表数据
  function loadChartData(timeRange, skipLoading = false) {
    // 只有在不跳过loading且当前未在加载时才显示loading
    if (!skipLoading && !isLoading) {
      showChartLoading();
    }

    // 构建API参数 - 重用现有门店选择逻辑
    let params = { time_range: timeRange };

    // 获取当前选择的门店（重用现有逻辑）
    const selectedStore = $('input[name="storeRadio"]:checked').val();
    if (selectedStore && selectedStore !== 'all') {
      params.store_name = selectedStore;
    } else {
      params.admin = 'true';  // 全部门店模式
    }

    $.ajax({
      url: '/api/orders/chart-statistics',
      method: 'GET',
      data: params,
      success: function(response) {
        if (response.success && response.data) {
          renderChart(response.data);
        } else {
          showNoDataMessage();
        }
      },
      error: function(xhr) {
        console.error('加载图表数据失败:', xhr);
        showNoDataMessage();
      },
      complete: function() {
        // 使用统一的隐藏函数，确保最小显示时间
        hideChartLoading();
      }
    });
  }

  // 渲染图表
  function renderChart(data) {
    const ctx = document.getElementById('orderStatisticsChart').getContext('2d');

    // 销毁现有图表
    if (orderChart) {
      orderChart.destroy();
    }

    // 检查是否有数据
    if (!data.dates || data.dates.length === 0) {
      showNoDataMessage();
      return;
    }

    $('#chartContainer').show();

    // 更新图表汇总信息
    updateChartSummary(data);

    // 创建新图表 - 双Y轴柱形图
    orderChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: data.dates,
        datasets: [
          {
            label: '订单数量',
            data: data.order_counts,
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1,
            yAxisID: 'y'
          },
          {
            label: '订单金额(元)',
            data: data.amounts,
            backgroundColor: 'rgba(255, 99, 132, 0.6)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1,
            yAxisID: 'y1'
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          title: {
            display: true,
            text: data.store_name + ' - ' + getTimeRangeText(data.time_range),
            font: {
              size: 14
            }
          },
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 20
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: '时间'
            },
            grid: {
              display: false
            }
          },
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: '订单数量'
            },
            beginAtZero: true,
            ticks: {
              stepSize: 1,
              precision: 0
            }
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
              display: true,
              text: '金额(元)'
            },
            beginAtZero: true,
            grid: {
              drawOnChartArea: false
            },
            ticks: {
              callback: function(value) {
                return '¥' + value.toFixed(0);
              }
            }
          }
        },
        layout: {
          padding: {
            top: 10,
            bottom: 10
          }
        }
      }
    });
  }

  // 显示无数据消息
  function showNoDataMessage() {
    $('#chartContainer').hide();
    $('#chartNoData').show();
    $('#chartSummary').hide();
  }

  // 更新图表汇总信息
  function updateChartSummary(data) {
    if (!data || !data.summary) {
      $('#chartSummary').hide();
      return;
    }

    // 更新时间范围文本
    $('#summaryTimeRange').text(getTimeRangeText(data.time_range));

    // 更新订单总数
    $('#summaryTotalOrders').text(data.summary.total_orders || 0);

    // 更新订单总金额（格式化为千分位）
    const totalAmount = data.summary.total_amount || 0;
    $('#summaryTotalAmount').text(formatCurrency(totalAmount));

    // 显示汇总信息
    $('#chartSummary').show();
  }

  // 格式化金额（添加千分位分隔符）
  function formatCurrency(amount) {
    if (amount === 0) return '0';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }

  // 显示图表加载状态（统一管理，避免快闪）
  function showChartLoading(minDuration = 2500) {
    // 如果已经在加载中，不重复显示
    if (isLoading) {
      return;
    }

    isLoading = true;
    $('#chartLoadingIndicator').show();
    $('#chartContainer').hide();
    $('#chartNoData').hide();
    $('#chartSummary').hide();

    // 清除之前的计时器
    if (loadingTimer) {
      clearTimeout(loadingTimer);
    }

    // 设置最小显示时间
    loadingTimer = setTimeout(function() {
      isLoading = false;
    }, minDuration);
  }

  // 隐藏图表加载状态（只有在最小时间后才真正隐藏）
  function hideChartLoading() {
    if (isLoading) {
      // 如果还在最小显示时间内，延迟隐藏
      setTimeout(hideChartLoading, 100);
      return;
    }

    $('#chartLoadingIndicator').hide();
    if (loadingTimer) {
      clearTimeout(loadingTimer);
      loadingTimer = null;
    }
  }

  // 获取时间范围文本
  function getTimeRangeText(range) {
    const texts = {
      'day': '最近30天',
      'week': '最近12周',
      'month': '最近12个月',
      'year': '最近5年'
    };
    return texts[range] || range;
  }

  // 时间范围切换
  $('.time-range-btn').click(function() {
    const newRange = $(this).data('range');
    if (newRange !== currentTimeRange) {
      $('.time-range-btn').removeClass('active');
      $(this).addClass('active');
      currentTimeRange = newRange;

      // 显示加载状态，提供即时反馈（2秒最小显示时间）
      showChartLoading(2000);
      loadChartData(currentTimeRange, true);  // 跳过重复loading
    }
  });

  // 门店切换时重新加载图表数据并切换门店
  $('input[name="storeRadio"]').change(function(){
    var storeName = $(this).val();

    // 立即显示图表加载状态，提供即时反馈（2.5秒最小显示时间）
    showChartLoading(2500);

    // 先切换门店session，再更新图表
    if(storeName === 'all'){
      // 选择全部门店，清除session中的store_name
      $.post('/api/select_store_by_name/all', function(resp){
        if(resp.success){
          // 门店切换成功后，重新加载图表（跳过loading，因为已经显示了）
          loadChartData(currentTimeRange, true);
          // 更新其他统计数据
          updatePageStatistics();
        }else{
          alert(resp.message || '切换到全部门店失败');
          hideChartLoading();
        }
      }).fail(function(){
        alert('切换门店失败，请检查网络连接');
        hideChartLoading();
      });
    } else {
      $.post('/api/select_store_by_name/' + encodeURIComponent(storeName), function(resp){
        if(resp.success){
          // 门店切换成功后，重新加载图表（跳过loading，因为已经显示了）
          loadChartData(currentTimeRange, true);
          // 更新其他统计数据
          updatePageStatistics();
        }else{
          alert(resp.message || '切换门店失败');
          hideChartLoading();
        }
      }).fail(function(){
        alert('切换门店失败，请检查网络连接');
        hideChartLoading();
      });
    }
  });

  // 更新页面统计数据（不刷新整个页面）
  function updatePageStatistics() {
    // 获取当前选择的门店
    const selectedStore = $('input[name="storeRadio"]:checked').val();

    // 构建API参数
    let params = {};
    if (selectedStore && selectedStore !== 'all') {
      params.store_name = selectedStore;
    } else {
      params.admin = 'true';  // 全部门店模式
    }

    // 调用仪表盘统计API
    $.ajax({
      url: '/api/orders/dashboard-statistics',
      method: 'GET',
      data: params,
      success: function(response) {
        if (response.success && response.data) {
          updateStatisticsCards(response.data);
        } else {
          console.error('获取统计数据失败:', response.error);
        }
      },
      error: function(xhr) {
        console.error('获取统计数据失败:', xhr);
      }
    });
  }

  // 更新统计卡片显示
  function updateStatisticsCards(data) {
    // 更新客户总数
    $('#totalCustomersCount').text(data.total_customers);

    // 更新订单总数
    $('#totalOrdersCount').text(data.total_orders);

    // 更新订单状态统计
    $('#pendingOrdersCount').text(data.pending_orders);
    $('#inProgressOrdersCount').text(data.in_progress_orders);
    $('#completedOrdersCount').text(data.completed_orders);

    // 添加简单的动画效果
    $('.card').addClass('animate__animated animate__pulse');
    setTimeout(function() {
      $('.card').removeClass('animate__animated animate__pulse');
    }, 600);
  }

  // 页面加载完成后初始化图表
  initChart();

  // ===== 原有功能保持不变 =====

  // ===== 快速订单地址建议功能 =====
  var quickOrderSuggestionsEnabled = {
    pickup: false,
    destination: false
  };

  var quickSuggestionTimers = {
    pickup: null,
    destination: null
  };

  // 地址建议切换按钮事件
  $('#toggleQuickPickupSuggestions').click(function() {
    toggleQuickAddressSuggestions('pickup');
  });

  $('#toggleQuickDestSuggestions').click(function() {
    toggleQuickAddressSuggestions('destination');
  });

  // 地址输入事件
  $('#quickPickup').on('input', function() {
    handleQuickAddressInput('pickup', $(this).val());
    checkOrderBtnState();
  });

  $('#quickDest').on('input', function() {
    handleQuickAddressInput('destination', $(this).val());
    checkOrderBtnState();
  });

  // 切换地址建议功能
  function toggleQuickAddressSuggestions(type) {
    var isEnabled = quickOrderSuggestionsEnabled[type];
    quickOrderSuggestionsEnabled[type] = !isEnabled;

    var $toggleBtn = $('#toggleQuick' + (type === 'pickup' ? 'Pickup' : 'Dest') + 'Suggestions');
    var $suggestionsDiv = $('#quick' + (type === 'pickup' ? 'Pickup' : 'Dest') + 'Suggestions');

    if (quickOrderSuggestionsEnabled[type]) {
      $toggleBtn.addClass('active').attr('title', '关闭地址建议');
    } else {
      $toggleBtn.removeClass('active').attr('title', '开启地址建议');
      $suggestionsDiv.hide();
      $('#quick' + (type === 'pickup' ? 'Pickup' : 'Dest')).removeClass('suggestions-active');
    }
  }

  // 处理地址输入
  function handleQuickAddressInput(type, value) {
    if (!quickOrderSuggestionsEnabled[type]) {
      return;
    }

    // 清除之前的计时器
    if (quickSuggestionTimers[type]) {
      clearTimeout(quickSuggestionTimers[type]);
    }

    // 如果输入为空或太短，隐藏建议
    if (!value || value.trim().length < 2) {
      hideQuickSuggestions(type);
      return;
    }

    // 检查是否为店面地址
    if (isQuickStoreAddress(value)) {
      hideQuickSuggestions(type);
      return;
    }

    // 防抖处理，500ms后执行搜索
    quickSuggestionTimers[type] = setTimeout(function() {
      searchQuickAddressSuggestions(type, value.trim());
    }, 500);
  }

  // 判断是否为店面地址
  function isQuickStoreAddress(address) {
    if (!address) return false;

    // 检查常见店面关键词
    var storeKeywords = ['康众汽配', '门店', '店面', '汽配店', '修理厂'];
    for (var i = 0; i < storeKeywords.length; i++) {
      if (address.includes(storeKeywords[i])) {
        return true;
      }
    }

    return false;
  }

  // 搜索地址建议
  function searchQuickAddressSuggestions(type, query) {
    var suggestionsId = 'quick' + (type === 'pickup' ? 'Pickup' : 'Dest') + 'Suggestions';
    var inputId = 'quick' + (type === 'pickup' ? 'Pickup' : 'Dest');

    var $suggestionsDiv = $('#' + suggestionsId);
    var $addressInput = $('#' + inputId);

    // 显示加载状态
    showQuickSuggestionsLoading(type);
    $addressInput.addClass('suggestions-active');

    $.ajax({
      url: '/api/geocoding/suggestions',
      method: 'POST',
      contentType: 'application/json',
      data: JSON.stringify({
        query: query,
        city: '深圳市',
        limit: 6
      }),
      success: function(response) {
        if (response.success && response.suggestions && response.suggestions.length > 0) {
          showQuickAddressSuggestions(type, response.suggestions);
        } else {
          showQuickSuggestionsEmpty(type);
        }
      },
      error: function(xhr) {
        console.error('获取地址建议失败:', xhr);
        showQuickSuggestionsEmpty(type);
      }
    });
  }

  // 显示地址建议
  function showQuickAddressSuggestions(type, suggestions) {
    var suggestionsId = 'quick' + (type === 'pickup' ? 'Pickup' : 'Dest') + 'Suggestions';
    var $suggestionsDiv = $('#' + suggestionsId);
    var html = '';

    suggestions.forEach(function(suggestion) {
      var mainAddress = suggestion.address || suggestion.formatted_address || '';
      var detailAddress = suggestion.formatted_address || '';

      // 如果主地址和详细地址相同，只显示一个
      if (mainAddress === detailAddress) {
        detailAddress = '';
      }

      html += '<div class="suggestion-item" data-address="' +
              (suggestion.formatted_address || suggestion.address || '') + '">';
      html += '<div class="suggestion-main">' + mainAddress + '</div>';
      if (detailAddress) {
        html += '<div class="suggestion-detail">' + detailAddress + '</div>';
      }
      html += '</div>';
    });

    $suggestionsDiv.html(html).show();

    // 绑定点击事件
    $suggestionsDiv.find('.suggestion-item').click(function() {
      var address = $(this).data('address');
      var inputId = 'quick' + (type === 'pickup' ? 'Pickup' : 'Dest');
      $('#' + inputId).val(address);
      hideQuickSuggestions(type);
      checkOrderBtnState();
    });
  }

  // 显示加载状态
  function showQuickSuggestionsLoading(type) {
    var suggestionsId = 'quick' + (type === 'pickup' ? 'Pickup' : 'Dest') + 'Suggestions';
    var $suggestionsDiv = $('#' + suggestionsId);
    $suggestionsDiv.html('<div class="suggestions-loading"><i class="fas fa-spinner fa-spin"></i> 搜索中...</div>').show();
  }

  // 显示空结果
  function showQuickSuggestionsEmpty(type) {
    var suggestionsId = 'quick' + (type === 'pickup' ? 'Pickup' : 'Dest') + 'Suggestions';
    var $suggestionsDiv = $('#' + suggestionsId);
    $suggestionsDiv.html('<div class="suggestions-empty">未找到相关地址</div>').show();

    // 3秒后自动隐藏
    setTimeout(function() {
      hideQuickSuggestions(type);
    }, 3000);
  }

  // 隐藏建议
  function hideQuickSuggestions(type) {
    var suggestionsId = 'quick' + (type === 'pickup' ? 'Pickup' : 'Dest') + 'Suggestions';
    var inputId = 'quick' + (type === 'pickup' ? 'Pickup' : 'Dest');
    $('#' + suggestionsId).hide();
    $('#' + inputId).removeClass('suggestions-active');
  }

  // 打开模态框（保留原有功能）
  $('#quickCreateOrderBtn').click(function(e){
    e.preventDefault();
    $('#quickOrderModal').modal('show');
    $('#customerSearchInput').val('');
    $('#customerList').empty();
    $('#quickOrderForm')[0].reset();
    $('#quickOrderCustomerId').val('');
    $('#selectedCustomerTip').remove();

    // 重置地址建议状态
    quickOrderSuggestionsEnabled.pickup = false;
    quickOrderSuggestionsEnabled.destination = false;
    $('#toggleQuickPickupSuggestions, #toggleQuickDestSuggestions').removeClass('active');
    hideQuickSuggestions('pickup');
    hideQuickSuggestions('destination');

    checkOrderBtnState();
    // 拉取最新5个客户
    loadCustomerList('', 5);
  });

  // 客户检索（防抖）
  let debounceTimer = null;
  $('#customerSearchInput').on('input', function(){
    var q = $(this).val();
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(function(){
      if(q.length > 0){
        loadCustomerList(q);
      } else {
        loadCustomerList('', 5);
      }
    }, 300);
  });

  // 选择客户
  $('#customerList').on('click', '.customer-item', function(){
    var customer = $(this).data('customer');
    $('#quickOrderCustomerId').val(customer.id);
    // 送达地址自动填充为客户地址
    $('#quickDest').val(customer.home_address || '');
    $('#quickRemarks').val('');
    $('#customerList .customer-item').removeClass('active bg-info text-white');
    $(this).addClass('active bg-info text-white');
    // 显示客户名提示
    $('#selectedCustomerTip').remove();
    $('#quickPickup').closest('.form-group').before('<div id="selectedCustomerTip" class="alert alert-info py-2 mb-2">已选择客户：<b>' + customer.name + '</b>，可为该客户下单</div>');
    checkOrderBtnState();
  });

  // 地址输入时校验下单按钮
  $('#quickPickup, #quickDest').on('input', function(){
    checkOrderBtnState();
  });

  // 校验下单按钮可用性
  function checkOrderBtnState(){
    var pickup = $('#quickPickup').val().trim();
    var dest = $('#quickDest').val().trim();
    var customerId = $('#quickOrderCustomerId').val();
    var $btn = $('#quickOrderForm').find('button[type=submit]');
    if(customerId && pickup && dest){
      $btn.prop('disabled', false);
    }else{
      $btn.prop('disabled', true);
    }
  }

  // 提交订单
  $('#quickOrderForm').submit(function(e){
    e.preventDefault();
    var customerId = $('#quickOrderCustomerId').val();
    var pickup = $('#quickPickup').val().trim();
    var dest = $('#quickDest').val().trim();
    if(!customerId){
      alert('请先选择客户');
      return;
    }
    if(!pickup){
      alert('取车地址不能为空');
      $('#quickPickup').focus();
      return;
    }
    if(!dest){
      alert('送达地址不能为空');
      $('#quickDest').focus();
      return;
    }
    var formData = $(this).serialize();
    var $btn = $(this).find('button[type=submit]');
    $btn.prop('disabled', true).text('提交中...');
    $.post('/api/create_edaijia_order/' + customerId, formData)
      .done(function(resp){
        alert(resp.message || '订单已成功创建！');
        $('#quickOrderModal').modal('hide');
        window.location.href = '/orders';
      })
      .fail(function(xhr){
        alert('下单失败: ' + (xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : '未知错误'));
      })
      .always(function(){
        $btn.prop('disabled', false).text('提交订单');
      });
  });

  // 客户列表加载函数，支持limit参数
  function loadCustomerList(q, limit){
    $('#customerList').html('<div class="text-center text-muted py-2"><span class="spinner-border spinner-border-sm"></span> 加载中...</div>');
    var params = {q: q};
    if(limit) params.limit = limit;
    $.ajax({
      url: '/api/customers',
      data: params,
      type: 'GET',
      headers: { 'X-Requested-With': 'XMLHttpRequest' },
      success: function(resp){
        if(resp && resp.customers && resp.customers.length > 0){
          var html = resp.customers.map(function(c){
            return '<div class="customer-item px-3 py-2 mb-1" style="cursor:pointer;border-radius:4px;" data-customer=\'' + JSON.stringify(c) + '\'>'
              + '<b>' + c.name + '</b> <span class="text-muted small">' + c.phone + '</span> '
              + (c.plate_number ? '<span class="badge badge-light">' + c.plate_number + '</span> ' : '')
              + (c.car_model ? '<span class="text-muted small">' + c.car_model + '</span>' : '')
              + '<br><span class="text-muted small">' + (c.home_address || '-') + '</span>'
              + '</div>';
          }).join('');
          $('#customerList').html(html);
        }else{
          $('#customerList').html('<div class="text-center text-muted py-2">无匹配客户</div>');
        }
      },
      error: function(){
        $('#customerList').html('<div class="text-center text-danger py-2">客户数据加载失败</div>');
      }
    });
  }

  // 门店切换 - 新架构：基于门店名称（已在上面处理图表联动）
  // 注意：门店切换的图表联动逻辑已在上面的 $('input[name="storeRadio"]').change() 中处理

  // 生成mock数据
  $('#generateMockDataBtn').click(function(){
    var $btn = $(this);
    $btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 生成中...');
    $.post('/dev/generate_mock_data', function(resp){
      alert(resp.message);
      location.reload();
    }).fail(function(xhr){
      alert('生成失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : '未知错误'));
    }).always(function(){
      $btn.prop('disabled', false).html('<i class="fas fa-magic"></i> 生成Mock数据');
    });
  });

  // API重连按钮增强：重连后自动刷新门店radio和统计
  $('.reconnect-api-btn').click(function(){
    var $btn = $(this);
    var service = $btn.data('service');
    $btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 重连中...');
    $.post('/api/reconnect/' + service, function(resp){
      alert(resp.message || 'API已重连');
      // 自动刷新门店radio和统计，无需手动刷新
      location.reload();
    }).fail(function(xhr){
      alert('重连失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : '未知错误'));
    }).always(function(){
      $btn.prop('disabled', false).html('<i class="fas fa-sync-alt"></i> <span class="d-none d-md-inline">重连</span>');
    });
  });
});
</script>
{% endblock %}
