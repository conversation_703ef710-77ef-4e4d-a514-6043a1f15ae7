from flask import render_template, flash, redirect, url_for, request, jsonify, current_app, session
from app.main import bp
from app.models import Customer, Order, APIStatus, Store
from app import db
from datetime import datetime
from app.main.forms import CreateOrderForm
from sqlalchemy import or_
from app.api.routes import sync_f6_customers_core
import requests
import hashlib
import json
from random import randint, choice

@bp.route('/')
@bp.route('/index')
def index():
    from flask import session
    # 新架构：不再使用Store表，门店信息完全来自F6页面
    # 从Order表中动态获取所有门店名称
    store_names = db.session.query(Order.store_name).distinct().filter(
        Order.store_name.isnot(None),
        Order.store_name != ''
    ).all()
    stores = [{'name': name[0]} for name in store_names]
    # 若数据库无客户，自动同步一次
    if Customer.query.count() == 0:
        try:
            sync_f6_customers_core()
        except Exception as e:
            current_app.logger.warning(f"首页自动同步F6客户失败: {e}")
    # Ensure both API statuses are present, create if not
    f6_status = APIStatus.query.filter_by(service_name='f6').first()
    if not f6_status:
        f6_status = APIStatus(service_name='f6', status='unknown', last_check=datetime.utcnow())
        db.session.add(f6_status)

    edaijia_status = APIStatus.query.filter_by(service_name='edaijia').first()
    if not edaijia_status:
        edaijia_status = APIStatus(service_name='edaijia', status='unknown', last_check=datetime.utcnow())
        db.session.add(edaijia_status)
    db.session.commit() # Commit any new status entries

    # ===== F6 Mock API 相关代码已注释 =====
    # 注释原因：根据最新方案，不需要连接F6的API了
    # 自动检测API存活
    # 检查F6 mock server
    # app_key = current_app.config.get('F6_API_KEY')
    # app_secret = current_app.config.get('F6_API_SECRET')
    # base_url = current_app.config.get('F6_API_BASE_URL', 'http://127.0.0.1:5001')
    # url = base_url.rstrip('/') + '/customer/list'
    # uri = '/customer/list'
    # body_dict = {"paramValues": [{"pageSize": 1, "currentPage": 1}]}
    # body = json.dumps(body_dict, ensure_ascii=False)

    # # 使用简化的签名算法进行API存活检查
    # def f6_calc_signature_simple(app_key, app_secret, body):
    #     sign_str = f"{app_key}{body}{app_secret}"
    #     return hashlib.md5(sign_str.encode('utf-8')).hexdigest()

    # signature = f6_calc_signature_simple(app_key, app_secret, body)
    # headers = {
    #     "Content-Type": "application/json",
    #     "X-Ca-Key": app_key,
    #     "X-Ca-Signature": signature
    # }
    f6_api_available = False  # 直接设置为False，不再检查F6 API
    # try:
    #     resp = requests.post(url, data=body, headers=headers, timeout=3)
    #     if resp.status_code == 200 and resp.json().get('code') == 200:
    #         f6_status.status = 'normal'
    #         f6_status.error_message = None
    #         f6_api_available = True
    #     else:
    #         f6_status.status = 'error'
    #         f6_status.error_message = 'F6 Mock API无法访问或签名认证失败。\n推荐做法：1. 检查Mock服务是否已启动（如：python mock_api_server.py）。2. 检查.env配置。3. 点击下方"重连"按钮。'
    # except Exception as e:
    #     f6_status.status = 'error'
    #     f6_status.error_message = f'F6 Mock API连接失败：{e}\n推荐做法：1. 检查Mock服务是否已启动（如：python mock_api_server.py）。2. 检查.env配置。3. 点击下方"重连"按钮。'

    # 设置F6状态为已禁用
    f6_status.status = 'disabled'
    f6_status.error_message = 'F6 API已禁用 - 根据最新方案，不需要连接F6的API'
    f6_status.last_check = datetime.utcnow()
    db.session.commit()

    # F6 API可用时自动同步门店 - 已注释
    # if f6_api_available:
    #     try:
    #         from app.api.routes import sync_f6_stores
    #         with current_app.test_request_context():
    #             sync_f6_stores()
    #     except Exception as e:
    #         current_app.logger.warning(f"自动同步F6门店失败: {e}")

    # 检查e代驾 mock server（如有独立API可加，否则同F6）
    # 这里只做F6演示，如需e代驾同理可扩展

    api_statuses = APIStatus.query.order_by(APIStatus.service_name).all()

    # 新架构：基于门店名称的统计，不再使用Store表
    selected_store_name = session.get('current_store_name')

    if selected_store_name:
        # 特定门店模式：直接按store_name过滤
        order_query = Order.query.filter(Order.store_name == selected_store_name)
        # 客户总数：使用去重统计（基于车牌号+手机号+姓名）
        total_customers = get_unique_customer_count_by_store_name(selected_store_name)
    else:
        # 全部门店模式
        order_query = Order.query
        # 客户总数：全部门店的去重统计
        total_customers = get_unique_customer_count_by_store_name(None)

    pending_orders_count = order_query.filter(Order.status == 'pending').count()
    in_progress_orders_count = order_query.filter(Order.status == 'in_progress').count()
    completed_orders_count = order_query.filter(Order.status == 'completed').count()
    # 统计所有类型的取消订单
    cancelled_orders_count = order_query.filter(Order.status.in_(['cancelled', 'cancelled_customer', 'cancelled_driver'])).count()

    total_orders = order_query.count()
    recent_orders = order_query.order_by(Order.created_at.desc()).limit(5).all()

    return render_template('index.html', title='首页',
                         api_statuses=api_statuses,
                         pending_orders=pending_orders_count,
                         in_progress_orders=in_progress_orders_count,
                         completed_orders=completed_orders_count,
                         cancelled_orders=cancelled_orders_count,
                         total_customers=total_customers,
                         total_orders=total_orders,
                         recent_orders=recent_orders,
                         stores=stores,
                         selected_store_name=selected_store_name)

@bp.route('/quick_access')
def quick_access():
    """
    快速访问页面 - 专为从F6插件跳转设计
    URL参数：
    - customer_phone: 客户手机号
    - manager_name: 店长姓名
    - action: 要执行的操作
    """
    customer_phone = request.args.get('customer_phone')
    manager_name = request.args.get('manager_name')
    action = request.args.get('action', 'view_orders')

    # 如果提供了店长姓名，尝试自动认证
    if manager_name and 'authenticated_manager' not in session:
        from app.api.routes import authenticate_manager_by_name
        auth_info = authenticate_manager_by_name(manager_name)
        if auth_info:
            session['authenticated_manager'] = auth_info
            flash(f'已自动登录：{manager_name} ({auth_info["role"]})', 'success')
        else:
            flash(f'无法认证店长身份：{manager_name}', 'error')
            return redirect(url_for('main.login'))

    # 如果仍未认证，跳转到登录页
    if 'authenticated_manager' not in session:
        return redirect(url_for('main.login'))

    # 根据action执行不同操作
    if action == 'view_orders' and customer_phone:
        # 跳转到订单页面并搜索该客户的订单
        return redirect(url_for('main.orders', q=f'phone:{customer_phone}'))
    elif action == 'create_order':
        # 跳转到创建订单页面
        return redirect(url_for('main.create_order'))
    else:
        # 默认跳转到首页
        return redirect(url_for('main.index'))

@bp.route('/customers')
def customers():
    from flask import session
    # 新架构：基于门店名称，不再使用store_id
    selected_store_name = session.get('current_store_name')
    page = request.args.get('page', 1, type=int)
    search_query = request.args.get('q', '')

    # 注意：Customer表仍然使用store_id，这里暂时显示所有客户
    # 未来可以考虑也迁移Customer表到基于store_name
    query = Customer.query  # 显示所有门店的客户
    if search_query:
        search_term = f'%{search_query}%'
        query = query.filter(or_(
            Customer.name.ilike(search_term),
            Customer.phone.ilike(search_term),
            Customer.car_model.ilike(search_term),
            Customer.plate_number.ilike(search_term),
            Customer.f6_customer_id.ilike(search_term) # Allow searching by F6 ID too
        ))

    customers_pagination = query.order_by(Customer.id.desc()).paginate(
        page=page, per_page=current_app.config['POSTS_PER_PAGE'], error_out=False
    )
    next_url = url_for('main.customers', page=customers_pagination.next_num, q=search_query) \
        if customers_pagination.has_next else None
    prev_url = url_for('main.customers', page=customers_pagination.prev_num, q=search_query) \
        if customers_pagination.has_prev else None

    return render_template('customers.html', title='客户管理',
                           customers=customers_pagination.items,
                           next_url=next_url, prev_url=prev_url,
                           pagination=customers_pagination, current_page=page,
                           search_query=search_query)

@bp.route('/orders')
def orders():
    from flask import session
    # 新架构：基于门店名称，不再使用store_id
    selected_store_name = session.get('current_store_name')
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', None)
    search_query = request.args.get('q', '')

    # 订单查询：支持全部门店和特定门店
    if selected_store_name:
        # 特定门店模式：直接按store_name过滤
        query = Order.query.filter(Order.store_name == selected_store_name)
    else:
        # 全部门店模式
        query = Order.query

    if search_query:
        search_term = f'%{search_query}%'
        # 搜索条件：支持新旧两种数据结构
        search_conditions = [
            Order.edaijia_order_id.ilike(search_term),
            Order.pickup_address.ilike(search_term),
            Order.destination_address.ilike(search_term),
            Order.customer_name.ilike(search_term),  # 新字段
            Order.customer_phone.ilike(search_term),  # 新字段
            Order.manager_name.ilike(search_term),    # 新字段
        ]

        # 兼容旧的Customer表搜索
        query = query.outerjoin(Customer, Order.customer_id == Customer.id)
        search_conditions.extend([
            Customer.name.ilike(search_term),
            Customer.phone.ilike(search_term)
        ])

        query = query.filter(or_(*search_conditions))

    # 状态过滤 - 基于e代驾状态码
    if status_filter and status_filter != 'all':
        if status_filter == 'cancelled':
            # "取消"选项显示所有类型的取消订单
            query = query.filter(Order.edj_status_code.in_(['403', '404']))
        elif status_filter == 'completed':
            query = query.filter(Order.edj_status_code == '304')
        elif status_filter == 'pending':
            query = query.filter(Order.edj_status_code.in_(['102', '180']))
        elif status_filter == 'in_progress':
            query = query.filter(Order.edj_status_code.in_(['301', '302', '303']))
        elif status_filter == 'failed':
            query = query.filter(Order.edj_status_code == '506')

    orders_pagination = query.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=current_app.config['POSTS_PER_PAGE'], error_out=False
    )
    next_url = url_for('main.orders', page=orders_pagination.next_num, status=status_filter, q=search_query) \
        if orders_pagination.has_next else None
    prev_url = url_for('main.orders', page=orders_pagination.prev_num, status=status_filter, q=search_query) \
        if orders_pagination.has_prev else None

    def order_to_dict(order):
        # 下单时间
        order_time = order.order_time if order.order_time else int(order.created_at.timestamp()) if order.created_at else None
        # 派单超时
        timeout = order.timeout if order.timeout else 300
        # 完成时间
        finish_time = None
        if order.created_at and timeout:
            from datetime import datetime, timezone, timedelta
            finish_dt = order.created_at + timedelta(seconds=timeout)
            finish_time = finish_dt.strftime('%Y-%m-%d %H:%M:%S')
        # 状态显示
        status_display = '完成' if order.status == 'completed' else order.get_status_display() if hasattr(order, 'get_status_display') else order.status
        # 客户信息：优先使用新字段，兼容旧字段
        customer_name = order.customer_name or (order.customer.name if order.customer else "")
        customer_phone = order.customer_phone or (order.customer.phone if order.customer else "")

        return {
            "edaijia_order_id": order.edaijia_order_id,
            "edj_booking_id": order.edj_booking_id or '-',
            "customer": {
                "name": customer_name,
                "phone": customer_phone
            },
            "car_info": {
                "model": order.car_model or '-',
                "plate": order.plate_number or '-'
            },
            "manager_info": {
                "name": order.manager_name or '-',
                "phone": order.manager_phone or '-'
            },
            "pickup_address": order.pickup_address,
            "destination_address": order.destination_address,
            "status": order.status,
            "status_display": status_display,
            "driver_name": order.driver_name,
            "driver_phone": order.driver_phone,
            "price": order.price,
            "order_time": order_time,
            "timeout": timeout,
            "channel": order.channel or '-',
            "from_source": order.from_source or '-',
            "dynamic_fee": order.dynamic_fee or '-',
            "bonus_sn": order.bonus_sn or '-',
            "edj_status_code": order.edj_status_code or '-',
            "edj_status_desc": order.edj_status_desc or '-',
            "finish_time": finish_time or '-',
        }
    orders_json = [order_to_dict(o) for o in orders_pagination.items]

    return render_template('orders.html', title='订单管理', orders=orders_pagination.items,
                           orders_json=orders_json,
                           next_url=next_url, prev_url=prev_url,
                           pagination=orders_pagination, current_page=page,
                           current_status=status_filter if status_filter else 'all',
                           search_query=search_query)

@bp.route('/create_order/<int:customer_id>', methods=['GET', 'POST'])
def create_order(customer_id):
    customer = Customer.query.get_or_404(customer_id) if customer_id != 0 else None
    form = CreateOrderForm()

    if customer is None and customer_id == 0:
        # This is for the generic "Create Order" link from homepage, which is currently a placeholder.
        # We can decide to pre-fill nothing or handle it differently.
        # For now, just pass an empty form and no specific customer.
        flash('请先从客户列表选择一位客户来创建订单，或将来支持在此直接创建无关联客户订单。目前请选择客户。注：首页的"创建订单"按钮仅为示例。 ', 'info')
        # return redirect(url_for('main.customers')) # Or redirect to customer list
        # Or render create_order with a message that a customer should be selected.
        pass # Proceed to render template with no customer

    # The actual POST logic is handled via AJAX to /api/create_edaijia_order/<customer_id>
    # This route mainly serves the form for GET requests.
    # If we wanted traditional form submission, we'd add: if form.validate_on_submit(): ...

    return render_template('create_order.html', title='创建e代驾订单',
                           customer=customer, form=form)

@bp.route('/dev/generate_mock_data', methods=['POST'])
def generate_mock_data():
    """
    为当前门店批量生成mock客户和订单数据（仅开发/测试环境可用）。
    """
    store_id = session.get('current_store_id')
    if not store_id:
        return jsonify({'success': False, 'message': '未选择门店'}), 400
    # 清空当前门店下的客户和订单
    Customer.query.filter_by(store_id=store_id).delete()
    db.session.commit()
    # 生成10个客户
    customers = []
    for i in range(10):
        c = Customer(
            f6_customer_id=f"F6C{store_id}{i+1:03d}",
            name=f"测试客户{store_id}-{i+1}",
            phone=f"138{store_id:02d}{i+1:03d}{randint(100,999)}",
            car_model=choice(['宝马X3','奥迪A4','特斯拉Model3','本田CRV','丰田凯美瑞']),
            plate_number=f"京A{randint(10000,99999)}",
            home_address=f"北京市朝阳区测试路{randint(1,99)}号",
            remarks="自动生成mock数据",
            store_id=store_id
        )
        db.session.add(c)
        customers.append(c)
    db.session.commit()
    # 生成20个订单，状态分布合理
    statuses = ['pending']*6 + ['in_progress']*6 + ['completed']*6 + ['cancelled']*2
    for i in range(20):
        cust = choice(customers)
        o = Order(
            edaijia_order_id=f"EDJ{store_id}{i+1:04d}",
            customer_id=cust.id,
            status=statuses[i%len(statuses)],
            pickup_address=f"北京市朝阳区上车点{randint(1,50)}号",
            destination_address=f"北京市海淀区下车点{randint(1,50)}号",
            price=randint(50,200),
            driver_name=choice(['张师傅','李师傅','王师傅','赵师傅']),
            driver_phone=f"139{store_id:02d}{i+1:03d}{randint(100,999)}",
            remarks="mock订单",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.session.add(o)
    db.session.commit()
    return jsonify({'success': True, 'message': '已为当前门店生成10个客户和20个订单mock数据'})

@bp.route('/dev/generate_a_store_data', methods=['POST'])
def generate_a_store_data():
    """
    为东莞店生成2个客户和6个订单，1待处理，2进行中，3已完成。
    """
    store = Store.query.filter_by(name='东莞店').first()
    if not store:
        return jsonify({'success': False, 'message': '未找到东莞店'}), 404
    # 清空A门店下的客户和订单
    customer_ids = [c.id for c in Customer.query.filter_by(store_id=store.id)]
    Order.query.filter(Order.customer_id.in_(customer_ids)).delete(synchronize_session=False)
    Customer.query.filter_by(store_id=store.id).delete()
    db.session.commit()
    # 新建2个客户
    c1 = Customer(f6_customer_id='A001', name='张三', phone='13800000001', car_model='宝马X3', plate_number='京A12345', home_address='北京朝阳区', remarks='VIP', store_id=store.id)
    c2 = Customer(f6_customer_id='A002', name='李四', phone='13800000002', car_model='奥迪A4', plate_number='京A54321', home_address='北京海淀区', remarks='', store_id=store.id)
    db.session.add_all([c1, c2])
    db.session.commit()
    # 新建6个订单
    from datetime import datetime
    o1 = Order(edaijia_order_id='EDJ1001', customer_id=c1.id, status='pending', pickup_address='望京', destination_address='国贸', price=100, driver_name='王师傅', driver_phone='13900000001', remarks='待处理', created_at=datetime.utcnow(), updated_at=datetime.utcnow())
    o2 = Order(edaijia_order_id='EDJ1002', customer_id=c1.id, status='in_progress', pickup_address='望京', destination_address='中关村', price=120, driver_name='李师傅', driver_phone='13900000002', remarks='进行中', created_at=datetime.utcnow(), updated_at=datetime.utcnow())
    o3 = Order(edaijia_order_id='EDJ1003', customer_id=c2.id, status='in_progress', pickup_address='国贸', destination_address='望京', price=110, driver_name='赵师傅', driver_phone='13900000003', remarks='进行中', created_at=datetime.utcnow(), updated_at=datetime.utcnow())
    o4 = Order(edaijia_order_id='EDJ1004', customer_id=c2.id, status='completed', pickup_address='海淀', destination_address='朝阳', price=130, driver_name='张师傅', driver_phone='13900000004', remarks='已完成', created_at=datetime.utcnow(), updated_at=datetime.utcnow())
    o5 = Order(edaijia_order_id='EDJ1005', customer_id=c2.id, status='completed', pickup_address='朝阳', destination_address='海淀', price=140, driver_name='王师傅', driver_phone='13900000005', remarks='已完成', created_at=datetime.utcnow(), updated_at=datetime.utcnow())
    o6 = Order(edaijia_order_id='EDJ1006', customer_id=c1.id, status='completed', pickup_address='望京', destination_address='国贸', price=150, driver_name='李师傅', driver_phone='13900000006', remarks='已完成', created_at=datetime.utcnow(), updated_at=datetime.utcnow())
    db.session.add_all([o1, o2, o3, o4, o5, o6])
    db.session.commit()
    return jsonify({'success': True, 'message': '东莞店已生成2个客户和6个订单（1待处理，2进行中，3已完成）'})

@bp.route('/customers', methods=['GET'])
def customers_api():
    # 用于快速下单弹窗的客户检索API，兼容前端JS
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.args.get('limit'):
        # 新架构：基于门店名称，暂时显示所有客户
        q = request.args.get('q', '').strip()
        limit = request.args.get('limit', 5, type=int)

        # 注意：Customer表仍然使用store_id，这里暂时显示所有客户
        query = Customer.query  # 显示所有门店的客户
        if q:
            search = f"%{q}%"
            query = query.filter(or_(
                Customer.name.ilike(search),
                Customer.phone.ilike(search),
                Customer.plate_number.ilike(search),
                Customer.car_model.ilike(search),
                Customer.f6_customer_id.ilike(search)
            ))
        customers = query.order_by(Customer.id.desc()).limit(limit).all()
        result = []
        for c in customers:
            result.append({
                'id': c.id,
                'name': c.name,
                'phone': c.phone,
                'car_model': c.car_model,
                'plate_number': c.plate_number,
                'home_address': c.home_address,
                'remarks': c.remarks
            })
        return jsonify({'customers': result})
    # 兼容原有页面渲染
    # ... existing code ...


def get_unique_customer_count_by_store_name(store_name):
    """
    获取指定店面或全部门店的唯一客户数量
    根据车牌号+手机号+姓名组合去重

    Args:
        store_name: 店面名称，如果为None则统计全部门店

    Returns:
        int: 唯一客户数量
    """
    try:
        # 构建查询条件
        query = db.session.query(
            Order.customer_name,
            Order.customer_phone,
            Order.plate_number,
            Order.store_name
        ).filter(
            Order.customer_name.isnot(None),
            Order.customer_name != '',
            Order.plate_number.isnot(None),
            Order.plate_number != ''
        )

        # 如果指定了store_name，则按店面过滤
        if store_name:
            query = query.filter(Order.store_name == store_name)

        orders = query.all()

        # 使用集合去重（基于车牌号+手机号+姓名的组合）
        unique_customers = set()

        for order in orders:
            # 处理空值，统一为空字符串
            customer_name = order.customer_name or ''
            customer_phone = order.customer_phone or ''
            plate_number = order.plate_number or ''

            # 创建唯一标识符（车牌号+手机号+姓名）
            customer_key = f"{plate_number}|{customer_phone}|{customer_name}"
            unique_customers.add(customer_key)

        store_desc = f"店面 {store_name}" if store_name else "全部门店"
        current_app.logger.info(f"{store_desc} 唯一客户统计: 总订单 {len(orders)}, 唯一客户 {len(unique_customers)}")
        return len(unique_customers)

    except Exception as e:
        store_desc = f"店面 {store_name}" if store_name else "全部门店"
        current_app.logger.error(f"统计{store_desc}唯一客户数量失败: {str(e)}")
        return 0