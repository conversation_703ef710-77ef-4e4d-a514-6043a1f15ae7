#!/usr/bin/env python3
"""
测试增强的重复订单检查功能

测试场景：
1. 同一车辆有活跃订单时，尝试下新订单 → 应该被拒绝
2. 同一手机号有活跃订单时，尝试为其他车辆下单 → 应该被拒绝
3. 预约订单时间冲突检查
4. 相同路线24小时内重复检查
"""

import requests
import json
import time
from datetime import datetime, timedelta

# 测试配置
BASE_URL = "http://127.0.0.1:5000"
API_URL = f"{BASE_URL}/api/f6/submit_order"

# 测试数据
TEST_MANAGER = "郑东"
TEST_STORE = "龙华店"

def test_duplicate_order_check():
    """测试重复订单检查功能"""
    print("🧪 开始测试增强的重复订单检查功能")
    print("="*60)
    
    # 测试场景1: 同一车辆重复下单
    print("\n📋 测试场景1: 同一车辆重复下单")
    print("-" * 40)
    
    # 第一次下单
    order1_data = {
        "customer_name": "测试客户1",
        "customer_phone": "13900000001",
        "plate_number": "粤B测试01",
        "car_model": "测试车型",
        "pickup_address": "深圳市南山区科技园",
        "pickup_longitude": 113.9547,
        "pickup_latitude": 22.5431,
        "destination_address": "深圳市福田区中心区",
        "destination_longitude": 114.0579,
        "destination_latitude": 22.5455,
        "order_type": "now",
        "payment_method": "self",
        "manager_name": TEST_MANAGER,
        "store_name": TEST_STORE,
        "remarks": "测试订单1"
    }
    
    print("🔄 第一次下单...")
    response1 = requests.post(API_URL, json=order1_data)
    print(f"状态码: {response1.status_code}")
    print(f"响应: {response1.json()}")
    
    if response1.status_code == 200:
        print("✅ 第一次下单成功")
        
        # 立即尝试第二次下单（相同车辆）
        print("\n🔄 立即尝试第二次下单（相同车辆）...")
        order2_data = order1_data.copy()
        order2_data["destination_address"] = "深圳市宝安区机场"
        order2_data["destination_longitude"] = 113.8206
        order2_data["destination_latitude"] = 22.6390
        order2_data["remarks"] = "测试订单2 - 应该被拒绝"
        
        response2 = requests.post(API_URL, json=order2_data)
        print(f"状态码: {response2.status_code}")
        print(f"响应: {response2.json()}")
        
        if response2.status_code == 409:
            print("✅ 重复订单检查生效 - 正确拒绝了重复订单")
        else:
            print("❌ 重复订单检查失败 - 应该拒绝但没有拒绝")
    else:
        print("❌ 第一次下单失败，无法继续测试")
    
    # 测试场景2: 同一手机号不同车辆
    print("\n📋 测试场景2: 同一手机号不同车辆")
    print("-" * 40)
    
    order3_data = order1_data.copy()
    order3_data["plate_number"] = "粤B测试02"  # 不同车辆
    order3_data["remarks"] = "测试订单3 - 同手机号不同车辆"
    
    print("🔄 尝试为同一手机号的不同车辆下单...")
    response3 = requests.post(API_URL, json=order3_data)
    print(f"状态码: {response3.status_code}")
    print(f"响应: {response3.json()}")
    
    if response3.status_code == 409:
        print("✅ 手机号冲突检查生效 - 正确拒绝了同手机号的其他车辆订单")
    else:
        print("❌ 手机号冲突检查失败")
    
    # 测试场景3: 预约订单时间冲突
    print("\n📋 测试场景3: 预约订单时间冲突")
    print("-" * 40)
    
    # 创建一个预约订单
    future_time = datetime.now() + timedelta(hours=2)
    reserve_time = future_time.strftime('%Y-%m-%dT%H:%M')
    
    order4_data = {
        "customer_name": "测试客户2",
        "customer_phone": "13900000002",
        "plate_number": "粤B测试03",
        "car_model": "测试车型",
        "pickup_address": "深圳市南山区科技园",
        "pickup_longitude": 113.9547,
        "pickup_latitude": 22.5431,
        "destination_address": "深圳市福田区中心区",
        "destination_longitude": 114.0579,
        "destination_latitude": 22.5455,
        "order_type": "reserve",
        "reserve_time": reserve_time,
        "payment_method": "self",
        "manager_name": TEST_MANAGER,
        "store_name": TEST_STORE,
        "remarks": "测试预约订单1"
    }
    
    print(f"🔄 创建预约订单（时间: {reserve_time}）...")
    response4 = requests.post(API_URL, json=order4_data)
    print(f"状态码: {response4.status_code}")
    print(f"响应: {response4.json()}")
    
    if response4.status_code == 200:
        print("✅ 预约订单创建成功")
        
        # 尝试创建冲突的预约订单（30分钟后）
        conflict_time = future_time + timedelta(minutes=30)
        conflict_reserve_time = conflict_time.strftime('%Y-%m-%dT%H:%M')
        
        order5_data = order4_data.copy()
        order5_data["reserve_time"] = conflict_reserve_time
        order5_data["remarks"] = "测试预约订单2 - 时间冲突"
        
        print(f"🔄 尝试创建冲突预约订单（时间: {conflict_reserve_time}）...")
        response5 = requests.post(API_URL, json=order5_data)
        print(f"状态码: {response5.status_code}")
        print(f"响应: {response5.json()}")
        
        if response5.status_code == 409:
            print("✅ 预约时间冲突检查生效")
        else:
            print("❌ 预约时间冲突检查失败")
    
    # 测试场景4: 强制跳过重复检查
    print("\n📋 测试场景4: 强制跳过重复检查")
    print("-" * 40)
    
    order6_data = order1_data.copy()
    order6_data["skip_duplicate_check"] = True
    order6_data["remarks"] = "测试订单6 - 跳过重复检查"
    
    print("🔄 尝试跳过重复检查强制下单...")
    response6 = requests.post(API_URL, json=order6_data)
    print(f"状态码: {response6.status_code}")
    print(f"响应: {response6.json()}")
    
    if response6.status_code == 200:
        print("✅ 跳过重复检查功能正常")
    else:
        print("❌ 跳过重复检查功能异常")
    
    print("\n" + "="*60)
    print("🎉 重复订单检查测试完成！")

def test_order_status_colors():
    """测试订单状态颜色一致性"""
    print("\n🎨 测试订单状态颜色一致性")
    print("-" * 40)
    
    # 访问中间件首页
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            content = response.text
            
            # 检查状态颜色
            if 'badge-danger">已取消' in content:
                print("✅ 首页'已取消'状态使用正确的红色(badge-danger)")
            else:
                print("❌ 首页'已取消'状态颜色不正确")
            
            if 'badge-warning">待处理' in content:
                print("✅ 首页'待处理'状态使用正确的黄色(badge-warning)")
            else:
                print("❌ 首页'待处理'状态颜色不正确")
        else:
            print(f"❌ 无法访问首页，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试状态颜色时发生错误: {str(e)}")

def cleanup_test_orders():
    """清理测试订单"""
    print("\n🧹 清理测试订单")
    print("-" * 40)
    
    try:
        # 这里可以添加清理逻辑，比如调用取消订单API
        print("💡 提示: 请手动清理测试订单，或等待订单自动过期")
    except Exception as e:
        print(f"❌ 清理测试订单时发生错误: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始MyProj2025重复订单检查功能测试")
    print(f"📡 API地址: {API_URL}")
    print(f"🏪 测试店面: {TEST_STORE}")
    print(f"👤 测试店长: {TEST_MANAGER}")
    
    try:
        # 测试重复订单检查
        test_duplicate_order_check()
        
        # 测试状态颜色一致性
        test_order_status_colors()
        
        # 清理测试数据
        cleanup_test_orders()
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
