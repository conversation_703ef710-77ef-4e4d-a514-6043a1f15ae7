#!/usr/bin/env python3
"""
测试订单创建功能
"""

import requests
import json

def test_order_creation():
    """测试订单创建"""
    
    # 测试数据
    order_data = {
        "customer_name": "测试客户",
        "customer_phone": "13800138000",
        "customer_f6_id": "F6_粤B12345_1234567890",
        "car_model": "测试车型",
        "plate_number": "粤B12345",
        "pickup_address": "龙华店",
        "destination_address": "深圳市南山区深圳湾",
        "pickup_longitude": 0,
        "pickup_latitude": 0,
        "destination_longitude": 0,
        "destination_latitude": 0,
        "order_type": "now",
        "contact_phone": "13800138000",
        "payment_method": "self",
        "reserve_time": "",
        "manager_name": "周鑫芸",
        "store_name": "龙华店",
        "remarks": "测试订单"
    }
    
    # 发送请求
    url = "http://localhost:5000/api/f6/submit_order"
    
    print("🧪 测试订单创建功能")
    print("=" * 50)
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(order_data, ensure_ascii=False, indent=2)}")
    print()
    
    try:
        response = requests.post(url, json=order_data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print()
        
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        else:
            print(f"响应内容: {response.text}")
            
        if response.status_code == 200:
            print("✅ 订单创建成功！")
        else:
            print("❌ 订单创建失败！")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

if __name__ == '__main__':
    test_order_creation()
