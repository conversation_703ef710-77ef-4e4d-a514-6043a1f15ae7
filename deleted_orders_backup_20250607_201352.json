{"backup_time": "2025-06-07T20:13:52.205183", "total_orders": 5, "orders": [{"id": 70, "edaijia_order_id": "1749105543574", "store_name": "龙华店", "customer_name": "郑德乐", "customer_phone": "13670559152", "plate_number": "粤BGF8448", "car_model": "零跑 C16 1.5 固定齿比变速器 2024 C16", "manager_name": "周鑫芸", "status": "pending", "pickup_address": "龙华店", "destination_address": "深圳市商汤科技有限公司", "created_at": "2025-06-05T06:39:03.316256", "updated_at": "2025-06-05T06:39:03.316271", "price": null, "driver_name": null, "driver_phone": null, "remarks": "[浏览器插件订单] 类型: 代叫\n操作员: 周鑫芸 (店长)\n联系人手机号: 13800013900\n支付方式: 我来支付\n"}, {"id": 69, "edaijia_order_id": "1749105527671", "store_name": "龙华店", "customer_name": "郑德乐", "customer_phone": "13670559152", "plate_number": "粤BGF8448", "car_model": "零跑 C16 1.5 固定齿比变速器 2024 C16", "manager_name": "周鑫芸", "status": "pending", "pickup_address": "龙华店", "destination_address": "深圳市商汤科技有限公司", "created_at": "2025-06-05T06:38:47.524519", "updated_at": "2025-06-05T06:38:47.524531", "price": null, "driver_name": null, "driver_phone": null, "remarks": "[浏览器插件订单] 类型: 代叫\n操作员: 周鑫芸 (店长)\n联系人手机号: 13800013900\n支付方式: 我来支付\n"}, {"id": 67, "edaijia_order_id": "1749103507417", "store_name": "龙华店", "customer_name": "郑德乐", "customer_phone": "13670559152", "plate_number": "粤BGF8448", "car_model": "零跑 C16 1.5 固定齿比变速器 2024 C16", "manager_name": "周鑫芸", "status": "pending", "pickup_address": "龙华店", "destination_address": "深圳市商汤科技有限公司", "created_at": "2025-06-05T06:05:07.481838", "updated_at": "2025-06-05T06:05:07.481857", "price": null, "driver_name": null, "driver_phone": null, "remarks": "[浏览器插件订单] 类型: 代叫\n操作员: 周鑫芸 (店长)\n联系人手机号: 13800013900\n支付方式: 我来支付\n"}, {"id": 39, "edaijia_order_id": "TEST_1748875832", "store_name": "龙华店", "customer_name": "测试客户", "customer_phone": "13800138000", "plate_number": "粤B12345", "car_model": null, "manager_name": "周鑫芸", "status": "cancelled_customer", "pickup_address": "深圳市南山区科技园", "destination_address": "深圳市南山区深圳湾", "created_at": "2025-06-02T14:50:32.736967", "updated_at": "2025-06-04T08:37:32.812156", "price": null, "driver_name": null, "driver_phone": null, "remarks": null}, {"id": 44, "edaijia_order_id": "1748916604798", "store_name": "龙华店", "customer_name": "新测试客户", "customer_phone": "13800138999", "plate_number": "粤B99999", "car_model": "测试车型", "manager_name": "周鑫芸", "status": "cancelled_customer", "pickup_address": "龙华店", "destination_address": "深圳市南山区深圳湾", "created_at": "2025-06-03T02:10:04.430563", "updated_at": "2025-06-04T08:37:32.812205", "price": null, "driver_name": null, "driver_phone": null, "remarks": "[浏览器插件订单] 类型: 代叫\n操作员: 周鑫芸 (店长)\n联系人手机号: 13800013900\n支付方式: 我来支付\n"}]}