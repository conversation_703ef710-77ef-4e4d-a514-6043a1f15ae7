#!/usr/bin/env python3
"""
更新店面信息 - 修改店面名称和统一店长
"""

import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

def update_store_info():
    """更新店面信息"""
    try:
        from app import create_app, db
        from app.models import Order
        
        print("=== 更新店面信息 ===")
        
        # 创建应用上下文
        app = create_app()
        with app.app_context():
            
            # 显示更新前的店面分布
            print("更新前的店面分布:")
            from sqlalchemy import func
            store_stats = db.session.query(
                Order.store_name,
                Order.manager_name,
                func.count(Order.id).label('count')
            ).group_by(Order.store_name, Order.manager_name).all()
            
            for store, manager, count in store_stats:
                print(f"  {store} ({manager}): {count}个订单")
            
            # 更新店面名称和店长信息
            print("\n开始更新店面信息...")
            
            # 1. 福田店 -> 东莞店
            fukuda_orders = Order.query.filter(Order.store_name == '福田店').all()
            if fukuda_orders:
                print(f"将 {len(fukuda_orders)} 个福田店订单更新为东莞店")
                for order in fukuda_orders:
                    order.store_name = '东莞店'
                    order.manager_name = '周鑫芸'
                    order.manager_phone = '13800138001'  # 统一使用周鑫芸的电话
                    # 更新员工ID
                    if order.manager_employee_id and '福田' in order.manager_employee_id:
                        order.manager_employee_id = order.manager_employee_id.replace('福田', '东莞')
                    elif not order.manager_employee_id:
                        order.manager_employee_id = f"EMP东莞001"
            
            # 2. 南山店 -> 万国城店
            nanshan_orders = Order.query.filter(Order.store_name == '南山店').all()
            if nanshan_orders:
                print(f"将 {len(nanshan_orders)} 个南山店订单更新为万国城店")
                for order in nanshan_orders:
                    order.store_name = '万国城店'
                    order.manager_name = '周鑫芸'
                    order.manager_phone = '13800138001'  # 统一使用周鑫芸的电话
                    # 更新员工ID
                    if order.manager_employee_id and '南山' in order.manager_employee_id:
                        order.manager_employee_id = order.manager_employee_id.replace('南山', '万国城')
                    elif not order.manager_employee_id:
                        order.manager_employee_id = f"EMP万国城001"
            
            # 3. 统一所有店面的店长为周鑫芸
            print("统一所有店面的店长为周鑫芸...")
            all_orders = Order.query.filter(Order.manager_name != '周鑫芸').all()
            if all_orders:
                print(f"将 {len(all_orders)} 个订单的店长更新为周鑫芸")
                for order in all_orders:
                    old_manager = order.manager_name
                    order.manager_name = '周鑫芸'
                    order.manager_phone = '13800138001'
                    print(f"  订单 {order.edaijia_order_id}: {old_manager} -> 周鑫芸")
            
            # 4. 更新取车地址（如果地址中包含旧店面名称）
            print("更新取车地址中的店面信息...")
            
            # 更新福田相关地址
            fukuda_address_orders = Order.query.filter(Order.pickup_address.like('%福田%')).all()
            for order in fukuda_address_orders:
                if '福田店' in order.pickup_address:
                    order.pickup_address = order.pickup_address.replace('福田店', '东莞店')
                elif '福田区' in order.pickup_address:
                    order.pickup_address = order.pickup_address.replace('深圳市福田区', '东莞市')
                print(f"  更新订单 {order.edaijia_order_id} 的取车地址")
            
            # 更新南山相关地址
            nanshan_address_orders = Order.query.filter(Order.pickup_address.like('%南山%')).all()
            for order in nanshan_address_orders:
                if '南山店' in order.pickup_address:
                    order.pickup_address = order.pickup_address.replace('南山店', '万国城店')
                elif '南山区' in order.pickup_address:
                    order.pickup_address = order.pickup_address.replace('深圳市南山区', '深圳市万国城')
                print(f"  更新订单 {order.edaijia_order_id} 的取车地址")
            
            # 提交更改
            db.session.commit()
            
            # 显示更新后的店面分布
            print("\n更新后的店面分布:")
            store_stats_after = db.session.query(
                Order.store_name,
                Order.manager_name,
                func.count(Order.id).label('count')
            ).group_by(Order.store_name, Order.manager_name).all()
            
            for store, manager, count in store_stats_after:
                print(f"  {store} ({manager}): {count}个订单")
            
            # 验证更新结果
            total_orders = Order.query.count()
            zhou_orders = Order.query.filter(Order.manager_name == '周鑫芸').count()
            
            print(f"\n✅ 更新完成!")
            print(f"总订单数: {total_orders}")
            print(f"周鑫芸管理的订单: {zhou_orders}")
            print(f"统一率: {zhou_orders/total_orders*100:.1f}%")
            
            # 显示各店面的最新订单示例
            print("\n各店面最新订单示例:")
            stores = ['龙华店', '东莞店', '万国城店']
            for store in stores:
                latest_order = Order.query.filter(Order.store_name == store).order_by(Order.created_at.desc()).first()
                if latest_order:
                    print(f"  {store}: {latest_order.edaijia_order_id} | {latest_order.customer_name} | {latest_order.manager_name}")
                else:
                    print(f"  {store}: 暂无订单")
            
            return True
            
    except Exception as e:
        print(f"❌ 更新店面信息失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_updated_data():
    """测试更新后的数据"""
    try:
        import requests
        
        print("\n=== 测试更新后的数据 ===")
        
        # 测试各店面的图表数据
        stores = ['龙华店', '东莞店', '万国城店']
        
        for store in stores:
            print(f"\n测试 {store} 的数据:")
            
            # 测试图表API
            response = requests.get(f'http://localhost:5000/api/orders/chart-statistics?store_name={store}&time_range=month')
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    chart_data = data.get('data', {})
                    total_orders = chart_data.get('summary', {}).get('total_orders', 0)
                    total_amount = chart_data.get('summary', {}).get('total_amount', 0)
                    print(f"  图表数据: {total_orders}个订单, ¥{total_amount:.2f}")
                else:
                    print(f"  图表API错误: {data.get('error')}")
            else:
                print(f"  图表API请求失败: {response.status_code}")
            
            # 测试活跃订单数量
            response = requests.get(f'http://localhost:5000/api/orders/active-count?store_name={store}')
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    active_count = data.get('count', 0)
                    print(f"  活跃订单: {active_count}个")
            
            # 测试历史订单数量
            response = requests.get(f'http://localhost:5000/api/orders/history-count?store_name={store}')
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    history_count = data.get('count', 0)
                    print(f"  历史订单: {history_count}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始更新店面信息...")
    
    # 更新店面信息
    update_success = update_store_info()
    
    if update_success:
        # 测试更新后的数据
        test_success = test_updated_data()
        
        if test_success:
            print("\n🎉 店面信息更新和测试都成功完成!")
            print("\n现在的店面配置:")
            print("- 龙华店 (周鑫芸)")
            print("- 东莞店 (周鑫芸)")  
            print("- 万国城店 (周鑫芸)")
            print("\n所有店面都由周鑫芸管理，符合F6账号配置")
        else:
            print("\n⚠️ 店面信息更新成功，但测试失败")
    else:
        print("\n❌ 店面信息更新失败")
