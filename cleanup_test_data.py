#!/usr/bin/env python3
"""
清理测试数据 - 删除指定的测试记录
"""

import sqlite3
from datetime import datetime

def cleanup_test_data():
    """清理测试数据"""
    try:
        # 连接数据库
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        print("=== 数据库测试数据清理工具 ===")
        
        # 1. 显示清理前的统计
        cursor.execute("SELECT COUNT(*) FROM 'order'")
        total_before = cursor.fetchone()[0]
        print(f"清理前总订单数: {total_before}")
        
        # 2. 查看要删除的记录
        print("\n📋 即将删除的记录:")
        
        # 查看测试门店A的记录
        cursor.execute("SELECT edaijia_order_id, manager_name, store_name, customer_name FROM 'order' WHERE store_name = '测试门店A'")
        test_store_orders = cursor.fetchall()
        print(f"\n🏪 测试门店A的订单 ({len(test_store_orders)}个):")
        for order_id, manager, store, customer in test_store_orders:
            print(f"  {order_id} | {manager} | {customer or '无客户'}")
        
        # 查看龙华店长的记录
        cursor.execute("SELECT edaijia_order_id, manager_name, store_name, customer_name FROM 'order' WHERE manager_name = '龙华店长'")
        manager_orders = cursor.fetchall()
        print(f"\n👨‍💼 龙华店长的订单 ({len(manager_orders)}个):")
        for order_id, manager, store, customer in manager_orders:
            print(f"  {order_id} | {store} | {customer or '无客户'}")
        
        # 3. 确认删除
        total_to_delete = len(test_store_orders) + len(manager_orders)
        print(f"\n⚠️  总共将删除 {total_to_delete} 个订单记录")
        
        # 4. 执行删除
        print("\n🗑️  开始删除...")
        
        # 删除测试门店A的所有记录
        cursor.execute("DELETE FROM 'order' WHERE store_name = '测试门店A'")
        deleted_store = cursor.rowcount
        print(f"  删除测试门店A的记录: {deleted_store}个")
        
        # 删除龙华店长的所有记录
        cursor.execute("DELETE FROM 'order' WHERE manager_name = '龙华店长'")
        deleted_manager = cursor.rowcount
        print(f"  删除龙华店长的记录: {deleted_manager}个")
        
        # 提交更改
        conn.commit()
        
        # 5. 显示清理后的统计
        cursor.execute("SELECT COUNT(*) FROM 'order'")
        total_after = cursor.fetchone()[0]
        
        print(f"\n✅ 清理完成!")
        print(f"清理前: {total_before}个订单")
        print(f"清理后: {total_after}个订单")
        print(f"删除了: {total_before - total_after}个订单")
        
        # 6. 显示剩余数据
        print("\n📊 剩余数据统计:")
        cursor.execute("SELECT store_name, manager_name, COUNT(*) as count FROM 'order' GROUP BY store_name, manager_name ORDER BY count DESC")
        remaining_stats = cursor.fetchall()
        
        if remaining_stats:
            for store_name, manager_name, count in remaining_stats:
                print(f"  {store_name} ({manager_name}): {count}个订单")
        else:
            print("  数据库中没有剩余订单")
        
        # 7. 显示剩余的最新订单
        cursor.execute("""
            SELECT edaijia_order_id, manager_name, store_name, customer_name, created_at 
            FROM 'order' 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        remaining_orders = cursor.fetchall()
        
        if remaining_orders:
            print("\n📋 剩余的最新5个订单:")
            for order_id, manager, store, customer, created in remaining_orders:
                print(f"  {order_id} | {manager} | {store} | {customer or '无客户'} | {created}")
        else:
            print("\n📋 数据库中没有剩余订单")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")

if __name__ == "__main__":
    cleanup_test_data()
