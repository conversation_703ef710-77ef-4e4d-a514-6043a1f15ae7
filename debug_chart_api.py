#!/usr/bin/env python3
"""
调试图表统计API的门店过滤问题
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

def debug_chart_api():
    """调试图表统计API"""
    try:
        from app import create_app, db
        from app.models import Order
        from sqlalchemy import func, or_
        
        print("=== 调试图表统计API ===")
        
        # 创建应用上下文
        app = create_app()
        with app.app_context():
            
            # 1. 检查数据库中的店面分布
            print("\n1. 数据库中的店面分布:")
            store_stats = db.session.query(
                Order.store_name,
                func.count(Order.id).label('total_count')
            ).group_by(Order.store_name).all()
            
            for store, total in store_stats:
                # 单独查询已支付订单数
                paid = Order.query.filter(Order.store_name == store, Order.payment_status == 'paid').count()
                print(f"  {store}: 总订单{total}个, 已支付{paid}个")
            
            # 2. 测试图表API的查询条件
            print("\n2. 测试图表API查询条件:")
            
            # e代驾终止状态码
            EDJ_TERMINATED_STATUSES = ['304', '403', '404', '506']
            
            # 基础查询条件（模拟API中的条件）
            base_query = Order.query.filter(
                or_(
                    Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES),
                    Order.status.in_(['completed'])
                ),
                Order.payment_status == 'paid',
                Order.payment_time.isnot(None)
            )
            
            # 时间过滤（最近12个月）
            now = datetime.now()
            start_date = now - timedelta(days=365)
            query = base_query.filter(Order.payment_time >= start_date)
            
            print(f"  基础查询条件匹配的订单数: {query.count()}")
            
            # 3. 测试各店面的过滤
            print("\n3. 测试各店面过滤:")
            stores = ['龙华店', '东莞店', '万国城店']
            
            for store in stores:
                store_query = query.filter(Order.store_name == store)
                count = store_query.count()
                print(f"  {store}: {count}个订单")
                
                # 显示具体订单
                orders = store_query.limit(3).all()
                for order in orders:
                    print(f"    - {order.edaijia_order_id}: {order.customer_name}, ¥{order.pay_amount/100 if order.pay_amount else 0}")
            
            # 4. 测试聚合查询
            print("\n4. 测试聚合查询:")
            
            # 龙华店的聚合查询
            store_name = '龙华店'
            store_query = query.filter(Order.store_name == store_name)
            
            # 按月聚合
            group_by_expr = func.strftime('%Y-%m', Order.payment_time)
            results = db.session.query(
                group_by_expr.label('time_period'),
                func.count(Order.id).label('order_count'),
                func.sum(Order.pay_amount / 100.0).label('total_amount')
            ).filter(
                Order.store_name == store_name,
                or_(
                    Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES),
                    Order.status.in_(['completed'])
                ),
                Order.payment_status == 'paid',
                Order.payment_time.isnot(None),
                Order.payment_time >= start_date
            ).group_by(group_by_expr).order_by(group_by_expr).all()
            
            print(f"  {store_name}的聚合结果:")
            total_orders = 0
            total_amount = 0
            for result in results:
                if result.time_period:
                    print(f"    {result.time_period}: {result.order_count}个订单, ¥{result.total_amount:.2f}")
                    total_orders += result.order_count
                    total_amount += result.total_amount or 0
            
            print(f"  {store_name}总计: {total_orders}个订单, ¥{total_amount:.2f}")
            
            # 5. 测试全部门店的聚合查询
            print("\n5. 测试全部门店聚合查询:")
            all_results = db.session.query(
                group_by_expr.label('time_period'),
                func.count(Order.id).label('order_count'),
                func.sum(Order.pay_amount / 100.0).label('total_amount')
            ).filter(
                or_(
                    Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES),
                    Order.status.in_(['completed'])
                ),
                Order.payment_status == 'paid',
                Order.payment_time.isnot(None),
                Order.payment_time >= start_date
            ).group_by(group_by_expr).order_by(group_by_expr).all()
            
            print("  全部门店的聚合结果:")
            all_total_orders = 0
            all_total_amount = 0
            for result in all_results:
                if result.time_period:
                    print(f"    {result.time_period}: {result.order_count}个订单, ¥{result.total_amount:.2f}")
                    all_total_orders += result.order_count
                    all_total_amount += result.total_amount or 0
            
            print(f"  全部门店总计: {all_total_orders}个订单, ¥{all_total_amount:.2f}")
            
            return True
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_chart_api()
