#!/usr/bin/env python3
"""
测试数据库写入权限
"""

import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

def test_database_write():
    """测试数据库写入权限"""
    try:
        from app import create_app, db
        from app.models import APIStatus
        
        print("=== 数据库写入权限测试 ===")
        
        # 创建应用上下文
        app = create_app()
        with app.app_context():
            print(f"数据库URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
            
            # 测试简单查询
            try:
                api_status = APIStatus.query.first()
                print(f"✅ 数据库读取成功: {api_status}")
            except Exception as e:
                print(f"❌ 数据库读取失败: {e}")
                return False
            
            # 测试写入
            try:
                if not api_status:
                    # 创建新记录
                    api_status = APIStatus(
                        service_name='test',
                        status='normal',
                        last_check=datetime.utcnow()
                    )
                    db.session.add(api_status)
                else:
                    # 更新现有记录
                    api_status.last_check = datetime.utcnow()
                    api_status.error_message = f'测试写入 - {datetime.now()}'
                
                db.session.commit()
                print("✅ 数据库写入成功")
                return True
                
            except Exception as e:
                print(f"❌ 数据库写入失败: {e}")
                db.session.rollback()
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_file_permissions():
    """检查文件权限"""
    print("\n=== 文件权限检查 ===")
    
    db_path = 'app.db'
    if os.path.exists(db_path):
        stat = os.stat(db_path)
        print(f"数据库文件: {db_path}")
        print(f"权限: {oct(stat.st_mode)[-3:]}")
        print(f"所有者: {stat.st_uid}")
        print(f"组: {stat.st_gid}")
        print(f"当前用户: {os.getuid()}")
        print(f"当前组: {os.getgid()}")
        
        # 检查读写权限
        readable = os.access(db_path, os.R_OK)
        writable = os.access(db_path, os.W_OK)
        print(f"可读: {readable}")
        print(f"可写: {writable}")
        
        return readable and writable
    else:
        print(f"❌ 数据库文件不存在: {db_path}")
        return False

if __name__ == "__main__":
    print("开始数据库权限测试...")
    
    # 检查文件权限
    file_ok = check_file_permissions()
    
    # 测试数据库写入
    if file_ok:
        db_ok = test_database_write()
        if db_ok:
            print("\n✅ 所有测试通过，数据库可以正常写入")
        else:
            print("\n❌ 数据库写入测试失败")
    else:
        print("\n❌ 文件权限检查失败")
