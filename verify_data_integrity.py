#!/usr/bin/env python3
"""
验证数据库数据完整性
"""

import sqlite3
from datetime import datetime

def verify_data_integrity():
    """验证数据完整性"""
    try:
        # 连接数据库
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        print("=== 数据库完整性验证 ===")
        
        # 1. 基本统计
        cursor.execute("SELECT COUNT(*) FROM 'order'")
        total_orders = cursor.fetchone()[0]
        print(f"📊 总订单数: {total_orders}")
        
        # 2. 检查关键字段的完整性
        checks = [
            ("manager_name", "操作员姓名"),
            ("store_name", "店面名称"),
            ("customer_name", "客户姓名"),
            ("customer_phone", "客户电话"),
            ("pickup_address", "取车地址"),
            ("destination_address", "送车地址"),
            ("created_at", "创建时间")
        ]
        
        print("\n🔍 字段完整性检查:")
        for field, desc in checks:
            cursor.execute(f"SELECT COUNT(*) FROM 'order' WHERE {field} IS NULL OR {field} = ''")
            empty_count = cursor.fetchone()[0]
            
            if empty_count == 0:
                print(f"  ✅ {desc}: 完整 (0个缺失)")
            else:
                print(f"  ⚠️  {desc}: {empty_count}个缺失")
        
        # 3. 显示各店面的订单分布
        print("\n🏪 店面订单分布:")
        cursor.execute("SELECT store_name, manager_name, COUNT(*) as count FROM 'order' GROUP BY store_name, manager_name ORDER BY count DESC")
        store_stats = cursor.fetchall()
        
        for store_name, manager_name, count in store_stats:
            print(f"  {store_name} ({manager_name}): {count}个订单")
        
        # 4. 显示最新的几个订单
        print("\n📋 最新5个订单:")
        cursor.execute("""
            SELECT edaijia_order_id, manager_name, store_name, customer_name, 
                   pickup_address, destination_address, created_at 
            FROM 'order' 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        recent_orders = cursor.fetchall()
        
        for order in recent_orders:
            order_id, manager, store, customer, pickup, dest, created = order
            print(f"  订单: {order_id}")
            print(f"    操作员: {manager} | 店面: {store}")
            print(f"    客户: {customer}")
            print(f"    路线: {pickup} → {dest}")
            print(f"    时间: {created}")
            print()
        
        # 5. 检查状态分布
        print("📈 订单状态分布:")
        cursor.execute("SELECT status, COUNT(*) as count FROM 'order' GROUP BY status ORDER BY count DESC")
        status_stats = cursor.fetchall()
        
        for status, count in status_stats:
            print(f"  {status}: {count}个")
        
        conn.close()
        print("\n✅ 数据完整性验证完成!")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_data_integrity()
