import os
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'instance', 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # F6 API Configuration
    F6_API_BASE_URL = os.environ.get('F6_API_BASE_URL') or 'https://api.f6car.com'
    F6_API_KEY = os.environ.get('F6_API_KEY')
    F6_API_SECRET = os.environ.get('F6_API_SECRET')

    # eDaijia API Configuration
    EDAIJIA_API_BASE_URL = os.environ.get('EDAIJIA_API_BASE_URL') or 'http://openapi.d.edaijia.cn'
    EDAIJIA_API_KEY = os.environ.get('EDAIJIA_API_KEY')
    EDAIJIA_API_SECRET = os.environ.get('EDAIJIA_API_SECRET')

    # eDaijia Callback Configuration
    EDAIJIA_CALLBACK_SECRET = os.environ.get('EDAIJIA_CALLBACK_SECRET') or 'your_callback_secret'
    EDAIJIA_MERCHANT_ID = os.environ.get('EDAIJIA_MERCHANT_ID') or 'your_merchant_id'

    # 百度地图API配置 - 用于地址转经纬度（BD09坐标系，与e代驾兼容）
    BAIDU_MAP_API_KEY = os.environ.get('BAIDU_MAP_API_KEY') or 'iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ'
    BAIDU_MAP_API_URL = 'https://api.map.baidu.com/geocoding/v3/'

    # 统一的店长信息映射配置 - 从.env文件读取
    MANAGER_INFO_MAP = os.environ.get('MANAGER_INFO_MAP') or '{}'

    # Session Configuration
    PERMANENT_SESSION_LIFETIME = 1800  # 30 minutes

    # Pagination
    POSTS_PER_PAGE = int(os.environ.get('POSTS_PER_PAGE') or 10)