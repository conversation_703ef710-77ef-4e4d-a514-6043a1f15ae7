#!/usr/bin/env python3
"""
删除Order表中的status字段，只保留edj_status_code字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import Order
from sqlalchemy import text

def remove_status_field():
    """删除Order表中的status字段"""
    app = create_app()
    
    with app.app_context():
        print("🔧 MyProj2025 数据库结构更新工具")
        print("=" * 50)
        
        try:
            # 检查当前表结构
            result = db.session.execute(text("PRAGMA table_info('order')")).fetchall()
            columns = [row[1] for row in result]
            
            print(f"📋 当前Order表字段: {columns}")
            
            if 'status' not in columns:
                print("✅ status字段已经不存在，无需删除")
                return
            
            # 检查数据一致性
            print("\n📊 检查数据一致性...")
            orders = Order.query.all()
            print(f"   - 总订单数: {len(orders)}")
            
            # 统计各状态的订单数
            status_count = {}
            edj_status_count = {}
            
            for order in orders:
                # 统计内部status
                status = order.status or 'None'
                status_count[status] = status_count.get(status, 0) + 1
                
                # 统计e代驾状态码
                edj_status = order.edj_status_code or 'None'
                edj_status_count[edj_status] = edj_status_count.get(edj_status, 0) + 1
            
            print("   内部status分布:", status_count)
            print("   e代驾状态码分布:", edj_status_count)
            
            # 确认删除
            print("\n⚠️  即将删除Order表的status字段")
            print("   这个操作不可逆，请确认所有订单都有正确的edj_status_code")
            
            confirm = input("是否继续? (y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ 操作已取消")
                return
            
            # 创建新表结构（不包含status字段）
            print("\n🔄 开始删除status字段...")
            
            # SQLite不支持直接删除列，需要重建表
            db.session.execute(text("""
                CREATE TABLE order_new (
                    id INTEGER PRIMARY KEY,
                    edaijia_order_id VARCHAR(120),
                    store_name VARCHAR(100) NOT NULL,
                    customer_id INTEGER,
                    customer_name VARCHAR(64),
                    customer_phone VARCHAR(20),
                    customer_f6_id VARCHAR(64),
                    car_model VARCHAR(64),
                    plate_number VARCHAR(20),
                    manager_name VARCHAR(64),
                    manager_phone VARCHAR(20),
                    manager_employee_id VARCHAR(64),
                    pickup_address VARCHAR(255),
                    destination_address VARCHAR(255),
                    created_at DATETIME,
                    updated_at DATETIME,
                    price FLOAT,
                    driver_name VARCHAR(100),
                    driver_phone VARCHAR(50),
                    remarks TEXT,
                    edj_booking_id VARCHAR(120),
                    order_time BIGINT,
                    timeout INTEGER,
                    edj_status_code VARCHAR(20),
                    edj_status_desc VARCHAR(100),
                    channel VARCHAR(20),
                    from_source VARCHAR(32),
                    dynamic_fee FLOAT,
                    bonus_sn VARCHAR(64),
                    payment_status VARCHAR(20) DEFAULT 'unpaid',
                    pay_channel VARCHAR(20),
                    order_fee FLOAT,
                    pay_amount INTEGER,
                    total_amount INTEGER,
                    third_trade_no VARCHAR(64),
                    payment_time DATETIME,
                    driver_id VARCHAR(32),
                    call_time DATETIME,
                    distance FLOAT,
                    income FLOAT,
                    cancel_reason VARCHAR(200),
                    third_order_id VARCHAR(64),
                    f6_workorder_id VARCHAR(64),
                    f6_workorder_version INTEGER,
                    f6_customer_id VARCHAR(64),
                    f6_car_id VARCHAR(64)
                )
            """))
            
            # 复制数据（排除status字段）
            db.session.execute(text("""
                INSERT INTO order_new SELECT 
                    id, edaijia_order_id, store_name, customer_id, customer_name, 
                    customer_phone, customer_f6_id, car_model, plate_number, 
                    manager_name, manager_phone, manager_employee_id, 
                    pickup_address, destination_address, created_at, updated_at, 
                    price, driver_name, driver_phone, remarks, edj_booking_id, 
                    order_time, timeout, edj_status_code, edj_status_desc, 
                    channel, from_source, dynamic_fee, bonus_sn, payment_status, 
                    pay_channel, order_fee, pay_amount, total_amount, 
                    third_trade_no, payment_time, driver_id, call_time, 
                    distance, income, cancel_reason, third_order_id, 
                    f6_workorder_id, f6_workorder_version, f6_customer_id, f6_car_id
                FROM 'order'
            """))
            
            # 删除旧表，重命名新表
            db.session.execute(text("DROP TABLE 'order'"))
            db.session.execute(text("ALTER TABLE order_new RENAME TO 'order'"))
            
            # 重建索引
            db.session.execute(text("CREATE UNIQUE INDEX ix_order_edaijia_order_id ON 'order' (edaijia_order_id)"))
            db.session.execute(text("CREATE INDEX ix_order_store_name ON 'order' (store_name)"))
            db.session.execute(text("CREATE INDEX ix_order_customer_phone ON 'order' (customer_phone)"))
            db.session.execute(text("CREATE INDEX ix_order_edj_booking_id ON 'order' (edj_booking_id)"))
            
            db.session.commit()
            
            print("✅ status字段删除成功!")
            
            # 验证新表结构
            result = db.session.execute(text("PRAGMA table_info('order')")).fetchall()
            new_columns = [row[1] for row in result]
            print(f"📋 新的Order表字段: {new_columns}")
            
            # 验证数据完整性
            new_count = db.session.execute(text("SELECT COUNT(*) FROM 'order'")).scalar()
            print(f"📊 数据验证: 原{len(orders)}条 -> 新{new_count}条")
            
            if new_count == len(orders):
                print("✅ 数据迁移完成，数据完整性验证通过!")
            else:
                print("❌ 数据迁移异常，请检查!")
                
        except Exception as e:
            print(f"❌ 删除status字段失败: {str(e)}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    remove_status_field()
