#!/usr/bin/env python3
"""
创建完整的测试数据 - 包括活跃订单和已完成支付订单
确保所有重要字段都有值
"""

import os
import sys
from datetime import datetime, timedelta
import random

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

def create_comprehensive_test_data():
    """创建完整的测试数据"""
    try:
        from app import create_app, db
        from app.models import Order
        
        print("=== 创建完整测试数据 ===")
        
        # 创建应用上下文
        app = create_app()
        with app.app_context():
            
            # 检查现有数据
            existing_count = Order.query.count()
            print(f"现有订单数: {existing_count}")

            # 如果已有足够数据，跳过创建
            if existing_count >= 20:
                print("已有足够测试数据，跳过创建")
                return True
            
            # 基础数据配置
            stores = [
                {"name": "龙华店", "address": "深圳市龙华区民治街道", "manager": "周鑫芸", "phone": "13800138001"},
                {"name": "福田店", "address": "深圳市福田区中心区", "manager": "李经理", "phone": "13800138002"},
                {"name": "南山店", "address": "深圳市南山区科技园", "manager": "王店长", "phone": "13800138003"}
            ]
            
            customers = [
                {"name": "张先生", "phone": "13900000001", "car": "宝马X3", "plate": "粤B12345"},
                {"name": "李女士", "phone": "13900000002", "car": "奥迪A4", "plate": "粤B23456"},
                {"name": "王总", "phone": "13900000003", "car": "特斯拉Model3", "plate": "粤B34567"},
                {"name": "陈经理", "phone": "13900000004", "car": "本田CRV", "plate": "粤B45678"},
                {"name": "刘先生", "phone": "13900000005", "car": "丰田凯美瑞", "plate": "粤B56789"},
                {"name": "赵女士", "phone": "13900000006", "car": "大众帕萨特", "plate": "粤B67890"},
                {"name": "孙总", "phone": "13900000007", "car": "奔驰C200", "plate": "粤B78901"},
                {"name": "周先生", "phone": "13900000008", "car": "比亚迪汉", "plate": "粤B89012"}
            ]
            
            drivers = [
                {"name": "张师傅", "phone": "13700000001", "id": "D001"},
                {"name": "李师傅", "phone": "13700000002", "id": "D002"},
                {"name": "王师傅", "phone": "13700000003", "id": "D003"},
                {"name": "赵师傅", "phone": "13700000004", "id": "D004"},
                {"name": "陈师傅", "phone": "13700000005", "id": "D005"}
            ]
            
            destinations = [
                "深圳市福田区中心区CBD",
                "深圳市南山区科技园南区",
                "深圳市宝安区西乡街道",
                "深圳市罗湖区东门商业区",
                "深圳市龙岗区龙城广场",
                "深圳市坪山区坪山中心区",
                "深圳市光明区光明中心",
                "深圳宝安国际机场",
                "深圳北站",
                "深圳火车站",
                "华强北商业区",
                "世界之窗",
                "欢乐谷",
                "深圳湾公园",
                "莲花山公园"
            ]
            
            # 创建已完成支付订单（过去30天）
            print("创建已完成支付订单...")
            completed_orders = []
            
            for i in range(15):  # 创建15个已完成订单
                store = random.choice(stores)
                customer = random.choice(customers)
                driver = random.choice(drivers)
                destination = random.choice(destinations)
                
                # 订单创建时间（过去30天内随机）
                days_ago = random.randint(1, 30)
                hours_ago = random.randint(0, 23)
                minutes_ago = random.randint(0, 59)
                created_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
                
                # 支付时间（创建后5-60分钟）
                payment_delay = timedelta(minutes=random.randint(5, 60))
                payment_time = created_time + payment_delay
                
                # 订单金额
                base_price = random.randint(50, 200)
                pay_amount = base_price * 100  # 转换为分
                
                order = Order(
                    edaijia_order_id=f"EDJ{datetime.now().strftime('%Y%m%d')}{1000 + i}",
                    store_name=store["name"],
                    
                    # 客户信息
                    customer_name=customer["name"],
                    customer_phone=customer["phone"],
                    customer_f6_id=f"F6C{1000 + i}",
                    
                    # 车辆信息
                    car_model=customer["car"],
                    plate_number=customer["plate"],
                    
                    # 操作员信息
                    manager_name=store["manager"],
                    manager_phone=store["phone"],
                    manager_employee_id=f"EMP{store['name'][:2]}{i:03d}",
                    
                    # 订单基本信息
                    status='completed',
                    pickup_address=store["address"],
                    destination_address=destination,
                    created_at=created_time,
                    updated_at=payment_time,
                    price=float(base_price),
                    
                    # 司机信息
                    driver_name=driver["name"],
                    driver_phone=driver["phone"],
                    driver_id=driver["id"],
                    call_time=created_time + timedelta(minutes=random.randint(2, 10)),
                    
                    # e代驾相关
                    edj_booking_id=f"EDJ_BOOK_{created_time.strftime('%Y%m%d%H%M%S')}_{i:03d}",
                    order_time=int(created_time.timestamp()),
                    timeout=300,
                    edj_status_code='304',  # 已完成
                    edj_status_desc='订单已完成',
                    channel='app',
                    from_source='f6_plugin',
                    
                    # 支付信息
                    payment_status='paid',
                    pay_channel='wechat',
                    order_fee=float(base_price),
                    pay_amount=pay_amount,
                    total_amount=pay_amount,
                    third_trade_no=f"WX{payment_time.strftime('%Y%m%d%H%M%S')}{i:03d}",
                    payment_time=payment_time,
                    
                    # 订单详情
                    distance=round(random.uniform(5.0, 25.0), 1),
                    income=float(base_price),
                    remarks=f"测试订单 - {customer['name']}的代驾服务",
                    
                    # F6工单信息
                    f6_workorder_id=f"F6WO{created_time.strftime('%Y%m%d')}{i:04d}",
                    f6_workorder_version=1,
                    f6_customer_id=f"F6C{1000 + i}",
                    f6_car_id=f"F6CAR{1000 + i}"
                )
                
                completed_orders.append(order)
                db.session.add(order)
                print(f"  已完成订单: {order.edaijia_order_id} | {customer['name']} | {store['name']} | ¥{base_price}")
            
            # 创建活跃订单（进行中和待处理）
            print("\n创建活跃订单...")
            active_orders = []
            
            # 待处理订单
            for i in range(5):
                store = random.choice(stores)
                customer = random.choice(customers)
                destination = random.choice(destinations)
                
                # 最近几小时内创建
                hours_ago = random.randint(0, 6)
                minutes_ago = random.randint(0, 59)
                created_time = datetime.now() - timedelta(hours=hours_ago, minutes=minutes_ago)
                
                base_price = random.randint(60, 180)
                
                order = Order(
                    edaijia_order_id=f"EDJ{datetime.now().strftime('%Y%m%d')}{2000 + i}",
                    store_name=store["name"],
                    
                    # 客户信息
                    customer_name=customer["name"],
                    customer_phone=customer["phone"],
                    customer_f6_id=f"F6C{2000 + i}",
                    
                    # 车辆信息
                    car_model=customer["car"],
                    plate_number=customer["plate"],
                    
                    # 操作员信息
                    manager_name=store["manager"],
                    manager_phone=store["phone"],
                    manager_employee_id=f"EMP{store['name'][:2]}{100 + i:03d}",
                    
                    # 订单基本信息
                    status='pending',
                    pickup_address=store["address"],
                    destination_address=destination,
                    created_at=created_time,
                    updated_at=created_time,
                    price=float(base_price),
                    
                    # e代驾相关
                    edj_booking_id=f"EDJ_BOOK_{created_time.strftime('%Y%m%d%H%M%S')}_{2000 + i}",
                    order_time=int(created_time.timestamp()),
                    timeout=300,
                    edj_status_code='100',  # 待处理
                    edj_status_desc='等待司机接单',
                    channel='app',
                    from_source='f6_plugin',
                    
                    # 支付信息（待支付）
                    payment_status='unpaid',
                    order_fee=float(base_price),
                    total_amount=base_price * 100,
                    
                    # 订单详情
                    remarks=f"待处理订单 - {customer['name']}的代驾需求",
                    
                    # F6工单信息
                    f6_workorder_id=f"F6WO{created_time.strftime('%Y%m%d')}{2000 + i:04d}",
                    f6_workorder_version=1,
                    f6_customer_id=f"F6C{2000 + i}",
                    f6_car_id=f"F6CAR{2000 + i}"
                )
                
                active_orders.append(order)
                db.session.add(order)
                print(f"  待处理订单: {order.edaijia_order_id} | {customer['name']} | {store['name']} | ¥{base_price}")
            
            # 进行中订单
            for i in range(3):
                store = random.choice(stores)
                customer = random.choice(customers)
                driver = random.choice(drivers)
                destination = random.choice(destinations)
                
                # 最近几小时内创建
                hours_ago = random.randint(0, 3)
                minutes_ago = random.randint(0, 59)
                created_time = datetime.now() - timedelta(hours=hours_ago, minutes=minutes_ago)
                call_time = created_time + timedelta(minutes=random.randint(2, 15))
                
                base_price = random.randint(70, 150)
                
                order = Order(
                    edaijia_order_id=f"EDJ{datetime.now().strftime('%Y%m%d')}{3000 + i}",
                    store_name=store["name"],
                    
                    # 客户信息
                    customer_name=customer["name"],
                    customer_phone=customer["phone"],
                    customer_f6_id=f"F6C{3000 + i}",
                    
                    # 车辆信息
                    car_model=customer["car"],
                    plate_number=customer["plate"],
                    
                    # 操作员信息
                    manager_name=store["manager"],
                    manager_phone=store["phone"],
                    manager_employee_id=f"EMP{store['name'][:2]}{200 + i:03d}",
                    
                    # 订单基本信息
                    status='in_progress',
                    pickup_address=store["address"],
                    destination_address=destination,
                    created_at=created_time,
                    updated_at=call_time,
                    price=float(base_price),
                    
                    # 司机信息
                    driver_name=driver["name"],
                    driver_phone=driver["phone"],
                    driver_id=driver["id"],
                    call_time=call_time,
                    
                    # e代驾相关
                    edj_booking_id=f"EDJ_BOOK_{created_time.strftime('%Y%m%d%H%M%S')}_{3000 + i}",
                    order_time=int(created_time.timestamp()),
                    timeout=300,
                    edj_status_code='102',  # 进行中
                    edj_status_desc='司机已接单，正在前往',
                    channel='app',
                    from_source='f6_plugin',
                    
                    # 支付信息（预付费）
                    payment_status='paid',
                    pay_channel='alipay',
                    order_fee=float(base_price),
                    pay_amount=base_price * 100,
                    total_amount=base_price * 100,
                    third_trade_no=f"ALI{call_time.strftime('%Y%m%d%H%M%S')}{i:03d}",
                    payment_time=call_time,
                    
                    # 订单详情
                    distance=round(random.uniform(8.0, 20.0), 1),
                    remarks=f"进行中订单 - {driver['name']}正在为{customer['name']}提供服务",
                    
                    # F6工单信息
                    f6_workorder_id=f"F6WO{created_time.strftime('%Y%m%d')}{3000 + i:04d}",
                    f6_workorder_version=2,
                    f6_customer_id=f"F6C{3000 + i}",
                    f6_car_id=f"F6CAR{3000 + i}"
                )
                
                active_orders.append(order)
                db.session.add(order)
                print(f"  进行中订单: {order.edaijia_order_id} | {customer['name']} | {driver['name']} | {store['name']} | ¥{base_price}")
            
            # 提交所有数据
            db.session.commit()
            
            # 统计结果
            total_orders = Order.query.count()
            completed_count = Order.query.filter(Order.status == 'completed').count()
            pending_count = Order.query.filter(Order.status == 'pending').count()
            in_progress_count = Order.query.filter(Order.status == 'in_progress').count()
            paid_count = Order.query.filter(Order.payment_status == 'paid').count()
            
            print(f"\n✅ 测试数据创建完成!")
            print(f"总订单数: {total_orders}")
            print(f"  已完成: {completed_count}")
            print(f"  待处理: {pending_count}")
            print(f"  进行中: {in_progress_count}")
            print(f"  已支付: {paid_count}")
            
            # 按店面统计
            print(f"\n📊 店面分布:")
            for store in stores:
                store_count = Order.query.filter(Order.store_name == store["name"]).count()
                print(f"  {store['name']}: {store_count}个订单")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

if __name__ == "__main__":
    success = create_comprehensive_test_data()
    if success:
        print("\n🎉 完整测试数据创建成功！")
        print("现在可以测试：")
        print("- 柱形图显示（已完成支付订单）")
        print("- 活跃订单列表")
        print("- 订单状态统计")
        print("- 多店面数据")
    else:
        print("\n❌ 测试数据创建失败")
