2025-06-08 12:17:43,921 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 12:17:46,319 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 12:31:50,410 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 12:31:52,892 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 12:32:42,593 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 12:32:42,598 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 12:33:50,619 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 12:33:50,620 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 12:33:59,153 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 12:33:59,154 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 12:38:28,002 INFO: 全部门店 唯一客户统计: 总订单 32, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-08 12:40:29,047 INFO: 全部门店 唯一客户统计: 总订单 32, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-08 12:41:38,139 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 12:41:40,491 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 12:41:48,179 INFO: 全部门店 唯一客户统计: 总订单 32, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-08 12:41:51,110 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4205]
2025-06-08 12:42:36,824 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4109]
2025-06-08 12:42:36,881 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 2, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4205]
2025-06-08 12:42:36,937 INFO: 店面 龙华店 唯一客户统计: 总订单 9, 唯一客户 5 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-08 12:42:36,939 INFO: 仪表盘统计 - 店面: 龙华店, 客户: 5, 订单: 9, 待处理: 3, 进行中: 1, 已完成: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:4699]
2025-06-08 12:42:53,272 INFO: Reconnect requested for edaijia [in /home/<USER>/MyProj2025/app/api/routes.py:284]
2025-06-08 12:42:53,277 INFO: Attempting to fetch new eDaijia token (force_refresh=True). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-08 12:42:53,291 ERROR: Updated API status for edaijia to error: eDaijia API request failed: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7551f41a91c0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:108]
2025-06-08 12:42:53,311 ERROR: eDaijia API request exception: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7551f41a91c0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:250]
2025-06-08 12:42:53,315 ERROR: Reconnect failed for edaijia. API status: error, Error: eDaijia API request failed: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7551f41a91c0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:309]
2025-06-08 12:45:06,120 INFO: 全部门店 唯一客户统计: 总订单 32, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-08 12:45:07,186 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4205]
2025-06-08 12:45:13,333 INFO: Reconnect requested for edaijia [in /home/<USER>/MyProj2025/app/api/routes.py:284]
2025-06-08 12:45:13,337 INFO: Attempting to fetch new eDaijia token (force_refresh=True). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-08 12:45:13,356 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-08 06:45:13.355957+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-08 12:45:13,374 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-08 12:45:13,378 INFO: Reconnect successful for edaijia [in /home/<USER>/MyProj2025/app/api/routes.py:298]
2025-06-08 12:45:14,851 INFO: 全部门店 唯一客户统计: 总订单 32, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-08 12:45:15,271 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4205]
2025-06-08 12:50:20,641 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 12:50:20,646 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 12:50:22,016 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:03:08,043 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 13:03:08,048 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 13:03:10,206 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:03:20,414 INFO: 调用百度Place API: query=shenzhenshi, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4505]
2025-06-08 13:03:21,068 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4543]
2025-06-08 13:03:22,336 INFO: 调用百度Place API: query=shenzhenshid, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4505]
2025-06-08 13:03:22,880 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4543]
2025-06-08 13:03:23,492 INFO: 调用百度Place API: query=shenzhenshidang, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4505]
2025-06-08 13:03:24,033 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4543]
2025-06-08 13:03:33,913 INFO: 开始修改订单预估: EDJ202506082000, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2718]
2025-06-08 13:03:33,917 INFO: 调用e代驾修改后预估API: http://localhost:5001/order/estimate/after/modify [in /home/<USER>/MyProj2025/app/api/routes.py:2763]
2025-06-08 13:03:33,938 ERROR: 修改后预估失败: 订单不存在 [in /home/<USER>/MyProj2025/app/api/routes.py:2802]
2025-06-08 13:03:40,359 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:04:26,027 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 13:04:26,029 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 13:04:27,183 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:04:57,217 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:05:26,119 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 13:05:26,120 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 13:05:27,214 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:05:57,214 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:49:32,114 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 13:49:32,115 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 13:49:33,734 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:50:03,891 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:50:18,174 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 13:50:18,176 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 13:50:20,118 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:50:22,902 INFO: 查询取消费用: EDJ202506082003, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2496]
2025-06-08 13:50:45,071 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 13:50:45,073 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
2025-06-08 13:50:46,744 INFO: 店面 龙华店 活跃订单列表: 4 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4300]
2025-06-08 13:50:48,317 INFO: 查询取消费用: EDJ202506082003, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2496]
2025-06-08 13:50:58,538 INFO: 店面 龙华店 活跃订单数量: 4 [in /home/<USER>/MyProj2025/app/api/routes.py:3925]
2025-06-08 13:50:58,543 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3984]
