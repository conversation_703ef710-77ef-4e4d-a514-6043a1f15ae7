2025-06-07 21:28:54,215 INFO: 🌐 百度地图API调用: she<PERSON>henshidan, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshidan&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:28:54,584 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:28:54,585 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.90577696656521, 'lat': 22.537089017946677}, 'precise': 0, 'confidence': 50, 'comprehension': 57, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:28:54,586 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:28:54,586 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:28:54,587 INFO: ✅ 百度地图API地址置信度良好: shenzhenshidan, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:28:54,588 INFO: ✅ 百度地图API成功解析: shenzhenshidan -> (113.90577696656521, 22.537089017946677) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:28:54,592 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshidan [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:28:54,595 INFO: 地址解析成功: shenzhenshidan -> (113.90577696656521, 22.537089017946677) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:28:54,598 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 21:28:54,609 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 15:28:54.609227+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 21:28:54,627 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 21:28:54,629 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 21:28:54,645 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshidan, 费用: ¥109.3 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 21:28:57,601 INFO: 调用百度Place API: query=shenzhenshidang, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:28:58,127 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:28:58,160 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:28:58,555 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:28:58,557 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:28:58,559 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:28:58,563 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:28:58,564 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:28:58,570 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:28:58,602 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:28:58,611 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:28:58,612 INFO: 🌐 百度地图API调用: shenzhenshidang, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshidang&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:28:58,890 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:28:58,891 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.90577696656521, 'lat': 22.537089017946677}, 'precise': 0, 'confidence': 50, 'comprehension': 35, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:28:58,892 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:28:58,893 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:28:58,894 INFO: ✅ 百度地图API地址置信度良好: shenzhenshidang, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:28:58,895 INFO: ✅ 百度地图API成功解析: shenzhenshidang -> (113.90577696656521, 22.537089017946677) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:28:58,901 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshidang [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:28:58,903 INFO: 地址解析成功: shenzhenshidang -> (113.90577696656521, 22.537089017946677) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:28:58,908 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 21:28:58,923 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 15:28:58.923307+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 21:28:58,939 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 21:28:58,942 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 21:28:58,964 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshidang, 费用: ¥109.3 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 21:28:59,770 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:29:00,118 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:29:00,119 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:29:00,120 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:29:00,121 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:29:00,121 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:29:00,122 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:29:00,126 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:29:00,127 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:29:00,128 INFO: 🌐 百度地图API调用: 深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域F2, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E7%A6%8F%E7%94%B0%E5%8C%BA-%E8%8E%B2%E8%8A%B1%E8%A1%97%E9%81%93%E7%A6%8F%E4%B8%AD%E8%B7%AF184%E5%8F%B7%E6%B7%B1%E5%9C%B3%E5%BD%93%E4%BB%A3%E8%89%BA%E6%9C%AF%E4%B8%8E%E5%9F%8E%E5%B8%82%E8%A7%84%E5%88%92%E9%A6%86%E5%95%86%E4%B8%9A%E5%8C%BA%E5%9F%9FF2&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:29:00,464 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:29:00,465 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06793898734233, 'lat': 22.551644979259102}, 'precise': 1, 'confidence': 75, 'comprehension': 35, 'level': '商务大厦'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:29:00,466 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:29:00,466 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:29:00,467 INFO: ✅ 百度地图API地址置信度良好: 深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域F2, confidence=75, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
