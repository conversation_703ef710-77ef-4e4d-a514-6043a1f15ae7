2025-06-08 21:46:18,387 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.97332891207938, 'lat': 22.57361671404587}, 'precise': 1, 'confidence': 80, 'comprehension': 100, 'level': '门址'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:46:18,390 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:46:18,390 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:46:18,391 INFO: ✅ 百度地图API地址置信度良好: 深圳市-南山区-珠光路5号, confidence=80, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 21:46:18,391 INFO: ✅ 百度地图API成功解析: 深圳市-南山区-珠光路5号 -> (113.97332891207938, 22.57361671404587) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:46:18,395 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市-南山区-珠光路5号 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:46:18,397 INFO: 地址解析成功: 深圳市-南山区-珠光路5号 -> (113.97332891207938, 22.57361671404587) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:46:18,398 INFO: 送车地址地理编码成功: (113.97332891207938, 22.57361671404587) [in /home/<USER>/MyProj2025/app/api/routes.py:2893]
2025-06-08 21:46:18,399 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:575]
2025-06-08 21:46:18,399 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2965]
2025-06-08 21:46:18,399 INFO: 开始重复订单检查: 手机号=17827055998, 车牌=粤BDX0720, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3361]
2025-06-08 21:46:18,401 ERROR: 重复订单检查异常: type object 'Order' has no attribute 'status' [in /home/<USER>/MyProj2025/app/api/routes.py:3605]
Traceback (most recent call last):
  File "/home/<USER>/MyProj2025/app/api/routes.py", line 3371, in check_duplicate_order
    Order.status.in_(ACTIVE_STATUSES)
    ^^^^^^^^^^^^
AttributeError: type object 'Order' has no attribute 'status'
2025-06-08 21:46:54,074 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:49:23,861 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:50:07,823 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:50:32,057 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:50:58,020 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:51:46,858 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:52:13,269 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:52:41,033 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:53:09,735 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:53:38,972 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:54:07,845 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:54:33,966 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:55:02,950 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:55:39,657 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:56:11,113 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:56:41,203 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:57:52,888 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:59:05,413 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:59:09,165 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 22:05:05,658 INFO: 全部门店 唯一客户统计: 总订单 27, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 22:05:06,971 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-08 22:05:11,315 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:05:11,350 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:05:11,361 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:05:11,362 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4054]
2025-06-08 22:05:11,414 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 2, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-08 22:05:11,483 INFO: 店面 龙华店 唯一客户统计: 总订单 7, 唯一客户 5 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 22:05:11,483 INFO: 仪表盘统计 - 店面: 龙华店, 客户: 5, 订单: 7, 待处理: 2, 进行中: 0, 已完成: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:4641]
2025-06-08 22:06:26,332 INFO: 取车地址缺少坐标，尝试地理编码: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2863]
2025-06-08 22:06:26,337 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 22:06:26,646 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 22:06:26,647 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 22:06:26,647 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 22:06:26,648 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 22:06:26,648 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 22:06:26,649 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 22:06:26,652 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 22:06:26,654 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-08 22:06:26,655 INFO: 取车地址地理编码成功: (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2867]
2025-06-08 22:06:26,656 INFO: 送车地址缺少坐标，尝试地理编码: 深圳市南山区深圳湾 [in /home/<USER>/MyProj2025/app/api/routes.py:2879]
2025-06-08 22:06:26,657 INFO: 🌐 百度地图API调用: 深圳市南山区深圳湾, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%8D%97%E5%B1%B1%E5%8C%BA%E6%B7%B1%E5%9C%B3%E6%B9%BE&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 22:06:26,960 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 22:06:26,961 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95469212699223, 'lat': 22.5283998968019}, 'precise': 1, 'confidence': 80, 'comprehension': 100, 'level': '餐饮'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 22:06:26,962 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 22:06:26,962 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 22:06:26,963 INFO: ✅ 百度地图API地址置信度良好: 深圳市南山区深圳湾, confidence=80, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 22:06:26,963 INFO: ✅ 百度地图API成功解析: 深圳市南山区深圳湾 -> (113.95469212699223, 22.5283998968019) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 22:06:26,967 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市南山区深圳湾 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 22:06:26,968 INFO: 地址解析成功: 深圳市南山区深圳湾 -> (113.95469212699223, 22.5283998968019) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-08 22:06:26,969 INFO: 送车地址地理编码成功: (113.95469212699223, 22.5283998968019) [in /home/<USER>/MyProj2025/app/api/routes.py:2883]
2025-06-08 22:06:26,970 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:566]
2025-06-08 22:06:26,970 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2955]
2025-06-08 22:06:26,970 INFO: 开始重复订单检查: 手机号=13800138000, 车牌=粤B12345, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3351]
2025-06-08 22:06:27,002 INFO: 重复订单检查通过: 手机号=13800138000, 车牌=粤B12345 [in /home/<USER>/MyProj2025/app/api/routes.py:3582]
