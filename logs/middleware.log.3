2025-06-08 22:09:30,451 INFO: SECURITY: Manager '周鑫芸' authenticated successfully for store '龙华店' (ID: 1) from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:743]
2025-06-08 22:09:30,453 INFO: Creating order for store 龙华店 by manager 周鑫芸 [in /home/<USER>/MyProj2025/app/api/routes.py:3023]
2025-06-08 22:09:30,457 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-08 22:09:30,467 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-08 16:09:30.467745+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-08 22:09:30,478 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-08 22:09:30,489 ERROR: Error creating plugin order: 'status' is an invalid keyword argument for Order [in /home/<USER>/MyProj2025/app/api/routes.py:3162]
Traceback (most recent call last):
  File "/home/<USER>/MyProj2025/app/api/routes.py", line 3093, in f6_submit_order
    order = Order(
            ^^^^^^
  File "<string>", line 4, in __init__
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/state.py", line 564, in _initialize_instance
    with util.safe_reraise():
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/state.py", line 562, in _initialize_instance
    manager.original_init(*mixed[1:], **kwargs)
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/decl_base.py", line 2139, in _declarative_constructor
    raise TypeError(
TypeError: 'status' is an invalid keyword argument for Order
2025-06-08 22:11:29,687 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 22:12:11,172 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 22:16:38,589 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 22:16:41,068 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 22:17:45,680 INFO: 取车地址缺少坐标，尝试地理编码: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2863]
2025-06-08 22:17:45,685 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 22:17:45,991 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 22:17:45,992 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 22:17:45,992 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 22:17:45,993 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 22:17:45,994 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 22:17:45,994 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 22:17:45,998 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 22:17:46,000 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-08 22:17:46,000 INFO: 取车地址地理编码成功: (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2867]
2025-06-08 22:17:46,001 INFO: 送车地址缺少坐标，尝试地理编码: 深圳市南山区深圳湾 [in /home/<USER>/MyProj2025/app/api/routes.py:2879]
2025-06-08 22:17:46,002 INFO: 🌐 百度地图API调用: 深圳市南山区深圳湾, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%8D%97%E5%B1%B1%E5%8C%BA%E6%B7%B1%E5%9C%B3%E6%B9%BE&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 22:17:46,283 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 22:17:46,284 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95469212699223, 'lat': 22.5283998968019}, 'precise': 1, 'confidence': 80, 'comprehension': 100, 'level': '餐饮'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 22:17:46,284 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 22:17:46,285 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 22:17:46,285 INFO: ✅ 百度地图API地址置信度良好: 深圳市南山区深圳湾, confidence=80, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 22:17:46,286 INFO: ✅ 百度地图API成功解析: 深圳市南山区深圳湾 -> (113.95469212699223, 22.5283998968019) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 22:17:46,291 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市南山区深圳湾 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 22:17:46,293 INFO: 地址解析成功: 深圳市南山区深圳湾 -> (113.95469212699223, 22.5283998968019) [in /home/<USER>/MyProj2025/app/api/routes.py:2801]
2025-06-08 22:17:46,294 INFO: 送车地址地理编码成功: (113.95469212699223, 22.5283998968019) [in /home/<USER>/MyProj2025/app/api/routes.py:2883]
2025-06-08 22:17:46,295 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:566]
2025-06-08 22:17:46,295 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2955]
2025-06-08 22:17:46,296 INFO: 开始重复订单检查: 手机号=13800138001, 车牌=粤B12346, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3351]
2025-06-08 22:17:46,438 INFO: 重复订单检查通过: 手机号=13800138001, 车牌=粤B12346 [in /home/<USER>/MyProj2025/app/api/routes.py:3582]
2025-06-08 22:17:46,439 INFO: Authentication attempt: manager='周鑫芸', ip=127.0.0.1, user_agent=curl/8.5.0 [in /home/<USER>/MyProj2025/app/api/routes.py:695]
2025-06-08 22:17:46,454 INFO: SECURITY: Manager '周鑫芸' authenticated successfully for store '龙华店' (ID: 1) from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:743]
2025-06-08 22:17:46,455 INFO: Creating order for store 龙华店 by manager 周鑫芸 [in /home/<USER>/MyProj2025/app/api/routes.py:3023]
2025-06-08 22:17:46,464 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:191]
2025-06-08 22:17:46,481 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-08 16:17:46.481540+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:88]
2025-06-08 22:17:46,510 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:226]
2025-06-08 22:17:46,567 INFO: F6 order created successfully: 1749392266488 by 周鑫芸 for store 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3144]
2025-06-08 22:19:54,919 INFO: 店面 龙华店 唯一客户统计: 总订单 8, 唯一客户 6 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 22:19:55,812 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:19:55,817 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4054]
2025-06-08 22:19:55,858 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 2, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-08 22:23:14,655 INFO: 原始店面名称参数: 'é¾ååº' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4191]
2025-06-08 22:23:14,657 INFO: 店面名称解码: 'é¾ååº' -> 'é¾ååº' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:23:14,658 INFO: 解码后店面名称: 'é¾ååº' [in /home/<USER>/MyProj2025/app/api/routes.py:4210]
2025-06-08 22:23:14,667 INFO: 店面 é¾ååº 活跃订单列表: 0 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4242]
2025-06-08 22:25:06,505 INFO: 店面 龙华店 唯一客户统计: 总订单 8, 唯一客户 6 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 22:25:07,298 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:25:07,299 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4054]
2025-06-08 22:25:07,308 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 2, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4144]
2025-06-08 22:25:13,181 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:25:13,184 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 22:25:13,234 INFO: 店面 龙华店 活跃订单数量: 3 [in /home/<USER>/MyProj2025/app/api/routes.py:3880]
2025-06-08 22:25:13,242 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3936]
