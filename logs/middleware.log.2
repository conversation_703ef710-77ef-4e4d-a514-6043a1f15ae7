2025-06-07 21:47:27,988 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:47:27,994 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:47:27,996 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:47:27,996 INFO: 取车地址地理编码成功: (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2894]
2025-06-07 21:47:27,997 INFO: 送车地址缺少坐标，尝试地理编码: 深圳市福田区市民中心 [in /home/<USER>/MyProj2025/app/api/routes.py:2906]
2025-06-07 21:47:27,997 INFO: 🌐 百度地图API调用: 深圳市福田区市民中心, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E7%A6%8F%E7%94%B0%E5%8C%BA%E5%B8%82%E6%B0%91%E4%B8%AD%E5%BF%83&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:47:28,284 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:47:28,285 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06899665224941, 'lat': 22.546752414854257}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:47:28,286 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:47:28,287 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:47:28,287 INFO: ✅ 百度地图API地址置信度良好: 深圳市福田区市民中心, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:47:28,288 INFO: ✅ 百度地图API成功解析: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:47:28,293 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市福田区市民中心 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:47:28,295 INFO: 地址解析成功: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:47:28,295 INFO: 送车地址地理编码成功: (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2910]
2025-06-07 21:47:28,296 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:577]
2025-06-07 21:47:28,297 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2982]
2025-06-07 21:47:28,297 INFO: 开始重复订单检查: 手机号=13418749501, 车牌=粤B8M68Q, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3377]
2025-06-07 21:47:28,306 WARNING: 发现车辆活跃订单: 1749303416549 [in /home/<USER>/MyProj2025/app/api/routes.py:3391]
2025-06-07 21:50:08,291 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:50:08,560 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:50:08,561 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:50:08,562 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:50:08,562 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:50:08,563 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:50:08,563 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:50:08,569 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:50:08,571 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:50:08,572 INFO: 🌐 百度地图API调用: 深圳市福田区市民中心, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E7%A6%8F%E7%94%B0%E5%8C%BA%E5%B8%82%E6%B0%91%E4%B8%AD%E5%BF%83&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:50:08,865 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:50:08,869 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06899665224941, 'lat': 22.546752414854257}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:50:08,870 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:50:08,870 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:50:08,871 INFO: ✅ 百度地图API地址置信度良好: 深圳市福田区市民中心, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:50:08,872 INFO: ✅ 百度地图API成功解析: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:50:08,884 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市福田区市民中心 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:50:08,887 INFO: 地址解析成功: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:50:08,891 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 21:50:08,911 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 15:50:08.911598+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 21:50:08,929 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 21:50:08,932 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 21:50:08,948 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市福田区市民中心, 费用: ¥67.8 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 21:50:23,118 INFO: 取车地址缺少坐标，尝试地理编码: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2890]
2025-06-07 21:50:23,119 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:50:23,418 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:50:23,419 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:50:23,419 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:50:23,420 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:50:23,420 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:50:23,421 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:50:23,424 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:50:23,426 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:50:23,427 INFO: 取车地址地理编码成功: (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2894]
2025-06-07 21:50:23,427 INFO: 送车地址缺少坐标，尝试地理编码: 深圳市福田区市民中心 [in /home/<USER>/MyProj2025/app/api/routes.py:2906]
2025-06-07 21:50:23,428 INFO: 🌐 百度地图API调用: 深圳市福田区市民中心, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E7%A6%8F%E7%94%B0%E5%8C%BA%E5%B8%82%E6%B0%91%E4%B8%AD%E5%BF%83&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
