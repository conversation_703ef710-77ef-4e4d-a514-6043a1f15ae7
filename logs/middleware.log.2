2025-06-08 21:38:17,266 INFO: ✅ 百度地图API成功解析: 深圳市-南山区-珠光路5号 -> (113.97332891207938, 22.57361671404587) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:38:17,275 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市-南山区-珠光路5号 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:38:17,277 INFO: 地址解析成功: 深圳市-南山区-珠光路5号 -> (113.97332891207938, 22.57361671404587) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:38:17,282 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:200]
2025-06-08 21:38:17,287 ERROR: Updated API status for edaijia to error: eDaijia API request failed: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115b8238c0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:106]
2025-06-08 21:38:17,300 ERROR: eDaijia API request exception: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115b8238c0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:248]
2025-06-08 21:38:50,199 INFO: 全部门店 唯一客户统计: 总订单 27, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 21:38:50,731 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4157]
2025-06-08 21:38:53,072 INFO: Reconnect requested for edaijia [in /home/<USER>/MyProj2025/app/api/routes.py:282]
2025-06-08 21:38:53,082 INFO: Attempting to fetch new eDaijia token (force_refresh=True). [in /home/<USER>/MyProj2025/app/api/routes.py:200]
2025-06-08 21:38:53,094 ERROR: Updated API status for edaijia to error: eDaijia API request failed: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115b816ba0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:106]
2025-06-08 21:38:53,129 ERROR: eDaijia API request exception: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115b816ba0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:248]
2025-06-08 21:38:53,137 ERROR: Reconnect failed for edaijia. API status: error, Error: eDaijia API request failed: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115b816ba0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:307]
2025-06-08 21:40:10,809 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:40:12,837 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-08 21:40:19,268 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:40:19,274 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:40:19,377 INFO: 店面 龙华店 活跃订单数量: 2 [in /home/<USER>/MyProj2025/app/api/routes.py:3893]
2025-06-08 21:40:19,378 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3949]
2025-06-08 21:40:26,235 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:40:26,535 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:40:26,537 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:40:26,551 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:40:26,551 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:40:26,552 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 21:40:26,553 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:40:26,562 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:40:26,564 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:40:26,565 INFO: 🌐 百度地图API调用: 深圳市-南山区-珠光路5号, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E5%8D%97%E5%B1%B1%E5%8C%BA-%E7%8F%A0%E5%85%89%E8%B7%AF5%E5%8F%B7&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:40:26,887 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:40:26,888 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.97332891207938, 'lat': 22.57361671404587}, 'precise': 1, 'confidence': 80, 'comprehension': 100, 'level': '门址'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:40:26,889 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:40:26,889 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:40:26,890 INFO: ✅ 百度地图API地址置信度良好: 深圳市-南山区-珠光路5号, confidence=80, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 21:40:26,893 INFO: ✅ 百度地图API成功解析: 深圳市-南山区-珠光路5号 -> (113.97332891207938, 22.57361671404587) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:40:26,901 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市-南山区-珠光路5号 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:40:26,904 INFO: 地址解析成功: 深圳市-南山区-珠光路5号 -> (113.97332891207938, 22.57361671404587) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:40:26,916 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:200]
2025-06-08 21:40:26,939 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-08 15:40:26.939433+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:97]
2025-06-08 21:40:26,964 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:235]
2025-06-08 21:40:26,966 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2163]
2025-06-08 21:40:26,983 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市-南山区-珠光路5号, 费用: ¥61.3 [in /home/<USER>/MyProj2025/app/api/routes.py:2236]
2025-06-08 21:40:35,743 INFO: 取车地址缺少坐标，尝试地理编码: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2873]
2025-06-08 21:40:35,746 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:40:36,055 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:40:36,056 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:40:36,057 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:40:36,058 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:40:36,059 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 21:40:36,060 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:40:36,065 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:40:36,068 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:40:36,068 INFO: 取车地址地理编码成功: (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2877]
