2025-06-07 20:34:38,559 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 20:34:38,566 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 20:34:38,568 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 20:34:38,569 INFO: 🌐 百度地图API调用: shenzhenshi, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshi&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 20:34:38,848 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 20:34:38,849 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06455183658751, 'lat': 22.548456637984177}, 'precise': 0, 'confidence': 20, 'comprehension': 57, 'level': '城市'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 20:34:38,850 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 20:34:38,850 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 20:34:38,851 WARNING: ⚠️ 百度地图API地址置信度较低但可接受: shenzhenshi, confidence=20, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:114]
2025-06-07 20:34:38,854 INFO: ✅ 百度地图API成功解析: shenzhenshi -> (114.06455183658751, 22.548456637984177) [confidence=20] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 20:34:38,866 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshi [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 20:34:38,869 INFO: 地址解析成功: shenzhenshi -> (114.06455183658751, 22.548456637984177) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 20:34:38,876 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 20:34:38,925 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 14:34:38.925470+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 20:34:38,946 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 20:34:38,947 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 20:34:38,982 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshi, 费用: ¥65.9 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 20:50:33,130 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 20:52:38,400 INFO: 店面 龙华店 活跃订单数量: 10 [in /home/<USER>/MyProj2025/app/api/routes.py:3911]
2025-06-07 20:52:38,403 INFO: 店面 龙华店 历史订单数量: 14 [in /home/<USER>/MyProj2025/app/api/routes.py:3970]
2025-06-07 20:52:39,883 INFO: 店面 龙华店 活跃订单列表: 10 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4270]
2025-06-07 20:52:56,194 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 20:52:56,431 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 20:52:56,432 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 20:52:56,432 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 20:52:56,433 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 20:52:56,433 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 20:52:56,434 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 20:52:56,437 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 20:52:56,439 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 20:52:56,439 INFO: 🌐 百度地图API调用: shenzhenshi, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshi&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 20:52:56,653 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 20:52:56,654 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06455183658751, 'lat': 22.548456637984177}, 'precise': 0, 'confidence': 20, 'comprehension': 57, 'level': '城市'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 20:52:56,655 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 20:52:56,655 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 20:52:56,656 WARNING: ⚠️ 百度地图API地址置信度较低但可接受: shenzhenshi, confidence=20, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:114]
2025-06-07 20:52:56,656 INFO: ✅ 百度地图API成功解析: shenzhenshi -> (114.06455183658751, 22.548456637984177) [confidence=20] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 20:52:56,659 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshi [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 20:52:56,661 INFO: 地址解析成功: shenzhenshi -> (114.06455183658751, 22.548456637984177) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 20:52:56,665 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 20:52:56,674 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 14:52:56.674321+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 20:52:56,691 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 20:52:56,692 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 20:52:56,701 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshi, 费用: ¥65.9 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 20:53:03,286 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 20:53:03,546 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 20:53:03,548 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 20:53:03,548 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 20:53:03,549 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 20:53:03,550 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 20:53:03,550 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 20:53:03,557 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 20:53:03,561 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 20:53:03,562 INFO: 🌐 百度地图API调用: shenzhen, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhen&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 20:53:03,822 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 20:53:03,822 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.90577696656521, 'lat': 22.537089017946677}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 20:53:03,823 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 20:53:03,823 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
