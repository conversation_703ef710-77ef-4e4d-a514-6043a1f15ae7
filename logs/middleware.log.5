2025-06-07 21:50:23,730 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:50:23,733 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06899665224941, 'lat': 22.546752414854257}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:50:23,733 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:50:23,734 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:50:23,734 INFO: ✅ 百度地图API地址置信度良好: 深圳市福田区市民中心, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:50:23,735 INFO: ✅ 百度地图API成功解析: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:50:23,739 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市福田区市民中心 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:50:23,740 INFO: 地址解析成功: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:50:23,741 INFO: 送车地址地理编码成功: (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2910]
2025-06-07 21:50:23,742 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:577]
2025-06-07 21:50:23,743 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2982]
2025-06-07 21:50:23,743 INFO: 开始重复订单检查: 手机号=13418749501, 车牌=粤B8M68Q, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3377]
2025-06-07 21:50:23,748 WARNING: 发现车辆活跃订单: 1749303416549 [in /home/<USER>/MyProj2025/app/api/routes.py:3391]
2025-06-07 22:04:56,339 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:04:59,421 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:05:10,627 INFO: 全部门店 唯一客户统计: 总订单 52, 唯一客户 52 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-07 22:05:11,303 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 4, 总订单: 20, 总金额: 2468.80 [in /home/<USER>/MyProj2025/app/api/routes.py:4189]
2025-06-07 22:05:14,284 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 4, 总订单: 20, 总金额: 2468.80 [in /home/<USER>/MyProj2025/app/api/routes.py:4189]
2025-06-07 22:05:14,370 INFO: 店面 龙华店 唯一客户统计: 总订单 25, 唯一客户 25 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-07 22:05:14,372 INFO: 仪表盘统计 - 店面: 龙华店, 客户: 25, 订单: 25, 待处理: 11, 进行中: 0, 已完成: 9 [in /home/<USER>/MyProj2025/app/api/routes.py:4683]
2025-06-07 22:05:38,164 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 22:05:38,514 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 22:05:38,518 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 22:05:38,523 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 22:05:38,527 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 22:05:38,529 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 22:05:38,529 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 22:05:38,536 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 22:05:38,539 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 22:05:38,540 INFO: 🌐 百度地图API调用: 深圳市福田区市民中心, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E7%A6%8F%E7%94%B0%E5%8C%BA%E5%B8%82%E6%B0%91%E4%B8%AD%E5%BF%83&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 22:05:38,908 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 22:05:38,909 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06899665224941, 'lat': 22.546752414854257}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 22:05:38,910 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 22:05:38,910 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 22:05:38,911 INFO: ✅ 百度地图API地址置信度良好: 深圳市福田区市民中心, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 22:05:38,912 INFO: ✅ 百度地图API成功解析: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 22:05:38,917 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市福田区市民中心 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 22:05:38,919 INFO: 地址解析成功: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 22:05:38,922 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 22:05:38,940 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 16:05:38.940806+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 22:05:38,948 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 22:05:38,949 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 22:05:38,965 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市福田区市民中心, 费用: ¥67.8 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 22:05:47,636 INFO: 取车地址缺少坐标，尝试地理编码: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2890]
2025-06-07 22:05:47,642 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 22:05:47,993 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 22:05:47,995 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 22:05:47,996 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 22:05:47,997 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 22:05:47,998 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 22:05:47,999 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 22:05:48,005 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 22:05:48,007 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 22:05:48,008 INFO: 取车地址地理编码成功: (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2894]
2025-06-07 22:05:48,009 INFO: 送车地址缺少坐标，尝试地理编码: 深圳市福田区市民中心 [in /home/<USER>/MyProj2025/app/api/routes.py:2906]
2025-06-07 22:05:48,009 INFO: 🌐 百度地图API调用: 深圳市福田区市民中心, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E7%A6%8F%E7%94%B0%E5%8C%BA%E5%B8%82%E6%B0%91%E4%B8%AD%E5%BF%83&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 22:05:48,297 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
