2025-06-07 22:05:48,307 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06899665224941, 'lat': 22.546752414854257}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 22:05:48,328 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 22:05:48,329 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 22:05:48,337 INFO: ✅ 百度地图API地址置信度良好: 深圳市福田区市民中心, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 22:05:48,338 INFO: ✅ 百度地图API成功解析: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 22:05:48,347 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市福田区市民中心 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 22:05:48,350 INFO: 地址解析成功: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 22:05:48,352 INFO: 送车地址地理编码成功: (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2910]
2025-06-07 22:05:48,354 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:577]
2025-06-07 22:05:48,355 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2982]
2025-06-07 22:05:48,356 INFO: 开始重复订单检查: 手机号=13418749501, 车牌=粤B8M68Q, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3377]
2025-06-07 22:05:48,389 WARNING: 发现车辆活跃订单: 1749303416549 [in /home/<USER>/MyProj2025/app/api/routes.py:3391]
2025-06-07 22:11:57,904 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:12:51,816 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:38:22,880 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:40:24,379 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:41:49,726 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:44:00,713 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:44:27,649 ERROR: Exception on / [GET] [in /home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/flask/app.py:828]
Traceback (most recent call last):
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1970, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such table: order

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/flask/app.py", line 1463, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/flask/app.py", line 872, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/flask_cors/extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/flask/app.py", line 870, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/flask/app.py", line 855, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/app/main/routes.py", line 23, in index
    ).all()
      ^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py", line 2673, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2306, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2191, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/orm/context.py", line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1421, in execute
    return meth(
           ^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/sql/elements.py", line 514, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1643, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1849, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1989, in _exec_single_context
    self._handle_dbapi_exception(
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 2356, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1970, in _exec_single_context
    self.dialect.do_execute(
  File "/home/<USER>/MyProj2025/venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: order
[SQL: SELECT DISTINCT "order".store_name AS order_store_name 
FROM "order" 
WHERE "order".store_name IS NOT NULL AND "order".store_name != ?]
[parameters: ('',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-07 22:46:04,987 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:46:05,355 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:46:05,355 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:47:43,384 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:48:22,493 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:48:48,730 INFO: 全部门店 唯一客户统计: 总订单 3, 唯一客户 3 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-07 22:49:41,967 INFO: 全部门店 唯一客户统计: 总订单 3, 唯一客户 3 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-07 22:49:42,657 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 0, 总订单: 0, 总金额: 0.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4189]
2025-06-07 22:49:48,374 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 0, 总订单: 0, 总金额: 0.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4189]
2025-06-07 22:49:48,450 INFO: 店面 龙华店 唯一客户统计: 总订单 3, 唯一客户 3 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-07 22:49:48,451 INFO: 仪表盘统计 - 店面: 龙华店, 客户: 3, 订单: 3, 待处理: 0, 进行中: 0, 已完成: 3 [in /home/<USER>/MyProj2025/app/api/routes.py:4683]
2025-06-07 22:49:59,289 INFO: 店面 龙华店 唯一客户统计: 总订单 3, 唯一客户 3 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-07 22:49:59,643 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 0, 总订单: 0, 总金额: 0.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4189]
2025-06-07 22:50:03,593 INFO: 图表统计 - 店面: 龙华店, 时间范围: day, 数据点: 0, 总订单: 0, 总金额: 0.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4189]
2025-06-07 22:53:19,024 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:53:19,165 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 1, 总订单: 3, 总金额: 195.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4189]
2025-06-07 22:53:35,862 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 1, 总订单: 3, 总金额: 195.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4189]
2025-06-07 22:54:48,868 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
2025-06-07 22:56:29,182 INFO: 图表统计 - 店面: é¾ååº, 时间范围: month, 数据点: 1, 总订单: 3, 总金额: 195.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4189]
2025-06-07 22:57:03,185 INFO: 店面 龙华店 唯一客户统计: 总订单 3, 唯一客户 3 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
