2025-06-07 21:40:27,236 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:40:27,239 INFO: ✅ 百度地图API地址置信度良好: shenzhenshishang, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:40:27,240 INFO: ✅ 百度地图API成功解析: shenzhenshishang -> (113.90577696656521, 22.537089017946677) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:40:27,245 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshishang [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:40:27,248 INFO: 地址解析成功: shenzhenshishang -> (113.90577696656521, 22.537089017946677) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:40:27,253 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 21:40:27,265 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 15:40:27.264941+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 21:40:27,272 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 21:40:27,274 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 21:40:27,288 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshishang, 费用: ¥109.3 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 21:40:28,525 INFO: 调用百度Place API: query=shenzhenshishangtang, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:40:28,929 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:40:29,036 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:40:29,335 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:40:29,339 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:40:29,344 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:40:29,347 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:40:29,350 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:40:29,354 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:40:29,363 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:40:29,367 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:40:29,368 INFO: 🌐 百度地图API调用: shenzhenshishangtang, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshishangtang&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:40:29,634 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:40:29,635 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.90577696656521, 'lat': 22.537089017946677}, 'precise': 0, 'confidence': 50, 'comprehension': 24, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:40:29,636 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:40:29,637 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:40:29,637 INFO: ✅ 百度地图API地址置信度良好: shenzhenshishangtang, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:40:29,638 INFO: ✅ 百度地图API成功解析: shenzhenshishangtang -> (113.90577696656521, 22.537089017946677) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:40:29,642 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshishangtang [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:40:29,645 INFO: 地址解析成功: shenzhenshishangtang -> (113.90577696656521, 22.537089017946677) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:40:29,648 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 21:40:29,662 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 15:40:29.662180+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 21:40:29,680 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 21:40:29,681 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 21:40:29,693 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshishangtang, 费用: ¥109.3 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 21:40:31,449 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:40:31,763 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:40:31,764 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:40:31,765 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:40:31,766 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:40:31,767 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:40:31,768 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:40:31,775 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:40:31,778 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:40:31,780 INFO: 🌐 百度地图API调用: 深圳市-南山区-高新南十道3区11栋11a, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E5%8D%97%E5%B1%B1%E5%8C%BA-%E9%AB%98%E6%96%B0%E5%8D%97%E5%8D%81%E9%81%933%E5%8C%BA11%E6%A0%8B11a&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:40:32,046 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:40:32,047 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95684006529643, 'lat': 22.532014498836844}, 'precise': 0, 'confidence': 80, 'comprehension': 99, 'level': '门址'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:40:32,048 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:40:32,048 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:40:32,049 INFO: ✅ 百度地图API地址置信度良好: 深圳市-南山区-高新南十道3区11栋11a, confidence=80, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:40:32,050 INFO: ✅ 百度地图API成功解析: 深圳市-南山区-高新南十道3区11栋11a -> (113.95684006529643, 22.532014498836844) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:40:32,055 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市-南山区-高新南十道3区11栋11a [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:40:32,057 INFO: 地址解析成功: 深圳市-南山区-高新南十道3区11栋11a -> (113.95684006529643, 22.532014498836844) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:40:32,061 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 21:40:32,072 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 15:40:32.071883+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 21:40:32,080 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 21:40:32,081 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
