2025-06-08 21:30:51,975 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:30:51,987 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4223]
2025-06-08 21:30:51,993 INFO: 店面 龙华店 活跃订单列表: 2 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4255]
2025-06-08 21:31:21,973 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4204]
2025-06-08 21:31:21,976 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:31:21,977 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4223]
2025-06-08 21:31:21,982 INFO: 店面 龙华店 活跃订单列表: 2 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4255]
2025-06-08 21:31:50,185 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:31:50,194 INFO: 店面 龙华店 活跃订单数量: 2 [in /home/<USER>/MyProj2025/app/api/routes.py:3893]
2025-06-08 21:31:50,200 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:31:50,212 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3949]
2025-06-08 21:31:51,972 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4204]
2025-06-08 21:31:51,975 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:31:51,976 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4223]
2025-06-08 21:31:51,980 INFO: 店面 龙华店 活跃订单列表: 2 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4255]
2025-06-08 21:32:21,983 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4204]
2025-06-08 21:32:21,988 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:32:21,989 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4223]
2025-06-08 21:32:21,999 INFO: 店面 龙华店 活跃订单列表: 2 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4255]
2025-06-08 21:32:50,177 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:32:50,181 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:32:50,197 INFO: 店面 龙华店 活跃订单数量: 2 [in /home/<USER>/MyProj2025/app/api/routes.py:3893]
2025-06-08 21:32:50,198 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3949]
2025-06-08 21:32:51,972 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4204]
2025-06-08 21:32:51,977 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:32:51,978 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4223]
2025-06-08 21:32:51,982 INFO: 店面 龙华店 活跃订单列表: 2 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4255]
2025-06-08 21:33:03,288 INFO: 全部门店 唯一客户统计: 总订单 27, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 21:33:04,190 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4157]
2025-06-08 21:33:11,257 INFO: 全部门店 唯一客户统计: 总订单 27, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 21:33:11,587 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4157]
2025-06-08 21:33:12,683 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:33:12,729 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:33:12,733 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:33:12,734 INFO: 图表统计API - 解码后的店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4067]
2025-06-08 21:33:12,757 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 2, 总订单: 5, 总金额: 650.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4157]
2025-06-08 21:33:12,773 INFO: 店面 龙华店 唯一客户统计: 总订单 7, 唯一客户 5 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 21:33:12,776 INFO: 仪表盘统计 - 店面: 龙华店, 客户: 5, 订单: 7, 待处理: 2, 进行中: 0, 已完成: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:4654]
2025-06-08 21:35:51,876 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:35:51,881 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:35:51,890 INFO: 店面 龙华店 活跃订单数量: 2 [in /home/<USER>/MyProj2025/app/api/routes.py:3893]
2025-06-08 21:35:51,892 INFO: 店面 龙华店 历史订单数量: 5 [in /home/<USER>/MyProj2025/app/api/routes.py:3949]
2025-06-08 21:35:53,667 INFO: 原始店面名称参数: '龙华店' (type: <class 'str'>) [in /home/<USER>/MyProj2025/app/api/routes.py:4204]
2025-06-08 21:35:53,670 INFO: 店面名称解码: '龙华店' -> '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:38]
2025-06-08 21:35:53,670 INFO: 解码后店面名称: '龙华店' [in /home/<USER>/MyProj2025/app/api/routes.py:4223]
2025-06-08 21:35:53,679 INFO: 店面 龙华店 活跃订单列表: 2 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4255]
2025-06-08 21:36:13,685 INFO: 查询取消费用: EDJ20250608004, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2479]
2025-06-08 21:36:13,706 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:200]
2025-06-08 21:36:13,720 ERROR: Updated API status for edaijia to error: eDaijia API request failed: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115a6b2ff0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:106]
2025-06-08 21:36:13,734 ERROR: eDaijia API request exception: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115a6b2ff0>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:248]
2025-06-08 21:36:53,349 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4157]
2025-06-08 21:36:53,366 INFO: 全部门店 唯一客户统计: 总订单 27, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 21:36:53,367 INFO: 仪表盘统计 - 店面: 全部门店, 客户: 12, 订单: 27, 待处理: 5, 进行中: 2, 已完成: 20 [in /home/<USER>/MyProj2025/app/api/routes.py:4654]
2025-06-08 21:37:40,275 INFO: 全部门店 唯一客户统计: 总订单 27, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 21:37:40,629 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4157]
2025-06-08 21:37:42,709 INFO: 全部门店 唯一客户统计: 总订单 27, 唯一客户 12 [in /home/<USER>/MyProj2025/app/main/routes.py:508]
2025-06-08 21:37:43,338 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 2, 总订单: 20, 总金额: 2422.00 [in /home/<USER>/MyProj2025/app/api/routes.py:4157]
2025-06-08 21:38:01,381 INFO: 调用百度Place API: query=shenzhenshi, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4460]
2025-06-08 21:38:01,903 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:38:02,086 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4498]
2025-06-08 21:38:02,301 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:38:02,320 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:38:02,326 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:38:02,327 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:38:02,330 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 21:38:02,337 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:38:02,348 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:38:02,353 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
