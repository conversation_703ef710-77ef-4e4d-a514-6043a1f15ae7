2025-06-07 20:56:00,766 INFO: 🌐 百度地图API调用: 深圳市商汤科技有限公司, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%95%86%E6%B1%A4%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 20:56:01,059 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 20:56:01,060 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95263186330578, 'lat': 22.546877167746977}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 20:56:01,061 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 20:56:01,061 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 20:56:01,062 INFO: ✅ 百度地图API地址置信度良好: 深圳市商汤科技有限公司, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 20:56:01,062 INFO: ✅ 百度地图API成功解析: 深圳市商汤科技有限公司 -> (113.95263186330578, 22.546877167746977) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 20:56:01,067 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市商汤科技有限公司 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 20:56:01,069 INFO: 地址解析成功: 深圳市商汤科技有限公司 -> (113.95263186330578, 22.546877167746977) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 20:56:01,073 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 20:56:01,083 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 14:56:01.083710+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 20:56:01,094 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 20:56:01,095 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 20:56:01,106 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市商汤科技有限公司, 费用: ¥83.9 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 20:59:53,687 INFO: 全部门店 唯一客户统计: 总订单 51, 唯一客户 51 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-07 20:59:54,253 INFO: 图表统计 - 店面: 全部门店, 时间范围: month, 数据点: 4, 总订单: 20, 总金额: 2468.80 [in /home/<USER>/MyProj2025/app/api/routes.py:4175]
2025-06-07 21:00:04,707 INFO: 图表统计 - 店面: 龙华店, 时间范围: month, 数据点: 4, 总订单: 20, 总金额: 2468.80 [in /home/<USER>/MyProj2025/app/api/routes.py:4175]
2025-06-07 21:00:04,727 INFO: 店面 龙华店 唯一客户统计: 总订单 24, 唯一客户 24 [in /home/<USER>/MyProj2025/app/main/routes.py:501]
2025-06-07 21:00:04,729 INFO: 仪表盘统计 - 店面: 龙华店, 客户: 24, 订单: 24, 待处理: 10, 进行中: 0, 已完成: 9 [in /home/<USER>/MyProj2025/app/api/routes.py:4669]
2025-06-07 21:04:08,754 INFO: 店面 龙华店 活跃订单数量: 10 [in /home/<USER>/MyProj2025/app/api/routes.py:3911]
2025-06-07 21:04:08,759 INFO: 店面 龙华店 历史订单数量: 14 [in /home/<USER>/MyProj2025/app/api/routes.py:3970]
2025-06-07 21:04:10,062 INFO: 店面 龙华店 历史订单数量: 14 [in /home/<USER>/MyProj2025/app/api/routes.py:3970]
2025-06-07 21:04:10,063 INFO: 店面 龙华店 活跃订单数量: 10 [in /home/<USER>/MyProj2025/app/api/routes.py:3911]
2025-06-07 21:04:29,368 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:04:29,911 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:04:29,912 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:04:29,913 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:04:29,913 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:04:29,913 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:04:29,914 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:04:29,916 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:04:29,918 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:04:29,918 INFO: 🌐 百度地图API调用: 深圳市商汤科技有限公司, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%95%86%E6%B1%A4%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:04:30,297 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:04:30,298 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95263186330578, 'lat': 22.546877167746977}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:04:30,299 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:04:30,299 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:04:30,300 INFO: ✅ 百度地图API地址置信度良好: 深圳市商汤科技有限公司, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:04:30,301 INFO: ✅ 百度地图API成功解析: 深圳市商汤科技有限公司 -> (113.95263186330578, 22.546877167746977) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:04:30,305 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市商汤科技有限公司 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:04:30,307 INFO: 地址解析成功: 深圳市商汤科技有限公司 -> (113.95263186330578, 22.546877167746977) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:04:30,310 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 21:04:30,321 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 15:04:30.321329+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 21:04:30,347 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 21:04:30,348 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 21:04:30,361 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市商汤科技有限公司, 费用: ¥83.9 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 21:04:33,229 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:04:33,615 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:04:33,616 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:04:33,617 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:04:33,617 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:04:33,618 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:04:33,618 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:04:33,624 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:04:33,626 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
