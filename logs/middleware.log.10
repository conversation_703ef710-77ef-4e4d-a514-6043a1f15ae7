2025-06-07 21:37:22,535 INFO: 🌐 百度地图API调用: 深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域F2, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E7%A6%8F%E7%94%B0%E5%8C%BA-%E8%8E%B2%E8%8A%B1%E8%A1%97%E9%81%93%E7%A6%8F%E4%B8%AD%E8%B7%AF184%E5%8F%B7%E6%B7%B1%E5%9C%B3%E5%BD%93%E4%BB%A3%E8%89%BA%E6%9C%AF%E4%B8%8E%E5%9F%8E%E5%B8%82%E8%A7%84%E5%88%92%E9%A6%86%E5%95%86%E4%B8%9A%E5%8C%BA%E5%9F%9FF2&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:37:22,740 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:37:22,741 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06793898734233, 'lat': 22.551644979259102}, 'precise': 1, 'confidence': 75, 'comprehension': 35, 'level': '商务大厦'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:37:22,742 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:37:22,743 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:37:22,743 INFO: ✅ 百度地图API地址置信度良好: 深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域F2, confidence=75, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:37:22,744 INFO: ✅ 百度地图API成功解析: 深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域F2 -> (114.06793898734233, 22.551644979259102) [confidence=75] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:37:22,750 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域F2 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:37:22,752 INFO: 地址解析成功: 深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域F2 -> (114.06793898734233, 22.551644979259102) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:37:22,754 INFO: 送车地址地理编码成功: (114.06793898734233, 22.551644979259102) [in /home/<USER>/MyProj2025/app/api/routes.py:2910]
2025-06-07 21:37:22,755 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:577]
2025-06-07 21:37:22,755 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2982]
2025-06-07 21:37:22,757 INFO: 开始重复订单检查: 手机号=13418749501, 车牌=粤B8M68Q, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3377]
2025-06-07 21:37:22,763 WARNING: 发现车辆活跃订单: 1749303416549 [in /home/<USER>/MyProj2025/app/api/routes.py:3391]
2025-06-07 21:40:21,717 INFO: 调用百度Place API: query=shenzhenshi, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:40:22,227 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:40:22,230 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:40:22,550 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:40:22,551 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:40:22,552 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:40:22,552 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:40:22,554 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:40:22,555 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:40:22,560 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:40:22,561 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:40:22,562 INFO: 🌐 百度地图API调用: shenzhenshi, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshi&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:40:22,794 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:40:22,795 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06455183658751, 'lat': 22.548456637984177}, 'precise': 0, 'confidence': 20, 'comprehension': 57, 'level': '城市'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:40:22,795 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:40:22,796 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:40:22,796 WARNING: ⚠️ 百度地图API地址置信度较低但可接受: shenzhenshi, confidence=20, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:114]
2025-06-07 21:40:22,797 INFO: ✅ 百度地图API成功解析: shenzhenshi -> (114.06455183658751, 22.548456637984177) [confidence=20] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:40:22,803 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshi [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:40:22,806 INFO: 地址解析成功: shenzhenshi -> (114.06455183658751, 22.548456637984177) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:40:22,810 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 21:40:22,822 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 15:40:22.822657+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 21:40:22,832 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 21:40:22,833 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 21:40:22,845 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshi, 费用: ¥65.9 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 21:40:26,004 INFO: 调用百度Place API: query=shenzhenshishang, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:40:26,510 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:40:26,525 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:40:26,926 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:40:26,927 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:40:26,928 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:40:26,928 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:40:26,929 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:40:26,929 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:40:26,933 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:40:26,935 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:40:26,935 INFO: 🌐 百度地图API调用: shenzhenshishang, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshishang&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:40:27,233 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:40:27,234 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.90577696656521, 'lat': 22.537089017946677}, 'precise': 0, 'confidence': 50, 'comprehension': 57, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:40:27,235 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
