2025-06-08 17:03:21,594 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 17:03:21,601 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-08 17:03:21,606 INFO: 🌐 百度地图API调用: shenzhenshi, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshi&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 17:03:21,950 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 17:03:21,951 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06455183658751, 'lat': 22.548456637984177}, 'precise': 0, 'confidence': 20, 'comprehension': 57, 'level': '城市'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 17:03:21,951 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 17:03:21,952 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 17:03:21,953 WARNING: ⚠️ 百度地图API地址置信度较低但可接受: shenzhenshi, confidence=20, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:114]
2025-06-08 17:03:21,954 INFO: ✅ 百度地图API成功解析: shenzhenshi -> (114.06455183658751, 22.548456637984177) [confidence=20] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 17:03:21,959 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshi [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 17:03:21,961 INFO: 地址解析成功: shenzhenshi -> (114.06455183658751, 22.548456637984177) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-08 17:03:21,965 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-08 17:03:21,976 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-08 11:03:21.976760+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-08 17:03:21,997 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-08 17:03:21,998 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-08 17:03:22,019 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshi, 费用: ¥70.6 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-08 17:03:22,932 INFO: 调用百度Place API: query=shenzhenshida, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4505]
2025-06-08 17:03:23,412 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4543]
2025-06-08 17:03:24,341 INFO: 调用百度Place API: query=shenzhenshidashang, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4505]
2025-06-08 17:03:24,778 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4543]
2025-06-08 17:03:24,851 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 17:03:25,131 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 17:03:25,131 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 17:03:25,132 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 17:03:25,133 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 17:03:25,133 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 17:03:25,134 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 17:03:25,139 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 17:03:25,141 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-08 17:03:25,143 INFO: 🌐 百度地图API调用: shenzhenshidashang, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshidashang&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 17:03:25,388 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 17:03:25,388 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.90577696656521, 'lat': 22.537089017946677}, 'precise': 0, 'confidence': 50, 'comprehension': 35, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 17:03:25,389 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 17:03:25,390 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 17:03:25,391 INFO: ✅ 百度地图API地址置信度良好: shenzhenshidashang, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 17:03:25,391 INFO: ✅ 百度地图API成功解析: shenzhenshidashang -> (113.90577696656521, 22.537089017946677) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 17:03:25,396 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshidashang [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 17:03:25,398 INFO: 地址解析成功: shenzhenshidashang -> (113.90577696656521, 22.537089017946677) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-08 17:03:25,402 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-08 17:03:25,413 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-08 11:03:25.413458+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-08 17:03:25,441 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-08 17:03:25,442 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-08 17:03:25,460 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> shenzhenshidashang, 费用: ¥109.3 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-08 17:03:35,919 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 17:03:36,202 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 17:03:36,203 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 17:03:36,204 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 17:03:36,205 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 17:03:36,205 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 17:03:36,206 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 17:03:36,213 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 17:03:36,216 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-08 17:03:36,217 INFO: 🌐 百度地图API调用: 深圳市-龙岗区-坂田街道永香路荣昌楼1101, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E9%BE%99%E5%B2%97%E5%8C%BA-%E5%9D%82%E7%94%B0%E8%A1%97%E9%81%93%E6%B0%B8%E9%A6%99%E8%B7%AF%E8%8D%A3%E6%98%8C%E6%A5%BC1101&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 17:03:36,512 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 17:03:36,513 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06970307970222, 'lat': 22.627507390092216}, 'precise': 0, 'confidence': 50, 'comprehension': 57, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 17:03:36,514 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 17:03:36,514 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
