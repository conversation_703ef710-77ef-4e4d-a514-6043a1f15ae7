2025-06-07 22:05:48,307 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06899665224941, 'lat': 22.546752414854257}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 22:05:48,328 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 22:05:48,329 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 22:05:48,337 INFO: ✅ 百度地图API地址置信度良好: 深圳市福田区市民中心, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 22:05:48,338 INFO: ✅ 百度地图API成功解析: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 22:05:48,347 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市福田区市民中心 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 22:05:48,350 INFO: 地址解析成功: 深圳市福田区市民中心 -> (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 22:05:48,352 INFO: 送车地址地理编码成功: (114.06899665224941, 22.546752414854257) [in /home/<USER>/MyProj2025/app/api/routes.py:2910]
2025-06-07 22:05:48,354 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:577]
2025-06-07 22:05:48,355 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2982]
2025-06-07 22:05:48,356 INFO: 开始重复订单检查: 手机号=13418749501, 车牌=粤B8M68Q, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3377]
2025-06-07 22:05:48,389 WARNING: 发现车辆活跃订单: 1749303416549 [in /home/<USER>/MyProj2025/app/api/routes.py:3391]
