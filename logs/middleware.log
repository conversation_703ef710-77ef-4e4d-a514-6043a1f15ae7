2025-06-07 21:04:33,626 INFO: 🌐 百度地图API调用: 深圳市商汤, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%95%86%E6%B1%A4&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-07 21:04:33,845 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-07 21:04:33,846 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95703203318385, 'lat': 22.535763615753062}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-07 21:04:33,847 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-07 21:04:33,847 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-07 21:04:33,847 INFO: ✅ 百度地图API地址置信度良好: 深圳市商汤, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-07 21:04:33,848 INFO: ✅ 百度地图API成功解析: 深圳市商汤 -> (113.95703203318385, 22.535763615753062) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-07 21:04:33,851 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市商汤 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-07 21:04:33,852 INFO: 地址解析成功: 深圳市商汤 -> (113.95703203318385, 22.535763615753062) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-07 21:04:33,856 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-07 21:04:33,865 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-07 15:04:33.865165+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-07 21:04:33,880 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-07 21:04:33,882 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-07 21:04:33,893 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市商汤, 费用: ¥88.5 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-07 21:04:41,866 INFO: 店面 龙华店 活跃订单数量: 10 [in /home/<USER>/MyProj2025/app/api/routes.py:3911]
2025-06-07 21:04:41,867 INFO: 店面 龙华店 历史订单数量: 14 [in /home/<USER>/MyProj2025/app/api/routes.py:3970]
2025-06-07 21:04:46,784 INFO: 店面 龙华店 活跃订单数量: 10 [in /home/<USER>/MyProj2025/app/api/routes.py:3911]
2025-06-07 21:04:46,785 INFO: 店面 龙华店 历史订单数量: 14 [in /home/<USER>/MyProj2025/app/api/routes.py:3970]
2025-06-07 21:05:42,815 INFO: 调用百度Place API: query=shenzhensh, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:05:43,415 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:05:43,569 INFO: 调用百度Place API: query=shenzhenshi, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:05:44,130 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:05:46,314 INFO: 调用百度Place API: query=shenzhenshida, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:05:46,886 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:06:08,535 INFO: 调用百度Place API: query=深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:06:09,117 INFO: 调用百度Place API: query=深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域F, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:06:09,138 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:06:09,734 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:06:10,076 INFO: 调用百度Place API: query=深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区域F2, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:06:10,565 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:06:37,570 INFO: 调用百度Place API: query=深圳市-福田区-莲花街道福中路184号深圳当代艺术与城市规划馆商业区, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:06:38,216 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:17:55,226 INFO: 店面 龙华店 活跃订单数量: 10 [in /home/<USER>/MyProj2025/app/api/routes.py:3911]
2025-06-07 21:17:55,228 INFO: 店面 龙华店 历史订单数量: 14 [in /home/<USER>/MyProj2025/app/api/routes.py:3970]
2025-06-07 21:18:17,321 INFO: 店面 龙华店 历史订单数量: 14 [in /home/<USER>/MyProj2025/app/api/routes.py:3970]
2025-06-07 21:18:17,325 INFO: 店面 龙华店 活跃订单数量: 10 [in /home/<USER>/MyProj2025/app/api/routes.py:3911]
2025-06-07 21:18:38,298 INFO: 调用百度Place API: query=shenzhenshi, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:18:38,964 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:18:41,197 INFO: 调用百度Place API: query=shenzhenshida, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:18:41,841 INFO: 调用百度Place API: query=shenzhenshidan, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4475]
2025-06-07 21:18:41,906 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
2025-06-07 21:18:42,519 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4513]
