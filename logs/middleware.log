2025-06-08 21:40:36,069 INFO: 送车地址缺少坐标，尝试地理编码: 深圳市-南山区-珠光路5号 [in /home/<USER>/MyProj2025/app/api/routes.py:2889]
2025-06-08 21:40:36,072 INFO: 🌐 百度地图API调用: 深圳市-南山区-珠光路5号, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E5%8D%97%E5%B1%B1%E5%8C%BA-%E7%8F%A0%E5%85%89%E8%B7%AF5%E5%8F%B7&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:40:36,300 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:40:36,301 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.97332891207938, 'lat': 22.57361671404587}, 'precise': 1, 'confidence': 80, 'comprehension': 100, 'level': '门址'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:40:36,302 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:40:36,303 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:40:36,304 INFO: ✅ 百度地图API地址置信度良好: 深圳市-南山区-珠光路5号, confidence=80, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 21:40:36,304 INFO: ✅ 百度地图API成功解析: 深圳市-南山区-珠光路5号 -> (113.97332891207938, 22.57361671404587) [confidence=80] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:40:36,310 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市-南山区-珠光路5号 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:40:36,313 INFO: 地址解析成功: 深圳市-南山区-珠光路5号 -> (113.97332891207938, 22.57361671404587) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:40:36,314 INFO: 送车地址地理编码成功: (113.97332891207938, 22.57361671404587) [in /home/<USER>/MyProj2025/app/api/routes.py:2893]
2025-06-08 21:40:36,315 INFO: Store name exact match: '龙华店' from IP 127.0.0.1 [in /home/<USER>/MyProj2025/app/api/routes.py:575]
2025-06-08 21:40:36,315 INFO: 店面认证成功: 店长周鑫芸 -> 店面龙华店 (匹配方式: 完全匹配) [in /home/<USER>/MyProj2025/app/api/routes.py:2965]
2025-06-08 21:40:36,316 INFO: 开始重复订单检查: 手机号=17827055998, 车牌=粤BDX0720, 店面=龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:3361]
2025-06-08 21:40:36,317 ERROR: 重复订单检查异常: type object 'Order' has no attribute 'status' [in /home/<USER>/MyProj2025/app/api/routes.py:3605]
Traceback (most recent call last):
  File "/home/<USER>/MyProj2025/app/api/routes.py", line 3371, in check_duplicate_order
    Order.status.in_(ACTIVE_STATUSES)
    ^^^^^^^^^^^^
AttributeError: type object 'Order' has no attribute 'status'
2025-06-08 21:43:57,302 INFO: Middleware startup [in /home/<USER>/MyProj2025/app/__init__.py:88]
