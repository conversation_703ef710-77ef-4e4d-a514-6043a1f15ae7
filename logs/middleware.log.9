2025-06-06 22:08:02,580 INFO: 调用百度Place API: query=shenzhenshida, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4367]
2025-06-06 22:08:03,090 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4405]
2025-06-06 22:08:04,955 INFO: 开始修改订单预估: 1749105543574, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2718]
2025-06-06 22:08:04,962 INFO: 调用e代驾修改后预估API: http://localhost:5001/order/estimate/after/modify [in /home/<USER>/MyProj2025/app/api/routes.py:2763]
2025-06-06 22:08:04,983 ERROR: 修改后预估失败: 订单不存在 [in /home/<USER>/MyProj2025/app/api/routes.py:2802]
2025-06-06 22:08:22,925 INFO: 店面 龙华店 活跃订单列表: 12 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4162]
2025-06-06 22:08:51,602 INFO: 店面 龙华店 活跃订单数量: 12 [in /home/<USER>/MyProj2025/app/api/routes.py:3803]
2025-06-06 22:08:51,604 INFO: 店面 龙华店 历史订单数量: 16 [in /home/<USER>/MyProj2025/app/api/routes.py:3862]
2025-06-06 22:08:52,922 INFO: 店面 龙华店 活跃订单列表: 12 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4162]
2025-06-06 22:09:22,926 INFO: 店面 龙华店 活跃订单列表: 12 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4162]
2025-06-06 22:09:51,687 INFO: 店面 龙华店 活跃订单数量: 12 [in /home/<USER>/MyProj2025/app/api/routes.py:3803]
2025-06-06 22:09:51,689 INFO: 店面 龙华店 历史订单数量: 16 [in /home/<USER>/MyProj2025/app/api/routes.py:3862]
2025-06-06 22:09:52,922 INFO: 店面 龙华店 活跃订单列表: 12 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4162]
2025-06-06 22:10:27,740 INFO: 店面 龙华店 历史订单数量: 16 [in /home/<USER>/MyProj2025/app/api/routes.py:3862]
2025-06-06 22:10:27,741 INFO: 店面 龙华店 活跃订单数量: 12 [in /home/<USER>/MyProj2025/app/api/routes.py:3803]
2025-06-06 22:10:29,311 INFO: 店面 龙华店 活跃订单列表: 12 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4162]
2025-06-06 22:12:38,040 INFO: 店面 龙华店 活跃订单数量: 12 [in /home/<USER>/MyProj2025/app/api/routes.py:3803]
2025-06-06 22:12:38,043 INFO: 店面 龙华店 历史订单数量: 16 [in /home/<USER>/MyProj2025/app/api/routes.py:3862]
2025-06-06 22:12:39,360 INFO: 店面 龙华店 活跃订单列表: 12 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4162]
2025-06-06 22:12:49,520 INFO: 调用百度Place API: query=shenzhenshi, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4367]
2025-06-06 22:12:49,951 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4405]
2025-06-06 22:12:50,417 INFO: 调用百度Place API: query=shenzhenshida, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4367]
2025-06-06 22:12:50,945 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4405]
2025-06-06 22:12:53,086 INFO: 开始修改订单预估: 1749105543574, 操作人: 龙华店 [in /home/<USER>/MyProj2025/app/api/routes.py:2718]
2025-06-06 22:12:53,091 INFO: 调用e代驾修改后预估API: http://localhost:5001/order/estimate/after/modify [in /home/<USER>/MyProj2025/app/api/routes.py:2763]
2025-06-06 22:12:53,106 ERROR: 修改后预估失败: 订单不存在 [in /home/<USER>/MyProj2025/app/api/routes.py:2802]
2025-06-06 22:13:41,350 INFO: 店面 龙华店 活跃订单数量: 12 [in /home/<USER>/MyProj2025/app/api/routes.py:3803]
2025-06-06 22:13:41,350 INFO: 店面 龙华店 历史订单数量: 16 [in /home/<USER>/MyProj2025/app/api/routes.py:3862]
2025-06-06 22:13:44,215 INFO: 店面 龙华店 活跃订单列表: 12 个 [in /home/<USER>/MyProj2025/app/api/routes.py:4162]
2025-06-06 22:14:04,545 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-06 22:14:04,808 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-06 22:14:04,809 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-06 22:14:04,810 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-06 22:14:04,811 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-06 22:14:04,812 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-06 22:14:04,812 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-06 22:14:04,820 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-06 22:14:04,822 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-06 22:14:04,823 INFO: 🌐 百度地图API调用: 深圳市商汤科技有限公司, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%95%86%E6%B1%A4%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-06 22:14:05,142 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-06 22:14:05,142 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95263186330578, 'lat': 22.546877167746977}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-06 22:14:05,143 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-06 22:14:05,143 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-06 22:14:05,144 INFO: ✅ 百度地图API地址置信度良好: 深圳市商汤科技有限公司, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-06 22:14:05,144 INFO: ✅ 百度地图API成功解析: 深圳市商汤科技有限公司 -> (113.95263186330578, 22.546877167746977) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-06 22:14:05,149 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市商汤科技有限公司 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-06 22:14:05,151 INFO: 地址解析成功: 深圳市商汤科技有限公司 -> (113.95263186330578, 22.546877167746977) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-06 22:14:05,155 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:202]
2025-06-06 22:14:05,165 INFO: Updated API status for edaijia to normal. Token expires at 2025-06-06 16:14:05.165383+00:00 [in /home/<USER>/MyProj2025/app/api/routes.py:99]
2025-06-06 22:14:05,182 INFO: Successfully fetched new eDaijia token, expires in 7200 seconds. [in /home/<USER>/MyProj2025/app/api/routes.py:237]
2025-06-06 22:14:05,182 INFO: 调用e代驾预估费用API: http://localhost:5001/order/costestimateV2 [in /home/<USER>/MyProj2025/app/api/routes.py:2180]
2025-06-06 22:14:05,191 INFO: 预估费用查询成功: 龙华店 - 龙华店 -> 深圳市商汤科技有限公司, 费用: ¥83.9 [in /home/<USER>/MyProj2025/app/api/routes.py:2253]
2025-06-06 22:14:13,163 INFO: 取车地址缺少坐标，尝试地理编码: 深圳市商汤科技有限公司 [in /home/<USER>/MyProj2025/app/api/routes.py:2890]
2025-06-06 22:14:13,164 INFO: 🌐 百度地图API调用: 深圳市商汤科技有限公司, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82%E5%95%86%E6%B1%A4%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-06 22:14:13,494 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-06 22:14:13,495 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.95263186330578, 'lat': 22.546877167746977}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-06 22:14:13,496 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-06 22:14:13,496 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-06 22:14:13,497 INFO: ✅ 百度地图API地址置信度良好: 深圳市商汤科技有限公司, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-06 22:14:13,497 INFO: ✅ 百度地图API成功解析: 深圳市商汤科技有限公司 -> (113.95263186330578, 22.546877167746977) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-06 22:14:13,501 INFO: 地址解析成功 - 提供商: baidu, 地址: 深圳市商汤科技有限公司 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-06 22:14:13,502 INFO: 地址解析成功: 深圳市商汤科技有限公司 -> (113.95263186330578, 22.546877167746977) [in /home/<USER>/MyProj2025/app/api/routes.py:2828]
2025-06-06 22:14:13,503 INFO: 取车地址地理编码成功: (113.95263186330578, 22.546877167746977) [in /home/<USER>/MyProj2025/app/api/routes.py:2894]
