2025-06-08 21:38:02,354 INFO: 🌐 百度地图API调用: she<PERSON><PERSON><PERSON>, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshi&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:38:02,600 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:38:02,604 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.06455183658751, 'lat': 22.548456637984177}, 'precise': 0, 'confidence': 20, 'comprehension': 57, 'level': '城市'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:38:02,605 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:38:02,605 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:38:02,606 WARNING: ⚠️ 百度地图API地址置信度较低但可接受: shenzhenshi, confidence=20, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:114]
2025-06-08 21:38:02,606 INFO: ✅ 百度地图API成功解析: shenzhenshi -> (114.06455183658751, 22.548456637984177) [confidence=20] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:38:02,610 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshi [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:38:02,611 INFO: 地址解析成功: shenzhenshi -> (114.06455183658751, 22.548456637984177) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:38:02,615 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:200]
2025-06-08 21:38:02,620 ERROR: Updated API status for edaijia to error: eDaijia API request failed: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115b823c20>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:106]
2025-06-08 21:38:02,635 ERROR: eDaijia API request exception: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115b823c20>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:248]
2025-06-08 21:38:05,229 INFO: 调用百度Place API: query=shenzhenshidangdai, city=深圳市 [in /home/<USER>/MyProj2025/app/api/routes.py:4460]
2025-06-08 21:38:05,742 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:38:05,755 INFO: 百度Place API返回 10 个建议 [in /home/<USER>/MyProj2025/app/api/routes.py:4498]
2025-06-08 21:38:06,150 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:38:06,152 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:38:06,153 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:38:06,153 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:38:06,154 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 21:38:06,155 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:38:06,162 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:38:06,165 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:38:06,166 INFO: 🌐 百度地图API调用: shenzhenshidangdai, URL: https://api.map.baidu.com/geocoding/v3/?address=shenzhenshidangdai&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:38:06,472 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:38:06,474 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.90577696656521, 'lat': 22.537089017946677}, 'precise': 0, 'confidence': 50, 'comprehension': 18, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:38:06,475 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:38:06,476 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:38:06,477 INFO: ✅ 百度地图API地址置信度良好: shenzhenshidangdai, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 21:38:06,477 INFO: ✅ 百度地图API成功解析: shenzhenshidangdai -> (113.90577696656521, 22.537089017946677) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:38:06,484 INFO: 地址解析成功 - 提供商: baidu, 地址: shenzhenshidangdai [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:38:06,486 INFO: 地址解析成功: shenzhenshidangdai -> (113.90577696656521, 22.537089017946677) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:38:06,491 INFO: Attempting to fetch new eDaijia token (force_refresh=False). [in /home/<USER>/MyProj2025/app/api/routes.py:200]
2025-06-08 21:38:06,499 ERROR: Updated API status for edaijia to error: eDaijia API request failed: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115861e180>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:106]
2025-06-08 21:38:06,525 ERROR: eDaijia API request exception: HTTPConnectionPool(host='localhost', port=5001): Max retries exceeded with url: /openApi/accessToken (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70115861e180>: Failed to establish a new connection: [Errno 111] Connection refused')) [in /home/<USER>/MyProj2025/app/api/routes.py:248]
2025-06-08 21:38:16,687 INFO: 🌐 百度地图API调用: 龙华店, URL: https://api.map.baidu.com/geocoding/v3/?address=%E9%BE%99%E5%8D%8E%E5%BA%97&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:38:16,999 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:38:17,000 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 114.03845498356466, 'lat': 22.647407533355356}, 'precise': 0, 'confidence': 50, 'comprehension': 100, 'level': 'NoClass'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:38:17,001 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:38:17,001 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:38:17,002 INFO: ✅ 百度地图API地址置信度良好: 龙华店, confidence=50, precise=0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
2025-06-08 21:38:17,003 INFO: ✅ 百度地图API成功解析: 龙华店 -> (114.03845498356466, 22.647407533355356) [confidence=50] [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:118]
2025-06-08 21:38:17,009 INFO: 地址解析成功 - 提供商: baidu, 地址: 龙华店 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:389]
2025-06-08 21:38:17,011 INFO: 地址解析成功: 龙华店 -> (114.03845498356466, 22.647407533355356) [in /home/<USER>/MyProj2025/app/api/routes.py:2811]
2025-06-08 21:38:17,012 INFO: 🌐 百度地图API调用: 深圳市-南山区-珠光路5号, URL: https://api.map.baidu.com/geocoding/v3/?address=%E6%B7%B1%E5%9C%B3%E5%B8%82-%E5%8D%97%E5%B1%B1%E5%8C%BA-%E7%8F%A0%E5%85%89%E8%B7%AF5%E5%8F%B7&output=json&ak=iCdis8rdiPYk5oryYiSWzCDoPhSATgAJ&city=%E6%B7%B1%E5%9C%B3%E5%B8%82 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:86]
2025-06-08 21:38:17,262 INFO: 🌐 百度地图API HTTP状态: 200 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:88]
2025-06-08 21:38:17,263 INFO: 🌐 百度地图API完整响应: {'status': 0, 'result': {'location': {'lng': 113.97332891207938, 'lat': 22.57361671404587}, 'precise': 1, 'confidence': 80, 'comprehension': 100, 'level': '门址'}} [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:92]
2025-06-08 21:38:17,264 INFO: 🌐 百度地图API状态码: 0 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:93]
2025-06-08 21:38:17,264 INFO: 🌐 百度地图API消息: N/A [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:94]
2025-06-08 21:38:17,265 INFO: ✅ 百度地图API地址置信度良好: 深圳市-南山区-珠光路5号, confidence=80, precise=1 [in /home/<USER>/MyProj2025/app/services/geocoding_service.py:116]
