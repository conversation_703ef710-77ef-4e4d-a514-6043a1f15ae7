<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终对话框测试 - 方案1统一CSS管理</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #ff6600;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.2s;
        }
        
        .test-button:hover {
            background: #e55a00;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .comparison-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .comparison-item h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .comparison-item ul {
            margin: 0;
            padding-left: 20px;
        }

        .comparison-item li {
            margin-bottom: 5px;
            font-size: 14px;
        }

        .status-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        .status-success {
            background: #f0f9ff;
            color: #1890ff;
        }

        .status-improved {
            background: #f6ffed;
            color: #52c41a;
        }

        /* 引入popup.html中的对话框样式 */
        .order-dialog-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(1px) !important;
            z-index: 2147483647 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        }

        .order-dialog {
            background: white;
            border-radius: 18px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 460px;
            width: 90%;
            max-height: 85vh;
            overflow: hidden;
            animation: dialogSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .dialog-header {
            padding: 6px 18px 4px;
            border-bottom: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border-radius: 18px 18px 0 0;
            color: white;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .dialog-header h3 {
            margin: 0;
            font-size: 15px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.3px;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            font-size: 18px;
            color: white;
            cursor: pointer;
            padding: 0;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .dialog-body {
            padding: 16px;
            max-height: 65vh;
            overflow-y: auto;
        }

        .order-info-section {
            margin-bottom: 12px;
        }

        .order-info-simple {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 14px;
            margin-bottom: 12px;
            line-height: 1.4;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        }

        .order-info-item {
            margin-bottom: 4px;
            font-size: 14px;
            color: #303133;
            display: flex;
            align-items: flex-start;
            line-height: 1.3;
        }

        .order-info-item:last-child {
            margin-bottom: 0;
        }

        .order-info-label {
            color: #4a5568;
            font-weight: 600;
            min-width: 70px;
            font-size: 14px;
            flex-shrink: 0;
        }

        .order-info-value {
            color: #303133;
            font-weight: 500;
            font-size: 14px;
        }

        .order-info-customer {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .customer-name {
            color: #303133;
            font-weight: 600;
            font-size: 14px;
        }

        .customer-plate {
            background: #f0f9ff;
            color: #1890ff;
            padding: 1px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
        }

        .destination-address {
            color: #606266;
            font-size: 13px;
            line-height: 1.3;
            word-break: break-all;
        }

        .modify-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .form-group label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 6px;
            position: relative;
            padding-left: 22px;
        }

        .form-group label::before {
            position: absolute;
            left: 0;
            top: 0;
            font-size: 14px;
        }

        .form-group label[data-icon="location"]::before {
            content: "📍";
        }

        .form-group label[data-icon="money"]::before {
            content: "💰";
        }

        .address-suggestion-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 6px 8px;
            background: #e8f4fd;
            border-radius: 6px;
            border: 1px solid #b3d8ff;
        }

        .toggle-label {
            font-size: 12px;
            color: #409eff;
            font-weight: 500;
        }

        .toggle-switch {
            position: relative;
            width: 36px;
            height: 20px;
            background: #dcdfe6;
            border-radius: 10px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .toggle-switch.active {
            background: #409eff;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active::after {
            transform: translateX(16px);
        }

        .address-input-container input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e4e7ed;
            border-radius: 6px;
            font-size: 13px;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }

        .address-input-container input:focus {
            outline: none;
            border-color: #ff6600;
            box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.1);
        }

        .estimate-display {
            background: white;
            border: 2px solid #e4e7ed;
            border-radius: 6px;
            padding: 10px;
            font-size: 13px;
            color: #606266;
            min-height: 16px;
            line-height: 1.3;
        }

        .dialog-footer {
            padding: 12px 16px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background: #fafafa;
            border-radius: 0 0 18px 18px;
        }

        .btn-cancel, .btn-confirm {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 70px;
        }

        .btn-cancel {
            background: #f5f7fa;
            color: #606266;
            border: 1px solid #dcdfe6;
        }

        .btn-cancel:hover {
            background: #ecf5ff;
            color: #409eff;
            border-color: #b3d8ff;
        }

        .btn-confirm {
            background: #ff6600;
            color: white;
        }

        .btn-confirm:hover:not(:disabled) {
            background: #e55a00;
        }

        .btn-confirm:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .btn-danger {
            background: #f56c6c;
            color: white;
        }

        .btn-danger:hover {
            background: #f78989;
        }

        .warning-message {
            padding: 10px;
            background: #fef0f0;
            border: 1px solid #fbc4c4;
            border-radius: 6px;
            margin-bottom: 12px;
        }

        .fee-info {
            padding: 8px 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            font-size: 12px;
            color: #606266;
        }

        @keyframes dialogSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 最终对话框测试 - 方案1统一CSS管理</h1>
        <p>测试修改和取消订单对话框的所有改进效果。</p>
        
        <div class="comparison-grid">
            <div class="comparison-item">
                <h4>✅ 已实现的改进</h4>
                <ul>
                    <li>标题高度缩小 <span class="status-improved">6px padding</span></li>
                    <li>字体大小适中 <span class="status-improved">14px</span></li>
                    <li>客户信息同行显示 <span class="status-improved">完成</span></li>
                    <li>添加图标标签 <span class="status-improved">📍💰</span></li>
                    <li>地址建议开关 <span class="status-improved">新增</span></li>
                    <li>CSS统一管理 <span class="status-success">方案1</span></li>
                </ul>
            </div>
            <div class="comparison-item">
                <h4>🔧 技术改进</h4>
                <ul>
                    <li>移除JavaScript CSS注入</li>
                    <li>样式统一在popup.html中</li>
                    <li>更可靠的样式加载</li>
                    <li>更好的维护性</li>
                    <li>响应式优化</li>
                    <li>动画效果优化</li>
                </ul>
            </div>
        </div>
        
        <h2>测试按钮</h2>
        <button class="test-button" onclick="testModifyDialog()">测试修改订单对话框</button>
        <button class="test-button" onclick="testCancelDialog()">测试取消订单对话框</button>
        <button class="test-button" onclick="testToggleSwitch()">测试地址建议开关</button>
    </div>

    <script>
        function testModifyDialog() {
            const dialog = document.createElement('div');
            dialog.className = 'order-dialog-overlay';

            dialog.innerHTML = `
                <div class="order-dialog">
                    <div class="dialog-header">
                        <h3>修改目的地</h3>
                        <button class="close-btn" onclick="closeDialog()">×</button>
                    </div>
                    <div class="dialog-body">
                        <div class="order-info-section">
                            <div class="order-info-simple">
                                <div class="order-info-item">
                                    <span class="order-info-label">订单号：</span>
                                    <span class="order-info-value">TEST_ORDER_123456</span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">客户：</span>
                                    <div class="order-info-customer">
                                        <span class="customer-name">张先生</span>
                                        <span class="customer-plate">粤B12345</span>
                                    </div>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">当前目的地：</span>
                                    <div class="destination-address">深圳市南山区科技园南区深南大道10000号</div>
                                </div>
                            </div>
                        </div>

                        <div class="modify-section">
                            <div class="form-group">
                                <label data-icon="location">新目的地地址</label>
                                <div class="address-suggestion-toggle">
                                    <span class="toggle-label">地址建议</span>
                                    <div class="toggle-switch active" onclick="toggleAddressSuggestion(this)">
                                    </div>
                                </div>
                                <div class="address-input-container">
                                    <input type="text" placeholder="请输入新的目的地地址" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label data-icon="money">预估费用</label>
                                <div class="estimate-display">输入地址后自动计算</div>
                            </div>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button class="btn-cancel" onclick="closeDialog()">取消</button>
                        <button class="btn-confirm">确认修改</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);
        }

        function testCancelDialog() {
            const dialog = document.createElement('div');
            dialog.className = 'order-dialog-overlay';

            dialog.innerHTML = `
                <div class="order-dialog">
                    <div class="dialog-header">
                        <h3>取消订单</h3>
                        <button class="close-btn" onclick="closeDialog()">×</button>
                    </div>
                    <div class="dialog-body">
                        <div class="order-info-section">
                            <div class="order-info-simple">
                                <div class="order-info-item">
                                    <span class="order-info-label">订单号：</span>
                                    <span class="order-info-value">TEST_ORDER_123456</span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">客户：</span>
                                    <div class="order-info-customer">
                                        <span class="customer-name">张先生</span>
                                        <span class="customer-plate">粤B12345</span>
                                    </div>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">目的地：</span>
                                    <div class="destination-address">深圳市南山区科技园南区深南大道10000号</div>
                                </div>
                            </div>
                        </div>

                        <div class="warning-message">
                            <p style="margin: 0 0 8px 0; color: #f56c6c; font-weight: 500;">确定要取消这个订单吗？</p>
                            <p style="margin: 0; color: #909399; font-size: 12px;">取消后订单将无法恢复，可能产生相应费用</p>
                            <p style="margin: 8px 0 0 0; color: #67c23a; font-size: 12px; font-weight: 500;">💡 当前订单处于等待派单状态，通常可以免费取消</p>
                        </div>

                        <div class="fee-section">
                            <h5 style="margin: 0 0 8px 0; color: #606266; font-size: 13px; font-weight: 500;">💰 费用信息</h5>
                            <div class="fee-info">
                                <div>取消费用: ¥0.00</div>
                                <div>等待费用: ¥0.00</div>
                                <div><strong>总费用: ¥0.00</strong></div>
                            </div>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button class="btn-cancel" onclick="closeDialog()">不取消</button>
                        <button class="btn-confirm btn-danger">确认取消</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);
        }

        function testToggleSwitch() {
            alert('地址建议开关功能：\n\n✅ 开启时显示地址建议\n❌ 关闭时隐藏地址建议\n\n点击修改订单对话框中的开关测试此功能');
        }

        function toggleAddressSuggestion(toggle) {
            toggle.classList.toggle('active');
            const isActive = toggle.classList.contains('active');
            console.log(`地址建议功能: ${isActive ? '开启' : '关闭'}`);
        }

        function closeDialog() {
            const dialog = document.querySelector('.order-dialog-overlay');
            if (dialog) {
                dialog.remove();
            }
        }

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeDialog();
            }
        });
    </script>
</body>
</html>
