/**
 * F6 e代驾助手 - UI界面模块
 *
 * @description F6智能门店系统的e代驾订单管理插件 - UI界面
 * @version 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2025 Yu <PERSON>. All rights reserved.
 * @license MIT License
 *
 * 负责订单表单和通知等UI界面，支持表单状态持久化
 */

/**
 * 表单状态管理器 - 解决表单数据丢失问题
 */
class FormStateManager {
    constructor() {
        this.storageKey = 'f6_edriver_form_state';
        this.autoSaveDelay = 500; // 500ms防抖
        this.maxStateAge = 3600000; // 1小时状态过期
        this.saveTimeout = null;
    }

    /**
     * 保存表单状态
     */
    saveFormState(formData, vehicleInfo) {
        try {
            const stateData = {
                formData,
                vehicleInfo,
                timestamp: Date.now(),
                version: '1.0.0'
            };
            localStorage.setItem(this.storageKey, JSON.stringify(stateData));
            console.log('表单状态已保存:', stateData);

            // 显示保存指示器
            this.showSaveIndicator();
        } catch (error) {
            console.error('保存表单状态失败:', error);
        }
    }

    /**
     * 恢复表单状态
     */
    restoreFormState() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (!saved) return null;

            const state = JSON.parse(saved);
            const age = Date.now() - state.timestamp;

            // 状态超过1小时自动清除
            if (age > this.maxStateAge) {
                this.clearFormState();
                console.log('表单状态已过期，自动清除');
                return null;
            }

            console.log('表单状态已恢复:', state);
            return state;
        } catch (error) {
            console.error('恢复表单状态失败:', error);
            return null;
        }
    }

    /**
     * 清除表单状态
     */
    clearFormState() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('表单状态已清除');
        } catch (error) {
            console.error('清除表单状态失败:', error);
        }
    }

    /**
     * 检查是否有保存的表单状态
     */
    hasFormState() {
        const state = this.restoreFormState();
        return state !== null;
    }

    /**
     * 防抖保存表单状态
     */
    debouncedSave(formData, vehicleInfo) {
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
        }

        this.saveTimeout = setTimeout(() => {
            this.saveFormState(formData, vehicleInfo);
        }, this.autoSaveDelay);
    }

    /**
     * 显示保存指示器
     */
    showSaveIndicator() {
        // 移除现有指示器
        const existingIndicator = document.getElementById('form-save-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // 创建新指示器
        const indicator = document.createElement('div');
        indicator.id = 'form-save-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #52c41a;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10001;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        indicator.textContent = '✅ 已自动保存';

        document.body.appendChild(indicator);

        // 显示动画
        setTimeout(() => {
            indicator.style.opacity = '1';
        }, 10);

        // 2秒后淡出
        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }, 2000);
    }
}

class F6EdriverUI {
    constructor() {
        this.currentModal = null;
        this.formStateManager = new FormStateManager();
        this.autoSaveForm = null;
    }

    /**
     * 创建e代驾下单按钮
     * @param {Function} onClick - 点击回调函数
     * @returns {HTMLElement}
     */
    createOrderButton(onClick) {
        const span = document.createElement('span');
        span.className = 'listTable_textLink__30Zp1d';
        span.textContent = '代驾';
        span.title = '为该客户创建代驾订单';
        span.style.cursor = 'pointer';
        span.style.marginLeft = '8px'; // 添加左边距，匹配F6的operationMargin效果

        span.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            onClick(e);
        });

        return span;
    }

    /**
     * 创建查看订单详情按钮
     * @param {Function} onClick - 点击回调函数
     * @returns {HTMLElement}
     */
    createViewOrderButton(onClick) {
        const link = document.createElement('a');
        link.href = 'javascript:void(0)';
        link.className = 'edriver-text-link';
        link.textContent = 'e代驾订单';
        link.title = '查看该客户的e代驾订单详情';

        link.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            onClick(e);
        });

        return link;
    }



    /**
     * 显示下单表单模态框
     * @param {Object} vehicleInfo - 车辆信息
     * @param {Function} onSubmit - 提交回调函数
     */
    showOrderForm(vehicleInfo, onSubmit) {
        // 将车辆信息存储到全局变量中，供切换地址时使用
        window.currentVehicleInfo = vehicleInfo;

        // 检查是否有保存的表单状态
        if (this.formStateManager.hasFormState()) {
            this.showFormRestoreDialog(vehicleInfo, onSubmit);
        } else {
            const modal = this.createModal('创建e代驾订单', this.createOrderFormContent(vehicleInfo, onSubmit));
            this.showModal(modal);
        }
    }

    /**
     * 显示表单恢复对话框
     */
    showFormRestoreDialog(vehicleInfo, onSubmit) {
        const savedState = this.formStateManager.restoreFormState();
        if (!savedState) {
            // 没有有效状态，直接显示新表单
            const modal = this.createModal('创建e代驾订单', this.createOrderFormContent(vehicleInfo, onSubmit));
            this.showModal(modal);
            return;
        }

        // 创建恢复对话框
        const dialogContent = document.createElement('div');
        dialogContent.style.cssText = 'text-align: center; padding: 20px; font-family: "PingFang SC", "Microsoft YaHei", sans-serif;';

        const saveTime = new Date(savedState.timestamp).toLocaleString('zh-CN');
        dialogContent.innerHTML = `
            <div style="margin-bottom: 20px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
                <h3 style="margin: 0 0 10px 0; color: #303133;">发现未完成的订单</h3>
                <p style="color: #606266; margin: 0; font-size: 14px;">保存时间: ${saveTime}</p>
            </div>

            <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px; text-align: left;">
                <div style="font-size: 12px; color: #909399; margin-bottom: 8px;">车辆信息:</div>
                <div style="font-size: 14px; color: #303133;">
                    ${savedState.vehicleInfo.customerName || '未知客户'} •
                    ${savedState.vehicleInfo.plateNumber || '未知车牌'}
                </div>
                ${savedState.formData.pickup_address ? `
                    <div style="font-size: 12px; color: #909399; margin: 8px 0 4px 0;">取车地址:</div>
                    <div style="font-size: 14px; color: #303133;">${savedState.formData.pickup_address}</div>
                ` : ''}
                ${savedState.formData.destination_address ? `
                    <div style="font-size: 12px; color: #909399; margin: 8px 0 4px 0;">送车地址:</div>
                    <div style="font-size: 14px; color: #303133;">${savedState.formData.destination_address}</div>
                ` : ''}
            </div>

            <div style="display: flex; gap: 10px; justify-content: center;">
                <button id="restore-continue" style="
                    background: #409eff;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">继续填写</button>
                <button id="restore-new" style="
                    background: #909399;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">重新开始</button>
            </div>
        `;

        const restoreModal = this.createModal('恢复订单信息', dialogContent);

        // 绑定按钮事件
        dialogContent.querySelector('#restore-continue').addEventListener('click', () => {
            this.closeModal();
            this.showOrderFormWithState(vehicleInfo, onSubmit, savedState);
        });

        dialogContent.querySelector('#restore-new').addEventListener('click', () => {
            this.formStateManager.clearFormState();
            this.closeModal();
            const modal = this.createModal('创建e代驾订单', this.createOrderFormContent(vehicleInfo, onSubmit));
            this.showModal(modal);
        });

        this.showModal(restoreModal);
    }

    /**
     * 显示带有恢复状态的订单表单
     */
    showOrderFormWithState(vehicleInfo, onSubmit, savedState) {
        const modal = this.createModal('创建e代驾订单', this.createOrderFormContent(vehicleInfo, onSubmit));
        this.showModal(modal);

        // 延迟恢复表单数据，确保DOM已渲染
        setTimeout(() => {
            this.restoreFormData(savedState.formData);
            this.showRestoreSuccessMessage();
        }, 100);
    }

    /**
     * 恢复表单数据
     */
    restoreFormData(formData) {
        console.log('开始恢复表单数据:', formData);

        Object.entries(formData).forEach(([key, value]) => {
            const element = document.getElementById(key);
            if (element && value !== undefined && value !== null) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else if (element.type === 'radio') {
                    if (element.value === value) {
                        element.checked = true;
                    }
                } else {
                    element.value = value;
                }

                // 触发change事件以更新相关UI
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });

        // 恢复数据后，重新触发地址建议和预估价功能
        setTimeout(() => {
            this.triggerPostRestoreActions();
        }, 200);
    }

    /**
     * 恢复数据后触发的操作
     */
    triggerPostRestoreActions() {
        console.log('触发恢复后操作');

        // 重新验证表单
        this.validateForm();

        // 检查是否需要触发预估费用（恢复数据后必须重新估价）
        const pickupAddress = document.getElementById('pickup-address')?.value?.trim();
        const destinationAddress = document.getElementById('destination-address')?.value?.trim();

        if (pickupAddress && destinationAddress &&
            pickupAddress.length >= 2 && destinationAddress.length >= 5) {
            console.log('恢复数据后重新调用API估价（价格可能已变化）');

            // 清除之前的估价状态，强制重新估价
            this.estimationStatus.isEstimated = false;
            this.estimationStatus.isEstimating = false;

            // 设置恢复触发标志
            this.isRestoreTriggered = true;

            // 延迟调用估价API，确保DOM完全更新
            setTimeout(() => {
                this.estimateOrderCost();
                // 清除恢复触发标志
                this.isRestoreTriggered = false;
            }, 500);
        } else {
            console.log('恢复数据后地址不完整，无法估价:', {
                pickupAddress: pickupAddress,
                destinationAddress: destinationAddress,
                pickupLength: pickupAddress?.length,
                destLength: destinationAddress?.length
            });
        }

        // 重新设置联系方式（如果需要）
        const vehicleInfo = window.currentVehicleInfo;
        const contactPhoneInput = document.getElementById('contact-phone');
        const contactPhoneHint = document.getElementById('contact-phone-hint');

        if (vehicleInfo && pickupAddress && contactPhoneInput && contactPhoneHint) {
            this.updateContactPhone(vehicleInfo, pickupAddress, contactPhoneInput, contactPhoneHint);
        }
    }

    /**
     * 显示恢复成功消息
     */
    showRestoreSuccessMessage() {
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            background: #67c23a;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10002;
            animation: slideDown 0.3s ease-out;
            max-width: 300px;
            text-align: center;
        `;
        message.innerHTML = '✅ 订单信息已恢复<br><small style="font-size: 12px; opacity: 0.9;">正在重新计算最新价格...</small>';

        // 添加CSS动画
        if (!document.getElementById('form-animations')) {
            const style = document.createElement('style');
            style.id = 'form-animations';
            style.textContent = `
                @keyframes slideDown {
                    from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                    to { opacity: 1; transform: translateX(-50%) translateY(0); }
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(message);

        // 3秒后自动移除
        setTimeout(() => {
            if (message.parentNode) {
                message.style.animation = 'slideDown 0.3s ease-out reverse';
                setTimeout(() => {
                    if (message.parentNode) {
                        message.parentNode.removeChild(message);
                    }
                }, 300);
            }
        }, 3000);
    }

    /**
     * 创建下单表单内容
     * @param {Object} vehicleInfo - 车辆信息
     * @param {Function} onSubmit - 提交回调函数
     * @returns {HTMLElement}
     */
    createOrderFormContent(vehicleInfo, onSubmit) {
        const form = document.createElement('div');
        form.innerHTML = `
            <div style="margin-bottom: 16px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">
                <h6 style="margin-bottom: 8px; color: #303133; font-size: 14px; font-weight: 600; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">📋 车辆信息</h6>
                <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 16px; border: 1px solid #e4e7ed;">
                    <div style="margin-bottom: 6px; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;"><strong style="color: #606266;">客户:</strong> <span style="color: #303133;">${vehicleInfo.customerName || '未知客户'}</span></div>
                    <div style="margin-bottom: 6px; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;"><strong style="color: #606266;">手机:</strong> <span style="color: #303133;">${vehicleInfo.customerPhone || '无'}</span></div>
                    <div style="margin-bottom: 6px; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;"><strong style="color: #606266;">车牌:</strong> <span style="color: #303133;">${vehicleInfo.plateNumber || '未知'}</span></div>
                    <div style="font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;"><strong style="color: #606266;">车型:</strong> <span style="color: #303133;">${vehicleInfo.carModel || '未知'}</span></div>
                </div>
            </div>

            <!-- 隐藏的店面信息 -->
            <input type="hidden" id="store-name">

            <!-- 地址输入区域 -->
            <div style="margin-bottom: 16px;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                    <label style="font-weight: 500; color: #606266; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">📍 取车地址</label>
                    <button type="button" id="swap-addresses" class="edriver-btn" style="padding: 6px 12px; font-size: 12px; background: #e6a23c; border: none; border-radius: 4px; color: white; cursor: pointer; transition: all 0.3s ease;">🔄 切换</button>
                </div>
                <div id="pickup-address-container" style="transition: all 0.3s ease;">
                    <input type="text" id="pickup-address" placeholder="店面地址加载中..." autocomplete="off"
                           style="width: 100%; padding: 10px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; box-sizing: border-box; transition: all 0.3s ease;">
                </div>
                <input type="hidden" id="pickup-longitude">
                <input type="hidden" id="pickup-latitude">
            </div>

            <div style="margin-bottom: 16px;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 6px;">
                    <label style="font-weight: 500; color: #606266; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">🏁 送车地址 *</label>
                    <button type="button" id="toggle-dest-suggestions" class="edriver-btn" style="padding: 4px 8px; font-size: 12px; background: #17a2b8; border: none; border-radius: 4px; color: white; cursor: pointer; transition: all 0.3s ease;" title="切换地址建议">💡</button>
                </div>
                <div id="destination-address-container" style="position: relative; transition: all 0.3s ease;">
                    <input type="text" id="destination-address" placeholder="请输入送车地址" autocomplete="off"
                           style="width: 100%; padding: 10px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; box-sizing: border-box; transition: all 0.3s ease;">
                    <div id="destination-suggestions" style="display: none; position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #dcdfe6; border-top: none; border-radius: 0 0 4px 4px; max-height: 200px; overflow-y: auto; z-index: 1000; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"></div>
                </div>
                <input type="hidden" id="destination-longitude">
                <input type="hidden" id="destination-latitude">
                <div style="font-size: 12px; color: #909399; margin-top: 4px;">💡 请提供客户详尽地址，定位才精确。</div>
            </div>

            <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #606266; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">⏰ 订单类型</label>
                <select id="order-type" style="width: 100%; padding: 10px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; box-sizing: border-box; background: white;">
                    <option value="now">代叫</option>
                    <option value="reserve">预约</option>
                </select>
            </div>

            <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #606266; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">📞 联系方式</label>
                <input type="text" id="contact-phone" placeholder="联系电话"
                       style="width: 100%; padding: 10px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; box-sizing: border-box;">
                <div id="contact-phone-hint" style="margin-top: 4px; font-size: 12px; color: #909399; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">
                    手机号用于司机可以联系到店里
                </div>
            </div>

            <div id="reserve-time-group" style="margin-bottom: 16px; display: none;">
                <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #606266; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">📅 预约时间</label>
                <input type="datetime-local" id="reserve-time"
                       style="width: 100%; padding: 10px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; box-sizing: border-box;">
            </div>



            <!-- 支付方式固定为"我来支付" -->
            <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #606266; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">💳 支付方式</label>
                <div style="padding: 10px 12px; border: 1px solid #e4e7ed; border-radius: 4px; background: #f5f7fa; color: #606266; font-size: 14px;">
                    我来支付
                </div>
                <input type="hidden" id="payment-method" value="self">
            </div>

            <!-- 预估费用显示区域 -->
            <div id="cost-estimate-section" style="margin-bottom: 16px; display: none;">
                <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #606266; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">💰 预估费用</label>
                <div id="cost-estimate-display" style="padding: 12px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef; font-size: 13px; line-height: 1.4;">
                    输入完整地址后自动计算费用
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #606266; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">📝 备注</label>
                <textarea id="order-remarks" placeholder="订单备注信息（选填）"
                          style="width: 100%; padding: 10px 12px; border: 1px solid #dcdfe6; border-radius: 4px; height: 60px; resize: vertical; font-size: 14px; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; box-sizing: border-box;"></textarea>
            </div>

            <div style="text-align: right; padding-top: 12px; border-top: 1px solid #e4e7ed;">
                <button type="button" id="cancel-order" class="edriver-btn"
                        style="background: #909399; margin-right: 10px; border-color: #909399; font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;">取消</button>
                <button type="button" id="submit-order" class="edriver-btn edriver-btn-primary"
                        style="font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; opacity: 0.5; cursor: not-allowed;"
                        disabled>创建订单</button>
            </div>
        `;

        // 添加事件监听
        const orderTypeSelect = form.querySelector('#order-type');
        const reserveTimeGroup = form.querySelector('#reserve-time-group');
        const pickupAddressInput = form.querySelector('#pickup-address');
        const contactPhoneInput = form.querySelector('#contact-phone');
        const contactPhoneHint = form.querySelector('#contact-phone-hint');

        orderTypeSelect.addEventListener('change', () => {
            reserveTimeGroup.style.display = orderTypeSelect.value === 'reserve' ? 'block' : 'none';
            this.validateForm();
        });

        // 监听取车地址变化，智能设置联系方式
        pickupAddressInput.addEventListener('input', () => {
            this.updateContactPhone(vehicleInfo, pickupAddressInput.value, contactPhoneInput, contactPhoneHint);
        });

        // 注意：不在这里初始化联系方式，因为此时取车地址还没有设置
        // 联系方式的初始化由 setFixedStoreInfo() 方法负责

        // 监听联系方式输入，如果是店面取车且用户手动输入了手机号，保存到本地存储
        contactPhoneInput.addEventListener('blur', () => {
            const phone = contactPhoneInput.value.trim();
            const isStorePickup = this.isStoreAddress(pickupAddressInput.value, F6Utils.getStoreName());

            if (isStorePickup && phone && /^1[3-9]\d{9}$/.test(phone)) {
                // 保存店长手机号到本地存储
                localStorage.setItem('f6_manager_phone', phone);
                F6Utils.log('info', '保存店长手机号到本地存储', phone);
            }
            this.validateForm();
        });

        // 监听联系方式输入变化
        contactPhoneInput.addEventListener('input', () => {
            this.validateForm();
        });

        // 监听预约时间变化
        const reserveTimeInput = form.querySelector('#reserve-time');
        if (reserveTimeInput) {
            reserveTimeInput.addEventListener('change', () => {
                this.validateForm();
            });
        }

        form.querySelector('#cancel-order').addEventListener('click', () => {
            this.closeModal();
        });

        form.querySelector('#submit-order').addEventListener('click', () => {
            this.handleOrderSubmit(vehicleInfo, onSubmit);
        });

        // 添加定位按钮事件（如果按钮存在）
        const pickupLocationBtn = form.querySelector('#get-pickup-location');
        if (pickupLocationBtn) {
            pickupLocationBtn.addEventListener('click', () => {
                this.getCurrentLocation('pickup');
            });
        }

        const destinationLocationBtn = form.querySelector('#get-destination-location');
        if (destinationLocationBtn) {
            destinationLocationBtn.addEventListener('click', () => {
                this.getCurrentLocation('destination');
            });
        }

        // 添加地址切换事件
        form.querySelector('#swap-addresses').addEventListener('click', () => {
            this.swapAddresses();
        });

        // 添加地址建议切换事件（仅送车地址）
        form.querySelector('#toggle-dest-suggestions').addEventListener('click', () => {
            this.toggleAddressSuggestions('destination');
        });

        // 添加地址输入监听，用于地址建议和预估费用
        const destinationAddressInput = form.querySelector('#destination-address');
        let estimateDebounceTimer;
        let destSuggestionTimer;

        // 取车地址输入监听（仅联系方式更新和表单验证）
        pickupAddressInput.addEventListener('input', () => {
            this.updateContactPhone(vehicleInfo, pickupAddressInput.value, contactPhoneInput, contactPhoneHint);
            this.validateForm(); // 验证表单状态
        });

        // 送车地址输入监听
        destinationAddressInput.addEventListener('input', () => {
            // 地址建议
            clearTimeout(destSuggestionTimer);
            destSuggestionTimer = setTimeout(() => {
                this.handleAddressInput('destination', destinationAddressInput.value);
            }, 500);

            // 预估费用
            clearTimeout(estimateDebounceTimer);
            estimateDebounceTimer = setTimeout(() => {
                this.estimateOrderCost();
            }, 1000); // 1秒防抖

            this.validateForm(); // 验证表单状态
        });

        // 点击其他地方隐藏建议
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#destination-address-container')) {
                this.hideAllSuggestions();
            }
        });

        // 设置固定的店面信息
        this.setFixedStoreInfo();

        // 设置表单自动保存
        this.setupFormAutoSave(form, vehicleInfo);

        // 初始化表单验证状态
        setTimeout(() => {
            this.validateForm();
        }, 200);

        return form;
    }

    /**
     * 地址建议功能状态
     */
    addressSuggestionsEnabled = {
        pickup: false,
        destination: false
    };

    /**
     * 估值状态
     */
    estimationStatus = {
        isEstimated: false,
        isEstimating: false
    };

    /**
     * 切换地址建议功能
     */
    toggleAddressSuggestions(type) {
        this.addressSuggestionsEnabled[type] = !this.addressSuggestionsEnabled[type];

        const buttonId = type === 'pickup' ? 'toggle-pickup-suggestions' : 'toggle-dest-suggestions';
        const button = document.getElementById(buttonId);
        const suggestionsDiv = document.getElementById(`${type}-suggestions`);

        if (this.addressSuggestionsEnabled[type]) {
            button.style.background = '#28a745';
            button.title = '关闭地址建议';
        } else {
            button.style.background = '#17a2b8';
            button.title = '开启地址建议';
            suggestionsDiv.style.display = 'none';
            this.removeSuggestionsActiveClass(type);
        }
    }

    /**
     * 处理地址输入
     */
    handleAddressInput(type, value) {
        if (!this.addressSuggestionsEnabled[type]) {
            return;
        }

        // 如果输入为空或太短，隐藏建议
        if (!value || value.trim().length < 2) {
            this.hideSuggestions(type);
            return;
        }

        // 检查是否为店面地址
        if (this.isStoreAddressForSuggestions(value)) {
            this.hideSuggestions(type);
            return;
        }

        // 搜索地址建议
        this.searchAddressSuggestions(type, value.trim());
    }

    /**
     * 判断是否为店面地址（用于地址建议）
     */
    isStoreAddressForSuggestions(address) {
        if (!address) return false;

        // 检查常见店面关键词
        const storeKeywords = ['康众汽配', '门店', '店面', '汽配店', '修理厂'];
        for (const keyword of storeKeywords) {
            if (address.includes(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 搜索地址建议
     */
    async searchAddressSuggestions(type, query) {
        const suggestionsDiv = document.getElementById(`${type}-suggestions`);
        const addressInput = document.getElementById(`${type}-address`);

        // 显示加载状态
        this.showSuggestionsLoading(type);
        this.addSuggestionsActiveClass(type);

        try {
            const response = await fetch('http://localhost:5000/api/geocoding/suggestions', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    query: query,
                    city: '深圳市',
                    limit: 6
                })
            });

            const data = await response.json();

            if (data.success && data.suggestions && data.suggestions.length > 0) {
                this.showAddressSuggestions(type, data.suggestions);
            } else {
                this.showSuggestionsEmpty(type);
            }
        } catch (error) {
            console.error('获取地址建议失败:', error);
            this.showSuggestionsEmpty(type);
        }
    }

    /**
     * 显示地址建议
     */
    showAddressSuggestions(type, suggestions) {
        const suggestionsDiv = document.getElementById(`${type}-suggestions`);
        let html = '';

        suggestions.forEach(suggestion => {
            const mainAddress = suggestion.address || suggestion.formatted_address || '';
            const detailAddress = suggestion.formatted_address || '';

            // 如果主地址和详细地址相同，只显示一个
            const displayDetail = mainAddress !== detailAddress ? detailAddress : '';

            html += `
                <div class="suggestion-item" data-address="${suggestion.formatted_address || suggestion.address || ''}"
                     style="padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: background-color 0.2s;">
                    <div style="font-weight: 500; color: #333;">${mainAddress}</div>
                    ${displayDetail ? `<div style="font-size: 12px; color: #666; margin-top: 2px;">${displayDetail}</div>` : ''}
                </div>
            `;
        });

        suggestionsDiv.innerHTML = html;
        suggestionsDiv.style.display = 'block';

        // 绑定点击事件
        suggestionsDiv.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const address = item.getAttribute('data-address');
                document.getElementById(`${type}-address`).value = address;
                this.hideSuggestions(type);

                // 如果是送车地址，触发费用估算
                if (type === 'destination') {
                    setTimeout(() => this.estimateOrderCost(), 100);
                }
            });

            // 悬停效果
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = '#f8f9fa';
            });

            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = '';
            });
        });
    }

    /**
     * 显示加载状态
     */
    showSuggestionsLoading(type) {
        const suggestionsDiv = document.getElementById(`${type}-suggestions`);
        suggestionsDiv.innerHTML = `
            <div style="padding: 12px; text-align: center; color: #666;">
                <span style="display: inline-block; width: 12px; height: 12px; border: 2px solid #ddd; border-top: 2px solid #17a2b8; border-radius: 50%; animation: spin 1s linear infinite;"></span>
                <span style="margin-left: 8px;">搜索中...</span>
            </div>
        `;
        suggestionsDiv.style.display = 'block';
    }

    /**
     * 显示空结果
     */
    showSuggestionsEmpty(type) {
        const suggestionsDiv = document.getElementById(`${type}-suggestions`);
        suggestionsDiv.innerHTML = `
            <div style="padding: 12px; text-align: center; color: #999; font-style: italic;">
                未找到相关地址
            </div>
        `;
        suggestionsDiv.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            this.hideSuggestions(type);
        }, 3000);
    }

    /**
     * 隐藏建议
     */
    hideSuggestions(type) {
        const suggestionsDiv = document.getElementById(`${type}-suggestions`);
        if (suggestionsDiv) {
            suggestionsDiv.style.display = 'none';
        }
        this.removeSuggestionsActiveClass(type);
    }

    /**
     * 隐藏所有建议
     */
    hideAllSuggestions() {
        this.hideSuggestions('pickup');
        this.hideSuggestions('destination');
    }

    /**
     * 添加建议激活样式
     */
    addSuggestionsActiveClass(type) {
        const addressInput = document.getElementById(`${type}-address`);
        if (addressInput) {
            addressInput.style.borderBottomLeftRadius = '0';
            addressInput.style.borderBottomRightRadius = '0';
        }
    }

    /**
     * 移除建议激活样式
     */
    removeSuggestionsActiveClass(type) {
        const addressInput = document.getElementById(`${type}-address`);
        if (addressInput) {
            addressInput.style.borderBottomLeftRadius = '4px';
            addressInput.style.borderBottomRightRadius = '4px';
        }
    }

    /**
     * 验证表单状态并更新提交按钮
     */
    validateForm() {
        const submitButton = document.getElementById('submit-order');
        if (!submitButton) return;

        // 获取必填字段
        const pickupAddress = document.getElementById('pickup-address')?.value?.trim();
        const destinationAddress = document.getElementById('destination-address')?.value?.trim();
        const contactPhone = document.getElementById('contact-phone')?.value?.trim();
        const orderType = document.getElementById('order-type')?.value;
        const reserveTime = document.getElementById('reserve-time')?.value;

        // 检查必填字段
        let isValid = true;
        let missingFields = [];

        if (!pickupAddress || pickupAddress.length < 2) {
            isValid = false;
            missingFields.push('取车地址');
        }

        if (!destinationAddress || destinationAddress.length < 5) {
            isValid = false;
            missingFields.push('送车地址');
        }

        if (!contactPhone || !/^1[3-9]\d{9}$/.test(contactPhone)) {
            isValid = false;
            missingFields.push('联系电话');
        }

        // 如果是预约订单，检查预约时间
        if (orderType === 'reserve' && !reserveTime) {
            isValid = false;
            missingFields.push('预约时间');
        }

        // 检查是否已完成估值
        if (!this.estimationStatus.isEstimated) {
            isValid = false;
            if (destinationAddress && destinationAddress.length >= 5) {
                missingFields.push('费用估算');
            }
        }

        // 更新按钮状态
        if (isValid) {
            submitButton.disabled = false;
            submitButton.style.opacity = '1';
            submitButton.style.cursor = 'pointer';
            submitButton.title = '点击创建订单';
        } else {
            submitButton.disabled = true;
            submitButton.style.opacity = '0.5';
            submitButton.style.cursor = 'not-allowed';

            if (missingFields.length > 0) {
                submitButton.title = `请完善：${missingFields.join('、')}`;
            } else {
                submitButton.title = '请完善必填信息';
            }
        }
    }

    /**
     * 预估订单费用
     */
    async estimateOrderCost() {
        try {
            const pickupAddress = document.getElementById('pickup-address')?.value?.trim();
            const destinationAddress = document.getElementById('destination-address')?.value?.trim();
            const costEstimateSection = document.getElementById('cost-estimate-section');
            const costEstimateDisplay = document.getElementById('cost-estimate-display');

            console.log('开始估值:', {
                pickupAddress,
                destinationAddress,
                isRestoreTriggered: this.isRestoreTriggered || false,
                timestamp: new Date().toLocaleTimeString()
            });

            // 检查地址是否足够完整（取车地址至少2个字符，送车地址至少5个字符）
            if (!pickupAddress || !destinationAddress ||
                pickupAddress.length < 2 || destinationAddress.length < 5) {
                console.log('地址不满足估值条件:', {
                    pickupLength: pickupAddress?.length,
                    destLength: destinationAddress?.length
                });
                costEstimateSection.style.display = 'none';
                this.estimationStatus.isEstimated = false;
                this.estimationStatus.isEstimating = false;
                this.validateForm();
                return;
            }

            console.log('地址满足估值条件，开始估值');

            // 显示预估费用区域
            costEstimateSection.style.display = 'block';
            costEstimateDisplay.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; padding: 8px;">
                    <div style="width: 16px; height: 16px; border: 2px solid #ddd; border-top: 2px solid #ff6600; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 8px;"></div>
                    <span style="color: #606266;">正在计算费用...</span>
                </div>
            `;
            this.estimationStatus.isEstimating = true;
            this.estimationStatus.isEstimated = false;
            this.validateForm();

            // 获取店长名称
            const managerName = F6Utils.getStoreName();
            console.log('获取到店长名称:', managerName);
            if (!managerName) {
                console.log('无法获取店长名称');
                costEstimateDisplay.textContent = '无法获取店面信息，请刷新页面';
                this.estimationStatus.isEstimating = false;
                this.estimationStatus.isEstimated = false;
                this.validateForm();
                return;
            }

            console.log('准备调用估值API:', {
                start_address: pickupAddress,
                end_address: destinationAddress,
                manager_name: managerName
            });

            // 调用预估费用API
            const response = await fetch('http://localhost:5000/api/f6/estimate_cost', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    start_address: pickupAddress,
                    end_address: destinationAddress,
                    manager_name: managerName
                })
            });

            console.log('API响应状态:', response.status);
            const data = await response.json();
            console.log('API响应数据:', data);

            if (data.success && data.estimate) {
                const estimate = data.estimate;
                const restoreNote = this.isRestoreTriggered ?
                    '<div style="font-size: 11px; color: #67c23a; margin-bottom: 4px; font-weight: 500;">🔄 已更新为最新价格</div>' : '';

                costEstimateDisplay.innerHTML = `
                    ${restoreNote}
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>基础费用:</span>
                        <span>¥${estimate.base_fee}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>距离费用:</span>
                        <span>¥${estimate.distance_fee}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-weight: bold; padding-top: 4px; border-top: 1px solid #ddd;">
                        <span>预估总费用:</span>
                        <span style="color: #ff6600;">¥${estimate.total_fee}</span>
                    </div>
                    <div style="font-size: 11px; color: #909399; margin-top: 4px;">
                        预计距离: ${estimate.distance}km | 预计时间: ${estimate.duration}分钟
                    </div>
                `;
                this.estimationStatus.isEstimated = true;
                this.estimationStatus.isEstimating = false;
            } else {
                costEstimateDisplay.innerHTML = `
                    <div style="color: #f56c6c; text-align: center; padding: 8px;">
                        ❌ 费用计算失败: ${data.message || '未知错误'}
                    </div>
                `;
                this.estimationStatus.isEstimated = false;
                this.estimationStatus.isEstimating = false;
            }
        } catch (error) {
            console.error('预估费用失败:', error);
            const costEstimateDisplay = document.getElementById('cost-estimate-display');
            if (costEstimateDisplay) {
                costEstimateDisplay.innerHTML = `
                    <div style="color: #f56c6c; text-align: center; padding: 8px;">
                        ❌ 费用计算失败，请检查网络连接
                    </div>
                `;
            }
            this.estimationStatus.isEstimated = false;
            this.estimationStatus.isEstimating = false;
        }

        // 更新表单验证状态
        this.validateForm();
    }

    /**
     * 设置表单自动保存
     */
    setupFormAutoSave(form, vehicleInfo) {
        // 获取所有需要监听的表单元素
        const formElements = form.querySelectorAll('input, select, textarea');

        // 为每个表单元素添加自动保存监听
        formElements.forEach(element => {
            // 监听输入事件
            element.addEventListener('input', () => {
                this.debouncedSaveFormState(vehicleInfo);
            });

            // 监听变化事件
            element.addEventListener('change', () => {
                this.debouncedSaveFormState(vehicleInfo);
            });
        });

        // 监听模态框关闭前保存状态
        window.addEventListener('beforeunload', () => {
            this.saveFormStateNow(vehicleInfo);
        });

        console.log('表单自动保存已设置');
    }

    /**
     * 防抖保存表单状态
     */
    debouncedSaveFormState(vehicleInfo) {
        if (this.saveFormTimeout) {
            clearTimeout(this.saveFormTimeout);
        }

        this.saveFormTimeout = setTimeout(() => {
            this.saveFormStateNow(vehicleInfo);
        }, 500);
    }

    /**
     * 立即保存表单状态
     */
    saveFormStateNow(vehicleInfo) {
        try {
            const formData = this.collectFormData();
            if (formData && Object.keys(formData).length > 0) {
                this.formStateManager.debouncedSave(formData, vehicleInfo);
            }
        } catch (error) {
            console.error('保存表单状态失败:', error);
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const formData = {};

        // 定义需要保存的字段
        const fieldIds = [
            'store-name',
            'pickup-address',
            'destination-address',
            'pickup-longitude',
            'pickup-latitude',
            'destination-longitude',
            'destination-latitude',
            'order-type',
            'reserve-time',
            'contact-phone',
            'payment-method',
            'order-remarks'
        ];

        fieldIds.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    formData[id] = element.checked;
                } else if (element.type === 'radio') {
                    if (element.checked) {
                        formData[id] = element.value;
                    }
                } else {
                    formData[id] = element.value;
                }
            }
        });

        return formData;
    }

    /**
     * 智能更新联系方式
     * @param {Object} vehicleInfo - 车辆信息
     * @param {string} pickupAddress - 取车地址
     * @param {HTMLElement} contactPhoneInput - 联系电话输入框
     * @param {HTMLElement} contactPhoneHint - 提示文本元素
     */
    updateContactPhone(vehicleInfo, pickupAddress, contactPhoneInput, contactPhoneHint) {
        try {
            const storeName = F6Utils.getStoreName();

            // 判断取车地点是否为店名
            const isStorePickup = this.isStoreAddress(pickupAddress, storeName);

            if (isStorePickup) {
                // 取车地点是店名，尝试获取店长手机号
                const managerPhone = this.getManagerPhone();
                if (managerPhone) {
                    contactPhoneInput.value = managerPhone;
                    contactPhoneInput.placeholder = '店长手机号';
                    contactPhoneHint.textContent = '该手机号用于司机可以联系到店里';
                } else {
                    // 获取不到店长手机号，让用户自己输入
                    contactPhoneInput.value = '';
                    contactPhoneInput.placeholder = '请提供店长手机号';
                    contactPhoneHint.textContent = '该手机号用于司机可以联系到店里';
                }
            } else {
                // 取车地点是客户地址，使用客户手机号
                const customerPhone = vehicleInfo.customerPhone;
                if (customerPhone && customerPhone !== '暂无' && customerPhone.trim()) {
                    contactPhoneInput.value = customerPhone;
                    contactPhoneInput.placeholder = '客户手机号';
                    contactPhoneHint.textContent = '该手机号为客户手机号，用于司机可以联系到客户';
                } else {
                    contactPhoneInput.value = '';
                    contactPhoneInput.placeholder = '暂无, 请提供客户手机号';
                    contactPhoneHint.textContent = '该手机号用于司机可以联系到客户';
                }
            }
        } catch (error) {
            F6Utils.log('error', '更新联系方式失败', error);
            contactPhoneInput.placeholder = '请输入联系电话';
            contactPhoneHint.textContent = '手机号用于司机联系';
        }
    }

    /**
     * 判断地址是否为当前店面地址
     * @param {string} address - 地址
     * @param {string} storeName - 当前店名
     * @returns {boolean} 是否为当前店面地址
     */
    isStoreAddress(address, storeName) {
        if (!address || !storeName) return false;

        // 严格判断：只有当地址完全等于当前店名或包含当前店名时，才认为是当前店面地址
        // 这样确保联系方式逻辑完全基于是否为当前店名来判断

        // 优先判断：如果地址完全等于当前店名，肯定是当前店面地址
        if (address.trim() === storeName.trim()) return true;

        // 次要判断：如果地址包含当前店名，也认为是当前店面地址
        if (address.includes(storeName)) return true;

        // 不再基于通用关键词判断，避免误判其他店面为当前店面
        // 只有明确包含当前店名的地址才使用店长手机号逻辑
        return false;
    }

    /**
     * 获取店长手机号（简化版本）
     * @returns {string|null} 店长手机号
     */
    getManagerPhone() {
        // 我们不知道哪个手机号才是店长的手机号
        // 所以直接返回null，让用户自己输入
        F6Utils.log('info', '需要用户手动输入店长手机号');
        return null;
    }





    /**
     * 设置店面信息（从F6页面获取真实店名）
     */
    setFixedStoreInfo() {
        // 延迟执行，确保DOM元素已渲染
        setTimeout(() => {
            try {
                // 从F6页面获取真实店名
                const storeName = F6Utils.getStoreName();
                const storeNameInput = document.getElementById('store-name');
                const pickupAddressInput = document.getElementById('pickup-address');

                // 检查元素是否存在
                if (!storeNameInput) {
                    F6Utils.log('error', 'store-name元素不存在');
                    return;
                }

                if (!pickupAddressInput) {
                    F6Utils.log('error', 'pickup-address元素不存在');
                    return;
                }

                if (storeName) {
                    // 成功获取店名（已去掉"您身边的"前缀）
                    storeNameInput.value = storeName;

                    // 将店面名称显示在取车地址文本框中
                    pickupAddressInput.value = storeName;
                    pickupAddressInput.placeholder = `${storeName}（可修改）`;

                    // 初始化联系方式 - 因为取车地址是店名，应该显示店长手机号逻辑
                    const contactPhoneInput = document.getElementById('contact-phone');
                    const contactPhoneHint = document.getElementById('contact-phone-hint');

                    if (contactPhoneInput && contactPhoneHint) {
                        // 获取当前车辆信息
                        const vehicleInfo = window.currentVehicleInfo || {
                            plateNumber: '',
                            customerName: '',
                            customerPhone: '',
                            carModel: ''
                        };

                        // 更新联系方式为店长手机号逻辑
                        this.updateContactPhone(vehicleInfo, storeName, contactPhoneInput, contactPhoneHint);
                        F6Utils.log('info', '订单页面初始化：已设置联系方式为店长手机号逻辑');
                    }

                    //F6Utils.showNotification(`已获取店面信息：${storeName}`, 'success');
                    //F6Utils.log('info', `店面信息设置成功: ${storeName}`);
                } else {
                    // 无法获取店名
                    storeNameInput.value = '';
                    pickupAddressInput.value = '';
                    pickupAddressInput.placeholder = '⚠️ 无法获取店面信息，请手动输入取车地址';
                    pickupAddressInput.style.borderColor = '#f56c6c';

                    F6Utils.showNotification('无法获取店面信息，请刷新页面或联系管理员', 'error');

                    // 尝试延迟重新获取
                    setTimeout(() => {
                        this.retryGetStoreInfo();
                    }, 2000);
                }
            } catch (error) {
                F6Utils.log('error', '设置店面信息时发生错误', error);
            }
        }, 100); // 延迟100ms确保DOM渲染完成
    }

    /**
     * 重试获取店面信息
     */
    retryGetStoreInfo() {
        try {
            const storeName = F6Utils.getStoreName();
            const storeNameInput = document.getElementById('store-name');
            const pickupAddressInput = document.getElementById('pickup-address');

            // 检查元素是否存在
            if (!storeNameInput || !pickupAddressInput) {
                F6Utils.log('warning', '重试时DOM元素不存在，跳过重试');
                return;
            }

            if (storeName) {
                storeNameInput.value = storeName;

                // 将店面名称显示在取车地址文本框中
                pickupAddressInput.value = storeName;
                pickupAddressInput.placeholder = `${storeName}（可修改）`;
                pickupAddressInput.style.borderColor = '#dcdfe6'; // 恢复正常边框色

                // 重试成功后也要更新联系方式
                const contactPhoneInput = document.getElementById('contact-phone');
                const contactPhoneHint = document.getElementById('contact-phone-hint');

                if (contactPhoneInput && contactPhoneHint) {
                    // 获取当前车辆信息
                    const vehicleInfo = window.currentVehicleInfo || {
                        plateNumber: '',
                        customerName: '',
                        customerPhone: '',
                        carModel: ''
                    };

                    // 更新联系方式为店长手机号逻辑
                    this.updateContactPhone(vehicleInfo, storeName, contactPhoneInput, contactPhoneHint);
                    F6Utils.log('info', '重试成功后：已设置联系方式为店长手机号逻辑');
                }

                F6Utils.showNotification(`重试成功，已获取店面信息：${storeName}`, 'success');
                F6Utils.log('info', `重试获取店面信息成功: ${storeName}`);
            } else {
                F6Utils.log('warning', '重试获取店面信息仍然失败');
            }
        } catch (error) {
            F6Utils.log('error', '重试获取店面信息时发生错误', error);
        }
    }

    /**
     * 切换取车地址和送车地址（带动画效果）
     */
    swapAddresses() {
        const pickupContainer = document.getElementById('pickup-address-container');
        const destinationContainer = document.getElementById('destination-address-container');
        const pickupAddressInput = document.getElementById('pickup-address');
        const destinationAddressInput = document.getElementById('destination-address');
        const pickupLongitudeInput = document.getElementById('pickup-longitude');
        const pickupLatitudeInput = document.getElementById('pickup-latitude');
        const destinationLongitudeInput = document.getElementById('destination-longitude');
        const destinationLatitudeInput = document.getElementById('destination-latitude');
        const swapButton = document.getElementById('swap-addresses');

        // 禁用按钮防止重复点击
        swapButton.disabled = true;
        swapButton.style.opacity = '0.6';

        // 添加旋转动画
        swapButton.style.transform = 'rotate(180deg)';

        // 第一阶段：向上滑出
        pickupContainer.style.transform = 'translateY(-20px)';
        pickupContainer.style.opacity = '0.3';
        destinationContainer.style.transform = 'translateY(20px)';
        destinationContainer.style.opacity = '0.3';

        setTimeout(() => {
            // 交换地址值
            const tempAddress = pickupAddressInput.value;
            const tempPlaceholder = pickupAddressInput.placeholder;

            pickupAddressInput.value = destinationAddressInput.value;
            pickupAddressInput.placeholder = destinationAddressInput.placeholder;
            destinationAddressInput.value = tempAddress;
            destinationAddressInput.placeholder = tempPlaceholder;

            // 交换经纬度
            const tempLng = pickupLongitudeInput.value;
            const tempLat = pickupLatitudeInput.value;
            pickupLongitudeInput.value = destinationLongitudeInput.value;
            pickupLatitudeInput.value = destinationLatitudeInput.value;
            destinationLongitudeInput.value = tempLng;
            destinationLatitudeInput.value = tempLat;

            // 第二阶段：滑入到新位置
            pickupContainer.style.transform = 'translateY(0)';
            pickupContainer.style.opacity = '1';
            destinationContainer.style.transform = 'translateY(0)';
            destinationContainer.style.opacity = '1';

            // 恢复按钮状态
            setTimeout(() => {
                swapButton.disabled = false;
                swapButton.style.opacity = '1';
                swapButton.style.transform = 'rotate(0deg)';

                // 切换后更新联系方式
                this.updateContactPhoneAfterSwap();
            }, 150);

        }, 150); // 150ms后执行交换

        F6Utils.showNotification('🔄 已切换取车地址和送车地址', 'success', 3000);
    }

    /**
     * 切换地址后更新联系方式和重新估值
     */
    updateContactPhoneAfterSwap() {
        try {
            // 获取当前表单中的车辆信息
            const vehicleInfo = this.getCurrentVehicleInfo();
            const pickupAddressInput = document.getElementById('pickup-address');
            const contactPhoneInput = document.getElementById('contact-phone');
            const contactPhoneHint = document.getElementById('contact-phone-hint');

            if (pickupAddressInput && contactPhoneInput && contactPhoneHint) {
                // 重新更新联系方式
                this.updateContactPhone(vehicleInfo, pickupAddressInput.value, contactPhoneInput, contactPhoneHint);
                F6Utils.log('info', '切换地址后已更新联系方式');
            }

            // 切换地址后重新估值
            const pickupAddress = pickupAddressInput?.value?.trim();
            const destinationAddressInput = document.getElementById('destination-address');
            const destinationAddress = destinationAddressInput?.value?.trim();

            if (pickupAddress && destinationAddress &&
                pickupAddress.length >= 2 && destinationAddress.length >= 5) {
                F6Utils.log('info', '切换地址后重新估值');
                setTimeout(() => {
                    this.estimateOrderCost();
                }, 300); // 延迟300ms确保UI更新完成
            }
        } catch (error) {
            F6Utils.log('error', '切换地址后更新联系方式失败', error);
        }
    }

    /**
     * 获取当前表单中的车辆信息
     * @returns {Object} 车辆信息
     */
    getCurrentVehicleInfo() {
        // 从表单的车辆信息区域获取数据，或使用全局存储的车辆信息
        if (window.currentVehicleInfo) {
            return window.currentVehicleInfo;
        }

        // 如果没有全局车辆信息，返回默认结构
        return {
            plateNumber: '',
            customerName: '',
            customerPhone: '',
            carModel: ''
        };
    }

    /**
     * 处理订单提交
     * @param {Object} vehicleInfo - 车辆信息
     * @param {Function} onSubmit - 提交回调函数
     */
    handleOrderSubmit(vehicleInfo, onSubmit) {
        const storeName = document.getElementById('store-name').value;
        const pickupAddress = document.getElementById('pickup-address').value.trim();
        const destinationAddress = document.getElementById('destination-address').value.trim();
        const pickupLongitude = document.getElementById('pickup-longitude').value;
        const pickupLatitude = document.getElementById('pickup-latitude').value;
        const destinationLongitude = document.getElementById('destination-longitude').value;
        const destinationLatitude = document.getElementById('destination-latitude').value;
        const orderType = document.getElementById('order-type').value;
        const reserveTime = document.getElementById('reserve-time').value;
        const paymentMethod = document.getElementById('payment-method').value;
        const contactPhone = document.getElementById('contact-phone').value.trim();
        const remarks = document.getElementById('order-remarks').value.trim();

        // 验证必填字段
        if (!storeName) {
            F6Utils.showNotification('店面信息未设置，请刷新页面重试', 'error');
            return;
        }

        if (!pickupAddress) {
            F6Utils.showNotification('请输入取车地址', 'error');
            return;
        }

        if (!destinationAddress) {
            F6Utils.showNotification('请输入送车地址', 'error');
            return;
        }

        if (!contactPhone) {
            F6Utils.showNotification('请输入联系方式', 'error');
            return;
        }

        // 验证手机号格式
        if (!/^1[3-9]\d{9}$/.test(contactPhone)) {
            F6Utils.showNotification('请输入正确的手机号格式', 'error');
            return;
        }

        // 地址定位由中间件处理，不需要前端验证经纬度

        if (orderType === 'reserve' && !reserveTime) {
            F6Utils.showNotification('预约订单请选择预约时间', 'error');
            return;
        }

        // 构造订单数据（使用车辆原有信息，地址定位由中间件处理）
        const orderData = {
            ...vehicleInfo,
            store_name: storeName,
            manager_name: F6Utils.getManagerName(),
            pickup_address: pickupAddress,
            destination_address: destinationAddress,
            contact_phone: contactPhone, // 新增联系方式字段
            order_type: orderType,
            reserve_time: reserveTime,
            payment_method: paymentMethod,
            remarks: remarks
        };

        // 禁用提交按钮
        const submitBtn = document.getElementById('submit-order');
        submitBtn.disabled = true;
        submitBtn.textContent = '创建中...';

        // 调用提交回调
        onSubmit(orderData).then((result) => {
            // 订单提交成功，清除保存的表单状态
            if (result && result.success) {
                this.formStateManager.clearFormState();
                console.log('订单提交成功，已清除表单状态');
            }
        }).finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = '创建订单';
        });
    }

    /**
     * 显示订单详情模态框
     * @param {Array} orders - 订单列表
     * @param {string} customerPhone - 客户手机号
     */
    showOrderDetails(orders, customerPhone) {
        const content = this.createOrderDetailsContent(orders, customerPhone);
        const modal = this.createModal('e代驾订单详情', content);
        this.showModal(modal);
    }

    /**
     * 创建订单详情内容
     * @param {Array} orders - 订单列表
     * @param {string} customerPhone - 客户手机号
     * @returns {HTMLElement}
     */
    createOrderDetailsContent(orders, customerPhone) {
        const container = document.createElement('div');

        if (!orders || orders.length === 0) {
            container.innerHTML = `
                <div class="edriver-empty-state">
                    <div class="edriver-empty-icon">🚗</div>
                    <p>该客户暂无e代驾订单</p>
                    <small style="color: #999;">客户手机号: ${customerPhone}</small>
                </div>
            `;
            return container;
        }

        // 创建订单列表
        const orderList = document.createElement('div');
        orders.forEach(order => {
            const orderItem = this.createOrderItem(order);
            orderList.appendChild(orderItem);
        });

        container.appendChild(orderList);

        // 添加底部信息
        const footer = document.createElement('div');
        footer.style.cssText = 'margin-top: 16px; padding-top: 16px; border-top: 1px solid #eee; text-align: center; color: #999; font-size: 12px;';
        footer.textContent = `共找到 ${orders.length} 个订单 • 客户: ${customerPhone}`;
        container.appendChild(footer);

        return container;
    }

    /**
     * 创建单个订单项
     * @param {Object} order - 订单信息
     * @returns {HTMLElement}
     */
    createOrderItem(order) {
        const item = document.createElement('div');
        item.className = 'edriver-order-item';

        item.innerHTML = `
            <div class="edriver-order-header">
                <div class="edriver-order-id">订单号: ${order.order_id}</div>
                <span class="edriver-status-badge edriver-status-${order.status}">${order.status_display}</span>
            </div>

            <div class="edriver-order-info">
                <div class="edriver-info-item">
                    <span class="edriver-info-icon">📍</span>
                    <span>起点: ${order.pickup_address}</span>
                </div>
                <div class="edriver-info-item">
                    <span class="edriver-info-icon">🏁</span>
                    <span>终点: ${order.destination_address}</span>
                </div>
                <div class="edriver-info-item">
                    <span class="edriver-info-icon">👨‍💼</span>
                    <span>司机: ${order.driver_name}</span>
                </div>
                <div class="edriver-info-item">
                    <span class="edriver-info-icon">📞</span>
                    <span>电话: ${order.driver_phone || '暂无'}</span>
                </div>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 8px;">
                <div class="edriver-price">${F6Utils.formatPrice(order.price)}</div>
                <div class="edriver-time">${F6Utils.formatDateTime(order.created_time)}</div>
            </div>
        `;

        return item;
    }

    /**
     * 创建模态框
     * @param {string} title - 标题
     * @param {HTMLElement} content - 内容
     * @returns {HTMLElement}
     */
    createModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'edriver-modal';

        const modalContent = document.createElement('div');
        modalContent.className = 'edriver-modal-content';

        const header = document.createElement('div');
        header.className = 'edriver-modal-header';
        header.innerHTML = `
            <h5 class="edriver-modal-title">${title}</h5>
            <button class="edriver-modal-close">&times;</button>
        `;

        const body = document.createElement('div');
        body.className = 'edriver-modal-body';
        body.appendChild(content);

        modalContent.appendChild(header);
        modalContent.appendChild(body);
        modal.appendChild(modalContent);

        // 添加关闭事件
        header.querySelector('.edriver-modal-close').addEventListener('click', () => {
            this.closeModal();
        });

        // 只在特定情况下允许点击外部关闭模态框
        modal.addEventListener('click', (e) => {
            // 只有当点击的是模态框背景（不是内容区域）且不是表单模态框时才关闭
            if (e.target === modal && !this.isFormModal(modal)) {
                this.closeModal();
            }
        });

        return modal;
    }

    /**
     * 显示模态框
     * @param {HTMLElement} modal - 模态框元素
     */
    showModal(modal) {
        this.closeModal(); // 关闭之前的模态框
        this.currentModal = modal;
        document.body.appendChild(modal);

        // 防止页面滚动
        document.body.style.overflow = 'hidden';
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        if (this.currentModal) {
            this.currentModal.remove();
            this.currentModal = null;
            document.body.style.overflow = '';
        }
    }

    /**
     * 判断是否是表单模态框（不应该点击外部关闭）
     * @param {HTMLElement} modal - 模态框元素
     * @returns {boolean}
     */
    isFormModal(modal) {
        if (!modal) return false;

        // 检查模态框标题，如果包含"创建"、"订单"、"恢复"等关键词，则认为是表单模态框
        const titleElement = modal.querySelector('.edriver-modal-title');
        if (titleElement) {
            const title = titleElement.textContent.toLowerCase();
            const formKeywords = ['创建', '订单', '恢复', 'form', 'create', 'order'];
            if (formKeywords.some(keyword => title.includes(keyword))) {
                return true;
            }
        }

        // 检查是否包含表单元素
        const hasForm = modal.querySelector('form, input, textarea, select');
        if (hasForm) {
            return true;
        }

        // 检查是否包含提交按钮
        const hasSubmitButton = modal.querySelector('#submit-order, #confirm-new-order');
        if (hasSubmitButton) {
            return true;
        }

        return false;
    }

    /**
     * 显示加载状态
     * @param {string} message - 加载消息
     * @returns {HTMLElement}
     */
    createLoadingContent(message = '加载中...') {
        const loading = document.createElement('div');
        loading.className = 'edriver-loading';
        loading.innerHTML = `
            <div class="edriver-spinner"></div>
            <span>${message}</span>
        `;
        return loading;
    }

    /**
     * 创建重复订单内容
     * @param {string} message - 重复订单消息
     * @param {Object} orderInfo - 重复订单信息
     * @returns {HTMLElement}
     */
    createDuplicateOrderContent(message, orderInfo) {
        const container = document.createElement('div');
        container.style.cssText = 'font-family: "PingFang SC", "Microsoft YaHei", sans-serif;';

        container.innerHTML = `
            <div class="edriver-duplicate-order">
                <!-- 顶部状态区域 -->
                <div style="text-align: center; margin-bottom: 24px;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px; box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);">
                        <span style="font-size: 36px;">🚗</span>
                    </div>
                    <h3 style="color: #dc2626; margin: 0 0 8px 0; font-size: 20px; font-weight: 600;">车辆正在使用中</h3>
                    <p style="color: #6b7280; margin: 0; font-size: 14px;">检测到该客户有进行中的订单</p>
                </div>

                <!-- 现有订单信息卡片 -->
                <div style="background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%); padding: 20px; border-radius: 12px; border: 1px solid #fca5a5; margin-bottom: 24px; box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);">
                    <!-- 订单头部 -->
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px; padding-bottom: 12px; border-bottom: 1px solid rgba(220, 38, 38, 0.2);">
                        <div style="display: flex; align-items: center;">
                            <div style="width: 32px; height: 32px; background: #dc2626; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                <span style="color: white; font-size: 14px; font-weight: bold;">📋</span>
                            </div>
                            <div>
                                <div style="color: #dc2626; font-weight: 600; font-size: 14px;">进行中订单</div>
                                <div style="color: #7f1d1d; font-size: 12px;">${orderInfo.order_id || '未知订单号'}</div>
                            </div>
                        </div>
                        <div style="background: #dc2626; color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 500;">
                            ${orderInfo.status_display || '进行中'}
                        </div>
                    </div>

                    <!-- 客户和车辆信息 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px;">
                        <div style="background: rgba(255,255,255,0.8); padding: 12px; border-radius: 8px;">
                            <div style="color: #7f1d1d; font-size: 11px; margin-bottom: 4px; text-transform: uppercase; font-weight: 500;">👤 客户信息</div>
                            <div style="color: #dc2626; font-weight: 600; font-size: 14px; margin-bottom: 2px;">${orderInfo.customer_name || '未知客户'}</div>
                            <div style="color: #991b1b; font-size: 12px;">${orderInfo.plate_number || '未知车牌'}</div>
                        </div>
                        <div style="background: rgba(255,255,255,0.8); padding: 12px; border-radius: 8px;">
                            <div style="color: #7f1d1d; font-size: 11px; margin-bottom: 4px; text-transform: uppercase; font-weight: 500;">🕐 时间信息</div>
                            <div style="color: #dc2626; font-weight: 600; font-size: 14px; margin-bottom: 2px;">创建时间</div>
                            <div style="color: #991b1b; font-size: 12px;">${orderInfo.created_time || '未知时间'}</div>
                        </div>
                    </div>

                    <!-- 路线信息 -->
                    <div style="background: rgba(255,255,255,0.8); padding: 16px; border-radius: 8px;">
                        <div style="color: #7f1d1d; font-size: 11px; margin-bottom: 12px; text-transform: uppercase; font-weight: 500;">🛣️ 订单路线</div>
                        <div style="position: relative;">
                            <!-- 起点 -->
                            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                <div style="width: 12px; height: 12px; background: #10b981; border-radius: 50%; margin-right: 12px; border: 2px solid white; box-shadow: 0 0 0 2px #10b981;"></div>
                                <div>
                                    <div style="color: #059669; font-size: 11px; font-weight: 500;">取车地点</div>
                                    <div style="color: #dc2626; font-size: 13px; font-weight: 500;">${orderInfo.pickup_address || '未知地址'}</div>
                                </div>
                            </div>
                            <!-- 连接线 -->
                            <div style="position: absolute; left: 5px; top: 12px; width: 2px; height: 20px; background: linear-gradient(to bottom, #10b981, #dc2626); border-radius: 1px;"></div>
                            <!-- 终点 -->
                            <div style="display: flex; align-items: center;">
                                <div style="width: 12px; height: 12px; background: #dc2626; border-radius: 50%; margin-right: 12px; border: 2px solid white; box-shadow: 0 0 0 2px #dc2626;"></div>
                                <div>
                                    <div style="color: #dc2626; font-size: 11px; font-weight: 500;">送车地点</div>
                                    <div style="color: #dc2626; font-size: 13px; font-weight: 500;">${orderInfo.destination_address || '未知地址'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作员信息 -->
                    <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid rgba(220, 38, 38, 0.2); text-align: center;">
                        <span style="color: #7f1d1d; font-size: 12px;">👨‍💼 操作员：${orderInfo.manager_name || '未知操作员'}</span>
                    </div>
                </div>

                <!-- 操作建议 -->
                <div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #c7d2fe; margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 16px; margin-right: 8px;">💡</span>
                        <span style="color: #3730a3; font-weight: 600; font-size: 14px;">建议操作</span>
                    </div>
                    <ul style="margin: 0; padding-left: 20px; color: #4338ca; font-size: 13px; line-height: 1.6;">
                        <li>等待当前订单完成后再下新单</li>
                        <li>如果要下预约订单，请修改“订单类型”</li>

                    </ul>
                </div>

                <!-- 操作按钮 -->
                <div style="text-align: center;">
                    <button type="button" id="close-duplicate-modal" class="edriver-btn"
                            style="background: #6b7280; border-color: #6b7280; padding: 12px 24px; border-radius: 8px; font-weight: 500; display: inline-flex; align-items: center; gap: 6px;">
                        <span>✅</span>
                        <span>我知道了</span>
                    </button>
                </div>
            </div>
        `;

        // 添加事件监听
        setTimeout(() => {
            const closeButton = container.querySelector('#close-duplicate-modal');

            if (closeButton) {
                closeButton.addEventListener('click', () => {
                    this.closeModal();
                });
            }
        }, 100);

        return container;
    }

    /**
     * 创建历史订单确认内容
     * @param {string} message - 确认消息
     * @param {Object} orderInfo - 历史订单信息
     * @param {boolean} isSameRoute - 是否为相同路线
     * @param {Object} newOrderData - 新订单数据
     * @param {string} conflictType - 冲突类型
     * @returns {HTMLElement}
     */
    createHistoricalOrderContent(message, orderInfo, isSameRoute, newOrderData, conflictType = 'historical_order') {
        const container = document.createElement('div');
        container.style.cssText = 'font-family: "PingFang SC", "Microsoft YaHei", sans-serif;';

        const routeIcon = isSameRoute ? '🔄' : '🆕';
        const routeText = isSameRoute ? '相同路线' : '不同路线';

        container.innerHTML = `
            <div class="edriver-historical-order">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 48px; color: #409eff; margin-bottom: 10px;">${routeIcon}</div>
                    <h3 style="color: #409eff; margin: 0; font-size: 18px;">发现历史订单 (${routeText})</h3>
                </div>

                <div style="background: #f0f9ff; padding: 15px; border-radius: 6px; border: 1px solid #409eff; margin-bottom: 20px;">
                    <p style="margin: 0; color: #1d4ed8; font-size: 14px; line-height: 1.5;">${message}</p>
                </div>

                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; border: 1px solid #e4e7ed; margin-bottom: 15px;">
                    <h4 style="margin: 0 0 12px 0; color: #303133; font-size: 14px;">📋 历史订单信息</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 13px;">
                        <div><strong>订单号:</strong> ${orderInfo.order_id || '未知订单号'}</div>
                        <div><strong>状态:</strong> <span style="color: #67c23a;">${orderInfo.status_display || '未知状态'}</span></div>
                        <div><strong>客户:</strong> ${orderInfo.customer_name || '未知客户'}</div>
                        <div><strong>车牌:</strong> ${orderInfo.plate_number || '未知车牌'}</div>
                        <div style="grid-column: 1 / -1;"><strong>取车地址:</strong> ${orderInfo.pickup_address || '未知地址'}</div>
                        <div style="grid-column: 1 / -1;"><strong>送车地址:</strong> ${orderInfo.destination_address || '未知地址'}</div>
                        <div><strong>创建时间:</strong> ${orderInfo.created_time || '未知时间'}</div>
                        <div><strong>操作员:</strong> ${orderInfo.manager_name || '未知操作员'}</div>
                    </div>
                </div>

                ${isSameRoute ? `
                <div style="background: #fff3cd; padding: 12px; border-radius: 6px; border: 1px solid #ffeaa7; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; color: #b25d12; font-size: 13px;">
                        <span style="margin-right: 8px;">⚠️</span>
                        <span>检测到相同路线的历史订单，请确认是否需要重复下单</span>
                    </div>
                </div>
                ` : `
                <div style="background: #f0f9ff; padding: 12px; border-radius: 6px; border: 1px solid #409eff; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; color: #1d4ed8; font-size: 13px;">
                        <span style="margin-right: 8px;">ℹ️</span>
                        <span>该客户曾下过订单，但路线不同，可以正常下单</span>
                    </div>
                </div>
                `}

                <div style="margin-top: 20px; text-align: center;">
                    <button type="button" id="confirm-new-order" class="edriver-btn"
                            style="background: #67c23a; border-color: #67c23a; margin-right: 10px;">
                        确认下单
                    </button>
                    <button type="button" id="view-historical-order" class="edriver-btn"
                            style="background: #409eff; border-color: #409eff; margin-right: 10px;">
                        查看历史订单
                    </button>
                    <button type="button" id="cancel-new-order" class="edriver-btn"
                            style="background: #909399; border-color: #909399;">
                        取消下单
                    </button>
                </div>

                <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e4e7ed; text-align: center;">
                    <small style="color: #909399; font-size: 12px;">
                        💡 确认下单将创建新的e代驾订单
                    </small>
                </div>
            </div>
        `;

        // 添加事件监听
        setTimeout(() => {
            const confirmButton = container.querySelector('#confirm-new-order');
            const viewButton = container.querySelector('#view-historical-order');
            const cancelButton = container.querySelector('#cancel-new-order');

            if (confirmButton) {
                confirmButton.addEventListener('click', async () => {
                    try {
                        confirmButton.disabled = true;
                        confirmButton.textContent = '创建中...';

                        // 调用确认订单API
                        const response = await window.F6EdriverAPI.createOrderConfirmed(newOrderData);

                        if (response.success) {
                            F6Utils.showNotification(`e代驾订单创建成功！订单号: ${response.order_id}`, 'success', 5000);
                            this.closeModal();
                        } else {
                            throw new Error(response.message || '确认订单创建失败');
                        }
                    } catch (error) {
                        F6Utils.log('error', '确认订单创建失败', error);
                        F6Utils.showNotification(`确认订单创建失败: ${error.message}`, 'error', 5000);
                        confirmButton.disabled = false;
                        confirmButton.textContent = '确认下单';
                    }
                });
            }

            if (viewButton) {
                viewButton.addEventListener('click', () => {
                    F6Utils.showNotification(`📋 历史订单详情\n\n订单号: ${orderInfo.order_id}\n状态: ${orderInfo.status_display}\n创建时间: ${orderInfo.created_time}\n客户: ${orderInfo.customer_name}\n车牌: ${orderInfo.plate_number}`, 'info', 10000);
                });
            }

            if (cancelButton) {
                cancelButton.addEventListener('click', () => {
                    this.closeModal();
                });
            }
        }, 100);

        return container;
    }

    /**
     * 显示修改目的地弹窗（新版：先建议地址，再估值，最后确认）
     * @param {Object} orderInfo - 当前订单信息
     */
    // showModifyDestinationDialog 已移除，恢复为原有设计
}

// 创建全局UI实例
window.F6EdriverUI = new F6EdriverUI();
