/**
 * F6 e代驾助手 - 状态轮询管理器
 *
 * @description 管理订单状态的自动轮询和更新，支持多平台扩展
 * @version 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2025 Yu <PERSON>. All rights reserved.
 * @license MIT License
 */

class F6StatusPoller {
    constructor() {
        this.pollingInterval = 30000; // 30秒轮询间隔
        this.activePollers = new Map(); // 活跃的轮询器
        this.statusCallbacks = new Map(); // 状态变更回调
        this.isPollingEnabled = true;
        this.maxRetries = 3;
        this.retryDelay = 5000; // 重试延迟5秒
    }

    /**
     * 开始轮询客户订单状态
     * @param {Object} customerInfo - 客户信息
     * @param {string} managerName - 店长姓名
     * @param {string} platform - 平台名称
     * @param {Function} onStatusUpdate - 状态更新回调
     */
    startPolling(customerInfo, managerName, platform = 'edaijia', onStatusUpdate = null) {
        const pollerId = this.generatePollerId(customerInfo, platform);
        
        // 如果已经在轮询，先停止
        if (this.activePollers.has(pollerId)) {
            this.stopPolling(pollerId);
        }

        F6Utils.log('info', `开始轮询客户订单状态: ${customerInfo.customerName}`, {
            platform,
            pollerId,
            interval: this.pollingInterval
        });

        // 注册状态更新回调
        if (onStatusUpdate) {
            this.statusCallbacks.set(pollerId, onStatusUpdate);
        }

        // 立即执行一次查询
        this.pollCustomerOrders(customerInfo, managerName, platform, pollerId);

        // 设置定时轮询
        const intervalId = setInterval(() => {
            if (this.isPollingEnabled) {
                this.pollCustomerOrders(customerInfo, managerName, platform, pollerId);
            }
        }, this.pollingInterval);

        // 记录轮询器
        this.activePollers.set(pollerId, {
            intervalId,
            customerInfo,
            managerName,
            platform,
            startTime: Date.now(),
            lastPollTime: null,
            pollCount: 0,
            errorCount: 0
        });
    }

    /**
     * 停止轮询
     * @param {string} pollerId - 轮询器ID
     */
    stopPolling(pollerId) {
        const poller = this.activePollers.get(pollerId);
        
        if (poller) {
            clearInterval(poller.intervalId);
            this.activePollers.delete(pollerId);
            this.statusCallbacks.delete(pollerId);
            
            F6Utils.log('info', `停止轮询: ${pollerId}`, {
                duration: Date.now() - poller.startTime,
                pollCount: poller.pollCount,
                errorCount: poller.errorCount
            });
        }
    }

    /**
     * 停止所有轮询
     */
    stopAllPolling() {
        const pollerIds = Array.from(this.activePollers.keys());
        pollerIds.forEach(pollerId => this.stopPolling(pollerId));
        
        F6Utils.log('info', `停止所有轮询: ${pollerIds.length} 个`);
    }

    /**
     * 生成轮询器ID
     * @param {Object} customerInfo - 客户信息
     * @param {string} platform - 平台名称
     * @returns {string}
     */
    generatePollerId(customerInfo, platform) {
        const key = `${customerInfo.customerName}_${customerInfo.customerPhone}_${customerInfo.plateNumber}_${platform}`;
        return btoa(encodeURIComponent(key));
    }

    /**
     * 轮询客户订单
     * @param {Object} customerInfo - 客户信息
     * @param {string} managerName - 店长姓名
     * @param {string} platform - 平台名称
     * @param {string} pollerId - 轮询器ID
     */
    async pollCustomerOrders(customerInfo, managerName, platform, pollerId) {
        const poller = this.activePollers.get(pollerId);
        if (!poller) return;

        try {
            poller.lastPollTime = Date.now();
            poller.pollCount++;

            F6Utils.log('debug', `轮询客户订单: ${customerInfo.customerName}`, {
                platform,
                pollCount: poller.pollCount
            });

            // 获取缓存的订单
            const cachedData = window.F6OrderCache.getCustomerOrders(customerInfo, platform);
            
            // 查询最新订单状态
            const response = await window.F6EdriverAPI.getCustomerOrders(customerInfo, managerName, platform);
            
            if (response.success && response.orders) {
                // 合并新订单到缓存
                const mergedOrders = window.F6OrderCache.mergeNewOrders(customerInfo, response.orders, platform);
                
                // 检查状态变更
                this.checkStatusChanges(cachedData ? cachedData.orders : [], response.orders, platform, pollerId);
                
                // 更新页面显示
                this.updatePageDisplay(customerInfo, mergedOrders, platform);
                
                // 重置错误计数
                poller.errorCount = 0;
            }
        } catch (error) {
            poller.errorCount++;
            
            F6Utils.log('warn', `轮询订单失败 (${poller.errorCount}/${this.maxRetries})`, {
                pollerId,
                error: error.message
            });

            // 如果错误次数过多，停止轮询
            if (poller.errorCount >= this.maxRetries) {
                F6Utils.log('error', `轮询错误次数过多，停止轮询: ${pollerId}`);
                this.stopPolling(pollerId);
            }
        }
    }

    /**
     * 检查状态变更
     * @param {Array} oldOrders - 旧订单列表
     * @param {Array} newOrders - 新订单列表
     * @param {string} platform - 平台名称
     * @param {string} pollerId - 轮询器ID
     */
    checkStatusChanges(oldOrders, newOrders, platform, pollerId) {
        const oldOrderMap = new Map();
        oldOrders.forEach(order => {
            oldOrderMap.set(order.order_id || order.platform_order_id, order);
        });

        const statusChanges = [];

        newOrders.forEach(newOrder => {
            const orderId = newOrder.order_id || newOrder.platform_order_id;
            const oldOrder = oldOrderMap.get(orderId);

            if (oldOrder && oldOrder.status !== newOrder.status) {
                statusChanges.push({
                    orderId,
                    oldStatus: oldOrder.status,
                    newStatus: newOrder.status,
                    order: newOrder
                });
            }
        });

        // 处理状态变更
        if (statusChanges.length > 0) {
            F6Utils.log('info', `检测到状态变更: ${statusChanges.length} 个订单`, statusChanges);
            
            statusChanges.forEach(change => {
                this.handleStatusChange(change, platform, pollerId);
            });
        }
    }

    /**
     * 处理状态变更
     * @param {Object} change - 状态变更信息
     * @param {string} platform - 平台名称
     * @param {string} pollerId - 轮询器ID
     */
    handleStatusChange(change, platform, pollerId) {
        // 缓存新状态
        window.F6OrderCache.setOrderStatus(change.orderId, change.order, platform);

        // 显示通知
        this.showStatusNotification(change, platform);

        // 调用回调函数
        const callback = this.statusCallbacks.get(pollerId);
        if (callback) {
            try {
                callback(change, platform);
            } catch (error) {
                F6Utils.log('error', '状态变更回调执行失败', error);
            }
        }

        // 更新页面状态图标
        this.updateStatusIcon(change.orderId, change.newStatus, platform);
    }

    /**
     * 显示状态变更通知
     * @param {Object} change - 状态变更信息
     * @param {string} platform - 平台名称
     */
    showStatusNotification(change, platform) {
        const statusText = this.getStatusText(change.newStatus, platform);
        const platformText = this.getPlatformText(platform);
        
        const message = `${platformText}订单状态更新：${statusText}`;
        
        F6Utils.showNotification(message, 'info', 8000); // 显示8秒
        
        // 如果支持浏览器通知，也发送桌面通知
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('F6 e代驾订单状态更新', {
                body: message,
                icon: chrome.runtime.getURL('icons/icon48.png'),
                tag: `order_${change.orderId}` // 避免重复通知
            });
        }
    }

    /**
     * 更新页面显示
     * @param {Object} customerInfo - 客户信息
     * @param {Array} orders - 订单列表
     * @param {string} platform - 平台名称
     */
    updatePageDisplay(customerInfo, orders, platform) {
        // 查找客户行并更新状态图标
        const customerRows = document.querySelectorAll('.el-table__row, tr');
        
        customerRows.forEach(row => {
            const rowText = row.textContent || '';
            
            // 检查是否是目标客户行
            if (rowText.includes(customerInfo.customerName) && 
                (customerInfo.customerPhone ? rowText.includes(customerInfo.customerPhone) : true) &&
                (customerInfo.plateNumber ? rowText.includes(customerInfo.plateNumber) : true)) {
                
                this.updateRowStatusIcon(row, orders, platform);
            }
        });
    }

    /**
     * 更新行状态图标
     * @param {HTMLElement} row - 表格行
     * @param {Array} orders - 订单列表
     * @param {string} platform - 平台名称
     */
    updateRowStatusIcon(row, orders, platform) {
        // 移除旧的状态图标
        const oldIcon = row.querySelector('.edriver-status-icon');
        if (oldIcon) {
            oldIcon.remove();
        }

        // 获取最新的活跃订单 - 基于e代驾状态码
        const activeOrder = orders.find(order =>
            ['102', '180', '301', '302', '303'].includes(order.edj_status_code || order.status)
        );

        if (activeOrder) {
            const statusIcon = this.createStatusIcon(activeOrder.edj_status_code || activeOrder.status, platform);
            
            // 插入到客户名称后面
            const nameCell = row.querySelector('td:nth-child(2)') || row.querySelector('td:first-child');
            if (nameCell) {
                nameCell.appendChild(statusIcon);
            }
        }
    }

    /**
     * 创建状态图标
     * @param {string} status - 订单状态
     * @param {string} platform - 平台名称
     * @returns {HTMLElement}
     */
    createStatusIcon(status, platform) {
        const icon = document.createElement('span');
        icon.className = 'edriver-status-icon';
        icon.style.cssText = `
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            color: white;
            display: inline-block;
        `;

        const statusConfig = this.getStatusConfig(status, platform);
        icon.textContent = statusConfig.text;
        icon.style.backgroundColor = statusConfig.color;
        icon.title = `${this.getPlatformText(platform)}: ${statusConfig.text}`;

        return icon;
    }

    /**
     * 获取状态配置
     * @param {string} status - 状态
     * @param {string} platform - 平台
     * @returns {Object}
     */
    getStatusConfig(status, platform) {
        const configs = {
            'pending': { text: '待接单', color: '#f39c12' },
            'accepted': { text: '已接单', color: '#3498db' },
            'in_progress': { text: '进行中', color: '#e74c3c' },
            'completed': { text: '已完成', color: '#27ae60' },
            'cancelled': { text: '已取消', color: '#95a5a6' }
        };

        return configs[status] || { text: '未知', color: '#95a5a6' };
    }

    /**
     * 获取状态文本
     * @param {string} status - 状态
     * @param {string} platform - 平台
     * @returns {string}
     */
    getStatusText(status, platform) {
        return this.getStatusConfig(status, platform).text;
    }

    /**
     * 获取平台文本
     * @param {string} platform - 平台
     * @returns {string}
     */
    getPlatformText(platform) {
        const platforms = {
            'edaijia': 'e代驾',
            'dada': '达达代驾',
            'pingan': '平安代驾'
        };
        return platforms[platform] || platform;
    }

    /**
     * 更新状态图标
     * @param {string} orderId - 订单ID
     * @param {string} status - 新状态
     * @param {string} platform - 平台
     */
    updateStatusIcon(orderId, status, platform) {
        // 查找并更新对应的状态图标
        const statusIcons = document.querySelectorAll('.edriver-status-icon');
        
        statusIcons.forEach(icon => {
            if (icon.dataset.orderId === orderId) {
                const statusConfig = this.getStatusConfig(status, platform);
                icon.textContent = statusConfig.text;
                icon.style.backgroundColor = statusConfig.color;
                icon.title = `${this.getPlatformText(platform)}: ${statusConfig.text}`;
            }
        });
    }

    /**
     * 获取轮询统计信息
     * @returns {Object}
     */
    getPollingStats() {
        const stats = {
            activePollers: this.activePollers.size,
            totalPollCount: 0,
            totalErrorCount: 0,
            pollers: []
        };

        this.activePollers.forEach((poller, pollerId) => {
            stats.totalPollCount += poller.pollCount;
            stats.totalErrorCount += poller.errorCount;
            
            stats.pollers.push({
                pollerId,
                customerName: poller.customerInfo.customerName,
                platform: poller.platform,
                duration: Date.now() - poller.startTime,
                pollCount: poller.pollCount,
                errorCount: poller.errorCount,
                lastPollTime: poller.lastPollTime
            });
        });

        return stats;
    }

    /**
     * 启用/禁用轮询
     * @param {boolean} enabled - 是否启用
     */
    setPollingEnabled(enabled) {
        this.isPollingEnabled = enabled;
        F6Utils.log('info', `轮询${enabled ? '启用' : '禁用'}`);
    }
}

// 创建全局实例
window.F6StatusPoller = new F6StatusPoller();

// 页面卸载时停止所有轮询
window.addEventListener('beforeunload', () => {
    window.F6StatusPoller.stopAllPolling();
});
