/**
 * F6 e代驾助手 - 弹出窗口脚本（简化版）
 *
 * @description F6智能门店系统的e代驾订单管理插件 - 弹出窗口
 * @version 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2025 Yu Zhou. All rights reserved.
 * @license MIT License
 */

class PopupManager {
    constructor() {
        this.currentTab = 'dashboard';
        this.isPopupPinned = false;
        console.log('PopupManager 构造函数开始');
        this.init();
    }

    async init() {
        try {
            console.log('开始初始化popup...');

            // 设置popup保持打开
            this.setupPopupPersistence();

            // 绑定事件
            this.bindEvents();
            console.log('事件绑定完成');

            // 检查状态
            await this.checkStatus();
            console.log('状态检查完成');

            // 启动自动刷新
            this.startAutoRefresh();
            console.log('自动刷新已启动');

            console.log('弹窗初始化完成');
        } catch (error) {
            console.error('弹窗初始化失败:', error);
            // 设置错误状态
            this.setErrorStatus();
        }
    }

    /**
     * 设置popup保持打开的逻辑
     */
    setupPopupPersistence() {
        console.log('设置popup保持打开逻辑...');

        // 设置popup为固定状态
        this.isPopupPinned = true;

        // 使用更强的方法阻止popup关闭
        this.preventPopupClose();

        // ESC键关闭popup
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                console.log('用户按下ESC键，关闭popup');
                this.closePopup();
            }
        });

        // 添加关闭按钮
        this.addCloseButton();

        console.log('Popup保持打开逻辑已设置完成');
    }

    /**
     * 阻止popup关闭的核心逻辑
     */
    preventPopupClose() {
        // 只阻止焦点相关事件，不阻止click事件
        const events = ['blur', 'focusout'];

        events.forEach(eventType => {
            window.addEventListener(eventType, (e) => {
                if (this.isPopupPinned) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
            }, { capture: true, passive: false });
        });

        // 特殊处理blur事件 - 重新获取焦点
        window.addEventListener('blur', () => {
            if (this.isPopupPinned) {
                console.log('检测到失去焦点，重新获取焦点');
                setTimeout(() => {
                    if (this.isPopupPinned && !document.hasFocus()) {
                        window.focus();
                    }
                }, 50);
            }
        });

        // 阻止页面卸载
        window.addEventListener('beforeunload', (e) => {
            if (this.isPopupPinned) {
                e.preventDefault();
                e.returnValue = '';
                return '';
            }
        });

        console.log('Popup关闭阻止机制已激活');
    }

    /**
     * 关闭popup
     */
    closePopup() {
        this.isPopupPinned = false;

        // 清理所有资源
        this.cleanup();

        window.close();
    }

    /**
     * 在popup中显示通知
     */
    showPopupNotification(message, type = 'info', duration = 3000) {
        // 移除现有通知
        const existingNotification = document.getElementById('popup-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.id = 'popup-notification';
        notification.style.cssText = `
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#F44336' : type === 'warning' ? '#FF9800' : '#2196F3'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 2147483648;
            box-shadow: 0 4px 16px rgba(0,0,0,0.3);
            animation: slideInDown 0.3s ease-out;
            max-width: 280px;
            text-align: center;
            line-height: 1.4;
            white-space: pre-wrap;
        `;
        notification.textContent = message;

        // 添加CSS动画
        if (!document.getElementById('popup-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'popup-notification-styles';
            style.textContent = `
                @keyframes slideInDown {
                    from {
                        opacity: 0;
                        transform: translateX(-50%) translateY(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                }
                @keyframes slideOutUp {
                    from {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                    to {
                        opacity: 0;
                        transform: translateX(-50%) translateY(-20px);
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideOutUp 0.3s ease-in';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);
        }

        return notification;
    }

    /**
     * 添加关闭按钮
     */
    addCloseButton() {
        const closeButton = document.createElement('button');
        closeButton.innerHTML = '✕';
        closeButton.id = 'popup-close-button';
        closeButton.style.cssText = `
            position: absolute;
            top: 8px;
            right: 8px;
            background: #f56c6c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            cursor: pointer;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        `;
        closeButton.title = '关闭popup (或按ESC键)';

        // 鼠标悬停效果
        closeButton.addEventListener('mouseenter', () => {
            closeButton.style.backgroundColor = '#e54545';
        });

        closeButton.addEventListener('mouseleave', () => {
            closeButton.style.backgroundColor = '#f56c6c';
        });

        closeButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            console.log('用户点击关闭按钮');
            this.closePopup();
        }, { capture: true });

        document.body.appendChild(closeButton);
        console.log('关闭按钮已添加');
    }

    /**
     * 设置错误状态
     */
    setErrorStatus() {
        this.updateStatus('plugin-status', '初始化失败', 'error');
        this.updateStatus('api-status', '初始化失败', 'error');
        this.updateStatus('page-status', '初始化失败', 'error');
    }

    /**
     * 更新状态显示
     */
    updateStatus(elementId, text, type) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
            element.className = `status-value status-${type}`;
        } else {
            console.warn(`状态元素未找到: ${elementId}`);
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 绑定标签页切换
        this.initTabs();

        // 绑定按钮事件
        this.bindButtonEvents();
    }

    /**
     * 初始化标签页
     */
    initTabs() {
        const tabItems = document.querySelectorAll('.tab-item');
        console.log(`找到 ${tabItems.length} 个标签页`);

        tabItems.forEach(item => {
            item.addEventListener('click', () => {
                const tabId = item.dataset.tab;
                console.log(`切换到标签页: ${tabId}`);
                this.switchTab(tabId);
            });
        });
    }

    /**
     * 绑定按钮事件
     */
    bindButtonEvents() {
        console.log('开始绑定按钮事件...');

        // 重载插件按钮
        const reloadBtn = document.getElementById('reload-plugin');
        if (reloadBtn) {
            console.log('找到重载插件按钮，绑定事件');
            reloadBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('重载插件按钮被点击');
                this.reloadPlugin();
            }, { capture: true });
        } else {
            console.warn('未找到重载插件按钮');
        }

        // 刷新状态按钮
        const refreshBtn = document.getElementById('refresh-status');
        if (refreshBtn) {
            console.log('找到刷新状态按钮，绑定事件');
            refreshBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('刷新状态按钮被点击');
                this.refreshStatus();
            }, { capture: true });
        } else {
            console.warn('未找到刷新状态按钮');
        }

        // 刷新订单按钮
        const refreshOrdersBtn = document.getElementById('refresh-orders');
        if (refreshOrdersBtn) {
            console.log('找到刷新订单按钮，绑定事件');
            refreshOrdersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('刷新订单按钮被点击');
                this.loadActiveOrders();
            }, { capture: true });
        }



        // 自动监控开关
        const autoMonitorSwitch = document.getElementById('auto-monitor');
        if (autoMonitorSwitch) {
            console.log('找到自动监控开关，绑定事件');
            autoMonitorSwitch.addEventListener('change', (e) => {
                console.log('自动监控开关状态:', e.target.checked);
                if (e.target.checked) {
                    // 如果当前在订单监控标签页，重新启动监控
                    if (this.currentTab === 'orders') {
                        this.loadActiveOrders();
                    }
                } else {
                    // 停止监控
                    this.stopOrderMonitoring();
                }
            });
        } else {
            console.warn('未找到自动监控开关');
        }

        console.log('按钮事件绑定完成');
    }



    /**
     * 切换标签页
     */
    switchTab(tabId) {
        console.log(`切换到标签页: ${tabId}`);

        // 移除所有标签的活跃状态
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.classList.remove('active');
            tab.style.borderBottom = '2px solid transparent';
            tab.style.color = '#606266';
        });

        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
            content.classList.add('hidden');
        });

        // 激活目标标签页
        const targetTab = document.querySelector(`[data-tab="${tabId}"]`);
        const targetContent = document.getElementById(tabId);

        if (targetTab) {
            targetTab.classList.add('active');
            targetTab.style.borderBottom = '2px solid #ff6600';
            targetTab.style.color = '#ff6600';
        }

        if (targetContent) {
            targetContent.classList.add('active');
            targetContent.classList.remove('hidden');
        }

        this.currentTab = tabId;
        console.log(`已切换到标签页: ${tabId}`);

        // 如果切换到订单监控标签，加载活跃订单
        if (tabId === 'orders') {
            console.log('切换到订单监控页面，开始加载订单数据');
            this.loadActiveOrders();
        }

        console.log(`✅ 成功切换到标签页: ${tabId}`);
    }

    /**
     * 检查状态
     */
    async checkStatus() {
        try {
            console.log('开始检查状态...');

            // 检查插件状态
            this.updateStatus('plugin-status', '正常', 'success');

            // 检查当前页面
            const tabs = await this.getCurrentTab();
            let isF6Page = false;

            if (tabs && tabs.length > 0) {
                const tab = tabs[0];
                if (tab.url && (tab.url.includes('f6car.com') || tab.url.includes('f6yc.com'))) {
                    this.updateStatus('page-status', 'F6系统', 'success');
                    isF6Page = true;
                } else {
                    this.updateStatus('page-status', '非F6页面', 'warning');
                    isF6Page = false;
                }
            } else {
                this.updateStatus('page-status', '无法获取', 'error');
                isF6Page = false;
            }

            // 根据页面类型决定后续操作
            if (isF6Page) {
                // F6页面：检查API连接并获取订单统计
                await this.testApiConnection();

                // 根据API连接状态决定是否获取订单统计
                const apiStatusElement = document.getElementById('api-status');
                const isApiConnected = apiStatusElement && apiStatusElement.textContent === '正常';

                if (isApiConnected) {
                    try {
                        await this.refreshOrderStats();
                        console.log('初始订单统计获取完成');
                        // API连接正常，启用所有功能
                        this.enableButtons();
                    } catch (error) {
                        console.log('初始订单统计获取失败:', error.message);
                        this.setServiceErrorOrderStats();
                        // API连接异常，禁用订单监控
                        this.disableOrderMonitoring();
                    }
                } else {
                    this.setServiceErrorOrderStats();
                    // API连接异常，禁用订单监控
                    this.disableOrderMonitoring();
                }
            } else {
                // 非F6页面：设置相应状态并禁用按钮
                this.updateStatus('api-status', '非F6页面', 'warning');
                this.setNonF6PageOrderStats();
                this.disableButtons();
            }

            console.log('状态检查完成');
        } catch (error) {
            console.error('状态检查失败:', error);
            this.setErrorStatus();
        }
    }

    /**
     * 刷新状态 - 增强版，包含服务重连和用户反馈
     */
    async refreshStatus() {
        try {
            console.log('🔄 开始手动刷新状态...');

            // 显示按钮状态
            const refreshBtn = document.getElementById('refresh-status');
            if (refreshBtn) {
                refreshBtn.textContent = '🔄 刷新中...';
                refreshBtn.disabled = true;
            }

            // 显示刷新中状态
            const activePollingEl = document.getElementById('active-polling');
            const cachedOrdersEl = document.getElementById('cached-orders');

            if (activePollingEl) {
                activePollingEl.textContent = '刷新中...';
            }
            if (cachedOrdersEl) {
                cachedOrdersEl.textContent = '刷新中...';
            }

            // 检查当前页面类型
            const tabs = await this.getCurrentTab();
            let isF6Page = false;

            if (tabs && tabs.length > 0) {
                const tab = tabs[0];
                isF6Page = tab.url && (tab.url.includes('f6car.com') || tab.url.includes('f6yc.com'));
            }

            if (isF6Page) {
                // F6页面：刷新API连接和订单统计
                console.log('🔍 检查API连接状态...');
                await this.testApiConnection();

                // 根据API连接状态决定是否获取订单统计
                const apiStatusElement = document.getElementById('api-status');
                const isApiConnected = apiStatusElement && apiStatusElement.textContent === '正常';

                if (isApiConnected) {
                    try {
                        console.log('✅ API连接正常，刷新订单统计...');
                        await this.refreshOrderStats();
                        console.log('✅ 手动刷新完成：服务已恢复正常');
                        this.showNotification('服务连接已恢复，订单数据已更新', 'success');
                    } catch (error) {
                        console.log('❌ 获取订单统计失败:', error.message);
                        this.setServiceErrorOrderStats();
                        this.showNotification('订单统计获取失败，请稍后重试', 'error');
                    }
                } else {
                    console.log('⚠️ API连接异常，显示错误状态');
                    this.setServiceErrorOrderStats();
                    this.showNotification('服务连接异常，请检查网络或稍后重试', 'error');
                }
            } else {
                // 非F6页面：显示相应状态
                this.updateStatus('api-status', '非F6页面', 'warning');
                this.setNonF6PageOrderStats();
                this.showNotification('请在F6页面使用此功能', 'warning');
            }

            console.log('✅ 状态刷新完成');
        } catch (error) {
            console.error('❌ 状态刷新失败:', error);
            this.setServiceErrorOrderStats();
            this.showNotification('刷新失败，请稍后重试', 'error');
        } finally {
            // 恢复按钮状态
            const refreshBtn = document.getElementById('refresh-status');
            if (refreshBtn) {
                refreshBtn.textContent = '🔄 刷新状态';
                refreshBtn.disabled = false;
            }
        }
    }

    /**
     * 刷新订单统计数据 - 从F6页面获取店名
     */
    async refreshOrderStats(storeName = null) {
        try {
            const API_BASE_URL = 'http://localhost:5000';

            // 从F6页面获取店面名称
            if (!storeName) {
                try {
                    // 尝试从当前F6页面获取店面名称
                    const tabs = await this.getCurrentTab();
                    if (tabs && tabs.length > 0) {
                        const tab = tabs[0];
                        if (tab.url && (tab.url.includes('f6car.com') || tab.url.includes('f6yc.com'))) {
                            // 向content script请求店面信息
                            const response = await chrome.tabs.sendMessage(tab.id, {
                                action: 'getStoreInfo'
                            });

                            if (response && response.success && response.data.storeName) {
                                storeName = response.data.storeName;
                                console.log(`✅ 从F6页面获取店面名称: ${storeName}`);
                                // 保存到localStorage以备后用
                                localStorage.setItem('f6_edriver_store_name', storeName);
                            } else {
                                throw new Error('无法从F6页面获取店面名称');
                            }
                        } else {
                            throw new Error('当前页面不是F6页面');
                        }
                    } else {
                        throw new Error('无法获取当前标签页');
                    }
                } catch (error) {
                    console.log(`⚠️ 从F6页面获取店名失败: ${error.message}`);
                    // 回退到保存的店面名称
                    const savedStoreName = localStorage.getItem('f6_edriver_store_name');
                    if (savedStoreName) {
                        storeName = savedStoreName;
                        console.log(`✅ 使用保存的店面名称: ${storeName}`);
                    } else {
                        // 无法获取店面名称，抛出错误
                        throw new Error('无法获取店面名称，请确保在F6页面中使用插件');
                    }
                }
            }

            console.log(`🔄 刷新订单统计: ${storeName}`);
            console.log(`🔄 编码后的店名: ${encodeURIComponent(storeName)}`);

            // 并行获取活跃订单和历史订单数量
            const activeURL = `${API_BASE_URL}/api/orders/active-count?store_name=${encodeURIComponent(storeName)}`;
            const historyURL = `${API_BASE_URL}/api/orders/history-count?store_name=${encodeURIComponent(storeName)}`;

            console.log(`🔄 活跃订单API URL: ${activeURL}`);
            console.log(`🔄 历史订单API URL: ${historyURL}`);

            const [activeResponse, historyResponse] = await Promise.all([
                fetch(activeURL),
                fetch(historyURL)
            ]);

            // 处理活跃订单响应
            const activePollingEl = document.getElementById('active-polling');
            if (activeResponse.ok) {
                const activeData = await activeResponse.json();
                if (activeData.success && activePollingEl) {
                    const activeCount = activeData.data.active_orders;
                    activePollingEl.textContent = `${activeCount}个`;
                    activePollingEl.title = `活跃订单: ${activeCount}个，点击查看详情`;

                    // 添加点击事件，跳转到订单监控页面
                    activePollingEl.style.cursor = 'pointer';
                    activePollingEl.style.color = '#409eff';
                    activePollingEl.style.textDecoration = 'underline';

                    // 移除之前的事件监听器
                    activePollingEl.replaceWith(activePollingEl.cloneNode(true));
                    const newActivePollingEl = document.getElementById('active-polling');

                    newActivePollingEl.addEventListener('click', () => {
                        console.log('点击活跃订单数量，切换到订单监控页面');
                        this.switchTab('orders');
                    });

                    console.log(`✅ 活跃订单: ${activeCount}个`);
                }
            } else {
                console.warn('❌ 获取活跃订单失败');
                if (activePollingEl) {
                    activePollingEl.textContent = '0个';
                    activePollingEl.title = '获取失败';
                    activePollingEl.style.cursor = 'default';
                    activePollingEl.style.color = '';
                    activePollingEl.style.textDecoration = '';
                }
            }

            // 处理历史订单响应
            const cachedOrdersEl = document.getElementById('cached-orders');
            if (historyResponse.ok) {
                const historyData = await historyResponse.json();
                if (historyData.success && cachedOrdersEl) {
                    const historyCount = historyData.data.history_orders;
                    cachedOrdersEl.textContent = `${historyCount}个`;
                    cachedOrdersEl.title = `历史订单: ${historyCount}个`;
                    console.log(`✅ 历史订单: ${historyCount}个`);
                }
            } else {
                console.warn('❌ 获取历史订单失败');
                if (cachedOrdersEl) {
                    cachedOrdersEl.textContent = '0个';
                    cachedOrdersEl.title = '获取失败';
                }
            }

        } catch (error) {
            console.error('❌ 刷新订单统计失败:', error);
            this.setDefaultOrderStats();
        }
    }

    /**
     * 设置默认订单统计
     */
    setDefaultOrderStats() {
        const activePollingEl = document.getElementById('active-polling');
        const cachedOrdersEl = document.getElementById('cached-orders');

        if (activePollingEl) {
            activePollingEl.textContent = '0个';
            activePollingEl.title = '连接失败';
        }
        if (cachedOrdersEl) {
            cachedOrdersEl.textContent = '0个';
            cachedOrdersEl.title = '连接失败';
        }
    }

    /**
     * 设置非F6页面的订单统计显示
     */
    setNonF6PageOrderStats() {
        const activePollingEl = document.getElementById('active-polling');
        const cachedOrdersEl = document.getElementById('cached-orders');

        if (activePollingEl) {
            activePollingEl.textContent = '-';
            activePollingEl.title = '请在F6页面使用此功能';
        }
        if (cachedOrdersEl) {
            cachedOrdersEl.textContent = '-';
            cachedOrdersEl.title = '请在F6页面使用此功能';
        }
    }

    /**
     * 设置服务异常时的订单统计显示
     */
    setServiceErrorOrderStats() {
        const activePollingEl = document.getElementById('active-polling');
        const cachedOrdersEl = document.getElementById('cached-orders');

        if (activePollingEl) {
            activePollingEl.textContent = '0个';
            activePollingEl.title = '服务连接异常';
        }
        if (cachedOrdersEl) {
            cachedOrdersEl.textContent = '0个';
            cachedOrdersEl.title = '服务连接异常';
        }
    }

    /**
     * 显示通知消息
     */
    showNotification(message, type = 'info', duration = 3000) {
        // 使用现有的popup通知方法
        this.showPopupNotification(message, type, duration);
    }

    /**
     * 禁用按钮（非F6页面时）
     */
    disableButtons() {
        const refreshBtn = document.getElementById('refresh-status');
        const reloadBtn = document.getElementById('reload-plugin');
        const ordersTab = document.querySelector('[data-tab="orders"]');

        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.style.opacity = '0.5';
            refreshBtn.style.cursor = 'not-allowed';
            refreshBtn.title = '请在F6页面使用此功能';
        }
        if (reloadBtn) {
            reloadBtn.disabled = true;
            reloadBtn.style.opacity = '0.5';
            reloadBtn.style.cursor = 'not-allowed';
            reloadBtn.title = '请在F6页面使用此功能';
        }

        // 禁用订单监控标签页
        if (ordersTab) {
            ordersTab.style.opacity = '0.5';
            ordersTab.style.cursor = 'not-allowed';
            ordersTab.style.pointerEvents = 'none';
            ordersTab.title = '请在F6页面使用此功能';
        }
    }

    /**
     * 启用按钮（F6页面时）
     */
    enableButtons() {
        const refreshBtn = document.getElementById('refresh-status');
        const reloadBtn = document.getElementById('reload-plugin');
        const ordersTab = document.querySelector('[data-tab="orders"]');

        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.style.opacity = '1';
            refreshBtn.style.cursor = 'pointer';
            refreshBtn.title = '刷新插件状态和订单统计';
        }
        if (reloadBtn) {
            reloadBtn.disabled = false;
            reloadBtn.style.opacity = '1';
            reloadBtn.style.cursor = 'pointer';
            reloadBtn.title = '重新加载插件';
        }

        // 启用订单监控标签页
        if (ordersTab) {
            ordersTab.style.opacity = '1';
            ordersTab.style.cursor = 'pointer';
            ordersTab.style.pointerEvents = 'auto';
            ordersTab.title = '订单监控';
        }
    }

    /**
     * 禁用订单监控功能（服务异常时）
     */
    disableOrderMonitoring() {
        const refreshBtn = document.getElementById('refresh-status');
        const reloadBtn = document.getElementById('reload-plugin');
        const ordersTab = document.querySelector('[data-tab="orders"]');

        // 启用基础按钮（刷新和重载）
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.style.opacity = '1';
            refreshBtn.style.cursor = 'pointer';
            refreshBtn.title = '刷新插件状态和订单统计';
        }
        if (reloadBtn) {
            reloadBtn.disabled = false;
            reloadBtn.style.opacity = '1';
            reloadBtn.style.cursor = 'pointer';
            reloadBtn.title = '重新加载插件';
        }

        // 禁用订单监控标签页
        if (ordersTab) {
            ordersTab.style.opacity = '0.5';
            ordersTab.style.cursor = 'not-allowed';
            ordersTab.style.pointerEvents = 'none';
            ordersTab.title = '服务连接异常，无法使用订单监控';
        }
    }

    /**
     * 测试API连接 - 直接测试中间件API服务状态
     */
    async testApiConnection() {
        this.updateStatus('api-status', '检查中...', 'warning');

        try {
            // 直接测试中间件API连接，不依赖F6页面
            const API_BASE_URL = 'http://localhost:5000';

            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('连接超时')), 5000);
            });

            const fetchPromise = fetch(`${API_BASE_URL}/api/status`);

            try {
                const response = await Promise.race([fetchPromise, timeoutPromise]);

                if (response.ok) {
                    const data = await response.json();
                    // 检查响应是否包含预期的状态结构
                    if (data && typeof data === 'object' && (data.f6 || data.edaijia)) {
                        this.updateStatus('api-status', '正常', 'success');
                        this.notifyBackgroundStatus(true);
                        console.log('✅ 中间件API连接成功');
                    } else {
                        this.updateStatus('api-status', '异常', 'error');
                        this.notifyBackgroundStatus(false);
                        console.log('❌ 中间件API响应格式异常');
                    }
                } else {
                    this.updateStatus('api-status', '异常', 'error');
                    this.notifyBackgroundStatus(false);
                    console.log(`❌ 中间件API响应错误: ${response.status}`);
                }
            } catch (apiError) {
                this.updateStatus('api-status', '异常', 'error');
                this.notifyBackgroundStatus(false);
                console.log('❌ 中间件API连接失败:', apiError.message);
            }
        } catch (error) {
            this.updateStatus('api-status', '异常', 'error');
            this.notifyBackgroundStatus(false);
            console.log('❌ API连接测试失败:', error.message);
        }
    }

    /**
     * 通知background script更新图标状态
     */
    notifyBackgroundStatus(isConnected) {
        try {
            chrome.runtime.sendMessage({
                action: 'updateIconStatus',
                connected: isConnected
            });
        } catch (error) {
            console.log('通知background script失败:', error);
        }
    }

    /**
     * 获取当前标签页
     */
    async getCurrentTab() {
        return new Promise((resolve) => {
            chrome.tabs.query({ active: true, currentWindow: true }, resolve);
        });
    }

    /**
     * 重载插件
     */
    async reloadPlugin() {
        try {
            console.log('开始重载插件...');

            // 显示重载状态
            const reloadBtn = document.getElementById('reload-plugin');
            if (reloadBtn) {
                reloadBtn.textContent = '🔄 重载中...';
                reloadBtn.disabled = true;
                reloadBtn.style.opacity = '0.6';
            }

            // 获取当前标签页
            const tabs = await this.getCurrentTab();
            if (tabs && tabs.length > 0) {
                const tab = tabs[0];

                // 检查是否是F6页面
                if (tab.url && (tab.url.includes('f6car.com') || tab.url.includes('f6yc.com'))) {
                    try {
                        // 向content script发送重载消息
                        const response = await chrome.tabs.sendMessage(tab.id, {
                            action: 'reloadPlugin'
                        });

                        console.log('插件重载消息已发送:', response);

                        // 显示成功状态
                        if (reloadBtn) {
                            reloadBtn.textContent = '✅ 重载成功';
                        }

                        // 延迟刷新页面，让content script有时间处理
                        setTimeout(() => {
                            chrome.tabs.reload(tab.id);
                            console.log('页面已刷新');
                        }, 1000);

                    } catch (error) {
                        console.log('发送重载消息失败，直接刷新页面:', error.message);

                        // 显示直接刷新状态
                        if (reloadBtn) {
                            reloadBtn.textContent = '🔄 直接刷新';
                        }

                        // 如果发送消息失败，直接刷新页面
                        setTimeout(() => {
                            chrome.tabs.reload(tab.id);
                            console.log('页面已直接刷新');
                        }, 800);
                    }
                } else {
                    console.log('非F6页面，无需重载插件');
                    if (reloadBtn) {
                        reloadBtn.textContent = '❌ 非F6页面';
                        setTimeout(() => {
                            reloadBtn.textContent = '🔄 重载插件';
                            reloadBtn.disabled = false;
                            reloadBtn.style.opacity = '1';
                        }, 2000);
                    }

                    return;
                }
            } else {
                console.log('无法获取当前标签页');
                if (reloadBtn) {
                    reloadBtn.textContent = '❌ 获取页面失败';
                    setTimeout(() => {
                        reloadBtn.textContent = '🔄 重载插件';
                        reloadBtn.disabled = false;
                        reloadBtn.style.opacity = '1';
                    }, 2000);
                }

                return;
            }

            // 延迟关闭popup，让用户看到重载状态
            setTimeout(() => {
                this.closePopup();
            }, 2000);

        } catch (error) {
            console.error('重载插件失败:', error);

            // 恢复按钮状态
            const reloadBtn = document.getElementById('reload-plugin');
            if (reloadBtn) {
                reloadBtn.textContent = '❌ 重载失败';
                setTimeout(() => {
                    reloadBtn.textContent = '🔄 重载插件';
                    reloadBtn.disabled = false;
                    reloadBtn.style.opacity = '1';
                }, 2000);
            }


        }
    }

    /**
     * 启动自动刷新 - 增强版，包含服务连接重试机制
     */
    startAutoRefresh() {
        // 清除现有的定时器
        this.stopAutoRefresh();

        // 每60秒自动刷新一次订单统计和服务状态
        this.autoRefreshInterval = setInterval(async () => {
            console.log('⏰ 自动刷新服务状态和订单统计...');

            try {
                // 1. 先检查API连接状态
                await this.testApiConnection();

                // 2. 如果连接正常，刷新订单统计
                const apiStatusElement = document.getElementById('api-status');
                const isApiConnected = apiStatusElement && apiStatusElement.textContent === '正常';

                if (isApiConnected) {
                    await this.refreshOrderStats();
                    console.log('✅ 自动刷新完成：服务正常，订单统计已更新');
                } else {
                    console.log('⚠️ 自动刷新：服务异常，跳过订单统计更新');
                    this.setServiceErrorOrderStats();
                }
            } catch (error) {
                console.error('❌ 自动刷新失败:', error);
                this.setServiceErrorOrderStats();
            }
        }, 60000); // 60秒

        console.log('✅ 自动刷新定时器已启动 (60秒间隔，包含服务重试)');
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
            console.log('自动刷新定时器已停止');
        }
    }

    /**
     * 关闭popup时清理资源
     */
    cleanup() {
        // 停止自动刷新
        this.stopAutoRefresh();

        // 停止订单监控
        this.stopOrderMonitoring();

        // 清理其他定时器
        if (this.keepAliveInterval) {
            clearInterval(this.keepAliveInterval);
            this.keepAliveInterval = null;
        }

        console.log('Popup资源清理完成');
    }

    /**
     * 加载活跃订单
     */
    async loadActiveOrders() {
        console.log('🔄 开始加载活跃订单...');

        const loadingEl = document.getElementById('orders-loading');
        const listEl = document.getElementById('active-orders-list');
        const emptyEl = document.getElementById('orders-empty');

        // 显示加载状态
        if (loadingEl) loadingEl.classList.remove('hidden');
        if (listEl) listEl.classList.add('hidden');
        if (emptyEl) emptyEl.classList.add('hidden');

        try {
            // 获取店面名称
            const storeName = await this.getStoreName();
            if (!storeName) {
                throw new Error('无法获取店面名称');
            }

            console.log(`📍 获取店面名称: ${storeName}`);

            // 调用API获取活跃订单
            const API_BASE_URL = 'http://localhost:5000';
            const url = `${API_BASE_URL}/api/orders/active-list?store_name=${encodeURIComponent(storeName)}`;

            const response = await fetch(url, { timeout: 10000 });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.orders) {
                console.log(`✅ 获取到 ${data.orders.length} 个活跃订单`);
                this.renderActiveOrders(data.orders);

                // 启动订单监控
                this.startOrderMonitoring(storeName);
            } else {
                throw new Error(data.message || '获取订单失败');
            }

        } catch (error) {
            console.error('❌ 加载活跃订单失败:', error);
            this.showOrderError(error.message);
        } finally {
            // 隐藏加载状态
            if (loadingEl) loadingEl.classList.add('hidden');
        }
    }

    /**
     * 渲染活跃订单列表
     */
    renderActiveOrders(orders) {
        const listEl = document.getElementById('active-orders-list');
        const emptyEl = document.getElementById('orders-empty');

        if (!orders || orders.length === 0) {
            if (listEl) listEl.classList.add('hidden');
            if (emptyEl) emptyEl.classList.remove('hidden');
            return;
        }

        if (emptyEl) emptyEl.classList.add('hidden');
        if (listEl) {
            listEl.classList.remove('hidden');
            listEl.innerHTML = orders.map(order => this.createOrderCard(order)).join('');

            // 绑定操作按钮事件
            this.bindOrderActionEvents();
        }
    }

    /**
     * 创建订单卡片HTML
     */
    createOrderCard(order) {
        // e代驾官方状态码映射 + 内部状态映射
        const statusNames = {
            // e代驾官方状态码
            '102': '开始系统派单',
            '180': '系统派单中',
            '301': '司机接单',
            '302': '司机就位',
            '303': '司机开车',
            '304': '代驾结束',
            '403': '客户取消',
            '404': '司机取消',
            '501': '司机报单',
            '506': '系统派单失败',
            // 内部状态映射（兼容性）
            'pending': '等待派单',
            'accepted': '司机接单',
            'in_progress': '服务中',
            'completed': '已完成',
            'cancelled': '已取消',
            'failed': '派单失败'
        };

        const statusName = statusNames[order.status] || order.status;
        const createdTime = new Date(order.created_time).toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        // 判断订单是否可以编辑和取消（根据e代驾官方API文档）
        // 可修改状态：102(开始系统派单), 180(系统派单中), 301(司机接单), 302(司机就位)
        const modifiableStatuses = ['102', '180', '301', '302', 'pending', 'accepted'];
        // 可取消状态：102(开始系统派单), 180(系统派单中), 301(司机接单), 302(司机就位)
        // 注意：303(司机开车), 304(代驾结束)不可取消
        const cancellableStatuses = ['102', '180', '301', '302', 'pending', 'accepted'];

        const canModify = modifiableStatuses.includes(order.status);
        const canCancel = cancellableStatuses.includes(order.status);

        // 生成操作按钮HTML（移除内联onclick，使用data属性）
        const actionButtons = (canModify || canCancel) ? `
            <div class="order-actions">
                ${canModify ? `
                    <button class="action-btn modify-btn"
                            data-order-id="${order.order_id}"
                            data-action="modify"
                            title="修改目的地">
                        ✏️
                    </button>
                ` : ''}
                ${canCancel ? `
                    <button class="action-btn cancel-btn"
                            data-order-id="${order.order_id}"
                            data-action="cancel"
                            title="取消订单">
                        ❌
                    </button>
                ` : ''}
            </div>
        ` : '';

        return `
            <div class="order-card" data-order-id="${order.order_id}">
                <div class="monitoring-indicator"></div>
                <div class="order-header">
                    <div class="order-id">${order.order_id}</div>
                    <div class="order-status status-${order.status}">${statusName}</div>
                    ${actionButtons}
                </div>
                <div class="order-info">
                    <div><strong>${order.customer_name}</strong> · ${order.car_info.plate}</div>
                    <div class="order-route">
                        <span>📍 ${this.truncateAddress(order.pickup_address)}</span>
                        <span class="route-icon">→</span>
                        <span>🎯 ${this.truncateAddress(order.destination_address)}</span>
                    </div>
                </div>
                <div class="order-meta">
                    <div class="order-time">
                        <span>⏰ ${createdTime}</span>
                    </div>
                    <div class="order-price">¥${order.price}</div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定订单操作按钮事件
     */
    bindOrderActionEvents() {
        // 绑定修改订单按钮
        document.querySelectorAll('.modify-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const orderId = btn.dataset.orderId;
                console.log(`点击修改订单按钮: ${orderId}`);
                this.showModifyOrderDialog(orderId);
            });
        });

        // 绑定取消订单按钮
        document.querySelectorAll('.cancel-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const orderId = btn.dataset.orderId;
                console.log(`点击取消订单按钮: ${orderId}`);
                this.showCancelOrderDialog(orderId);
            });
        });
    }

    /**
     * 截断地址显示
     */
    truncateAddress(address, maxLength = 12) {
        if (!address) return '未知地址';
        return address.length > maxLength ? address.substring(0, maxLength) + '...' : address;
    }

    /**
     * 显示订单错误
     */
    showOrderError(message) {
        const listEl = document.getElementById('active-orders-list');
        const emptyEl = document.getElementById('orders-empty');

        if (listEl) listEl.classList.add('hidden');
        if (emptyEl) {
            emptyEl.classList.remove('hidden');
            emptyEl.innerHTML = `
                <div style="text-align: center; padding: 40px 20px; color: #f56c6c;">
                    <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                    <div>加载订单失败</div>
                    <div style="font-size: 12px; margin-top: 8px;">${message}</div>
                </div>
            `;
        }
    }

    /**
     * 获取店面名称
     */
    async getStoreName() {
        try {
            console.log(`🏪 开始获取店面名称`);

            // 首先尝试从F6页面获取最新的店面信息
            const tabs = await this.getCurrentTab();
            if (tabs && tabs.length > 0) {
                const tab = tabs[0];
                console.log(`🔍 当前标签页URL: ${tab.url}`);

                if (tab.url && (tab.url.includes('f6car.com') || tab.url.includes('f6yc.com'))) {
                    console.log(`📡 尝试从F6页面获取店面信息`);

                    const response = await chrome.tabs.sendMessage(tab.id, {
                        action: 'getStoreInfo'
                    });

                    if (response && response.success && response.data.storeName) {
                        console.log(`✅ 从F6页面获取店面名称: ${response.data.storeName}`);
                        // 保存到localStorage（使用统一的键名）
                        localStorage.setItem('f6_store_name', response.data.storeName);
                        return response.data.storeName;
                    } else {
                        console.log(`⚠️ F6页面响应无效:`, response);
                    }
                } else {
                    console.log(`⚠️ 当前页面不是F6系统`);
                }
            } else {
                console.log(`⚠️ 无法获取当前标签页`);
            }

            // 如果无法从F6页面获取，尝试从localStorage获取之前保存的值
            const savedStoreName = localStorage.getItem('f6_store_name');
            if (savedStoreName) {
                console.log(`✅ 从localStorage获取店面名称: ${savedStoreName}`);
                return savedStoreName;
            }

        } catch (error) {
            console.log('❌ 从F6页面获取店名失败:', error.message);

            // 尝试从localStorage获取备用值
            const savedStoreName = localStorage.getItem('f6_store_name');
            if (savedStoreName) {
                console.log(`✅ 使用localStorage备用店面名称: ${savedStoreName}`);
                return savedStoreName;
            }
        }

        // 无法获取店面名称，返回null让调用方处理
        console.log(`❌ 无法获取店面名称，请确保在F6页面中使用插件`);
        return null;
    }

    /**
     * 启动订单监控
     */
    startOrderMonitoring(storeName) {
        // 停止现有监控
        this.stopOrderMonitoring();

        console.log(`🎯 启动订单监控: ${storeName}`);

        // 每30秒检查一次订单状态变化
        this.orderMonitoringInterval = setInterval(async () => {
            await this.checkOrderStatusChanges(storeName);
        }, 30000);

        console.log('✅ 订单监控已启动 (30秒间隔)');
    }

    /**
     * 停止订单监控
     */
    stopOrderMonitoring() {
        if (this.orderMonitoringInterval) {
            clearInterval(this.orderMonitoringInterval);
            this.orderMonitoringInterval = null;
            console.log('订单监控已停止');
        }
    }

    /**
     * 检查订单状态变化
     */
    async checkOrderStatusChanges(storeName) {
        try {
            console.log('🔍 检查订单状态变化...');

            // 获取当前显示的订单
            const currentOrders = this.getCurrentDisplayedOrders();

            // 获取最新的订单状态
            const API_BASE_URL = 'http://localhost:5000';
            const url = `${API_BASE_URL}/api/orders/active-list?store_name=${encodeURIComponent(storeName)}`;

            const response = await fetch(url, { timeout: 10000 });
            if (!response.ok) return;

            const data = await response.json();
            if (!data.success || !data.orders) return;

            // 检查状态变化
            const changes = this.detectOrderChanges(currentOrders, data.orders);

            if (changes.length > 0) {
                console.log(`📢 检测到 ${changes.length} 个订单状态变化`);

                // 更新显示
                this.renderActiveOrders(data.orders);

                // 发送通知
                changes.forEach(change => {
                    this.showOrderChangeNotification(change);
                });
            }

        } catch (error) {
            console.error('检查订单状态变化失败:', error);
        }
    }

    /**
     * 获取当前显示的订单
     */
    getCurrentDisplayedOrders() {
        const orderCards = document.querySelectorAll('.order-card');
        return Array.from(orderCards).map(card => ({
            order_id: card.dataset.orderId,
            status: card.querySelector('.order-status').className.match(/status-(\d+)/)?.[1] || ''
        }));
    }

    /**
     * 检测订单变化
     */
    detectOrderChanges(oldOrders, newOrders) {
        const changes = [];
        const oldOrderMap = new Map(oldOrders.map(o => [o.order_id, o.status]));

        newOrders.forEach(newOrder => {
            const oldStatus = oldOrderMap.get(newOrder.order_id);
            if (oldStatus && oldStatus !== newOrder.status) {
                changes.push({
                    order_id: newOrder.order_id,
                    customer_name: newOrder.customer_name,
                    old_status: oldStatus,
                    new_status: newOrder.status
                });
            }
        });

        return changes;
    }

    /**
     * 显示订单状态变化通知
     */
    showOrderChangeNotification(change) {
        // e代驾官方状态码映射 + 内部状态映射
        const statusNames = {
            // e代驾官方状态码
            '102': '开始系统派单',
            '180': '系统派单中',
            '301': '司机接单',
            '302': '司机就位',
            '303': '司机开车',
            '304': '代驾结束',
            '403': '客户取消',
            '404': '司机取消',
            '501': '司机报单',
            '506': '系统派单失败',
            // 内部状态映射（兼容性）
            'pending': '等待派单',
            'accepted': '司机接单',
            'in_progress': '服务中',
            'completed': '已完成',
            'cancelled': '已取消',
            'failed': '派单失败'
        };

        const oldStatusName = statusNames[change.old_status] || change.old_status;
        const newStatusName = statusNames[change.new_status] || change.new_status;

        const message = `${change.customer_name}的订单状态已更新：${oldStatusName} → ${newStatusName}`;

        // 显示popup内通知
        this.showPopupNotification(message, 'info', 5000);

        // 发送系统通知
        this.sendSystemNotification(message, change);
    }

    /**
     * 发送系统通知
     */
    sendSystemNotification(message, change) {
        try {
            chrome.runtime.sendMessage({
                action: 'showNotification',
                title: 'F6订单状态更新',
                message: message,
                data: change
            });
        } catch (error) {
            console.log('发送系统通知失败:', error);
        }
    }



    /**
     * 获取订单信息
     */
    async getOrderInfo(orderId) {
        try {
            console.log(`🔍 查找订单信息: ${orderId}`);

            // 从当前显示的订单列表中查找订单信息
            const orderCards = document.querySelectorAll('.order-card');
            console.log(`📋 找到 ${orderCards.length} 个订单卡片`);

            for (const card of orderCards) {
                const cardOrderId = card.querySelector('.order-id')?.textContent?.trim();
                console.log(`🔍 检查订单卡片: ${cardOrderId}`);

                if (cardOrderId === orderId) {
                    console.log(`✅ 找到匹配的订单卡片`);

                    // 根据实际的HTML结构提取信息
                    const orderInfoDiv = card.querySelector('.order-info');
                    if (orderInfoDiv) {
                        // 从第一行提取客户姓名和车牌：<strong>张先生</strong> · 粤B12345
                        const firstLine = orderInfoDiv.querySelector('div:first-child')?.textContent || '';
                        const [customerPart, platePart] = firstLine.split(' · ');
                        const customerName = customerPart?.replace(/\*\*/g, '').trim() || '未知';
                        const plateNumber = platePart?.trim() || '未知';

                        // 从路线信息中提取目的地：🎯 深圳市南山区科技园南区
                        const routeDiv = orderInfoDiv.querySelector('.order-route');
                        const destinationSpan = routeDiv?.querySelector('span:last-child');
                        const destinationAddress = destinationSpan?.textContent?.replace('🎯 ', '').trim() || '未知';

                        // 从状态信息中提取状态名称
                        const statusDiv = orderInfoDiv.querySelector('.order-status');
                        const statusName = statusDiv?.textContent?.trim() || '未知状态';

                        console.log(`📋 提取到订单信息:`, {
                            customer_name: customerName,
                            plate_number: plateNumber,
                            destination_address: destinationAddress,
                            status_name: statusName
                        });

                        return {
                            customer_name: customerName,
                            plate_number: plateNumber,
                            destination_address: destinationAddress,
                            status_name: statusName
                        };
                    }
                }
            }

            // 如果在当前列表中找不到，尝试通过API获取
            const storeName = await this.getStoreName();
            if (storeName) {
                const API_BASE_URL = 'http://localhost:5000';

                const response = await fetch(`${API_BASE_URL}/api/f6/order_detail`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        order_id: orderId,
                        manager_name: storeName
                    })
                });

                const data = await response.json();
                if (data.success && data.order) {
                    return {
                        customer_name: data.order.customer_name || '未知',
                        plate_number: data.order.plate_number || '未知',
                        destination_address: data.order.destination_address || '未知'
                    };
                }
            } else {
                console.log(`⚠️ 无法获取店面名称，跳过API调用`);
            }

            return {
                customer_name: '未知',
                plate_number: '未知',
                destination_address: '未知'
            };
        } catch (error) {
            console.error('获取订单信息失败:', error);
            return {
                customer_name: '未知',
                plate_number: '未知',
                destination_address: '未知'
            };
        }
    }

    /**
     * 显示修改订单对话框
     */
    async showModifyOrderDialog(orderId) {
        console.log(`显示修改订单对话框: ${orderId}`);

        // 先清理已存在的对话框
        this.closeExistingDialogs();

        // 检查店面名称是否可用
        const storeName = await this.getStoreName();
        if (!storeName) {
            // 提供临时设置店面名称的选项
            const tempStoreName = prompt('无法从F6页面获取店面名称，请临时输入店面名称（用于测试）:');
            if (tempStoreName && tempStoreName.trim()) {
                localStorage.setItem('f6_store_name', tempStoreName.trim());
            } else {
                this.showPopupNotification('需要店面名称才能修改订单，请在F6页面中使用插件', 'error');
                return;
            }
        }

        // 获取订单信息
        const orderInfo = await this.getOrderInfo(orderId);

        const dialog = document.createElement('div');
        dialog.className = 'order-dialog-overlay';

        // 透明背景，让用户能看到订单监控页面
        dialog.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(1px) !important;
            z-index: 2147483647 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        `;

        dialog.innerHTML = `
            <div class="order-dialog">
                <div class="dialog-header">
                    <h3>修改目的地</h3>
                    <button class="close-btn" data-action="close">×</button>
                </div>
                <div class="dialog-body">
                    <!-- 订单信息展示 - 参考F6页面风格 -->
                    <div class="order-info-section">
                        <div class="order-info-simple">
                            <div class="order-info-item">
                                <span class="order-info-label">订单号：</span>
                                <span class="order-info-value">${orderId}</span>
                            </div>
                            <div class="order-info-item">
                                <span class="order-info-label">客户：</span>
                                <div class="order-info-customer">
                                    <span class="customer-name">${orderInfo.customer_name}</span>
                                    <span class="customer-plate">${orderInfo.plate_number}</span>
                                </div>
                            </div>
                            <div class="order-info-item">
                                <span class="order-info-label">当前目的地：</span>
                                <div class="destination-address">${orderInfo.destination_address}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 修改目的地 -->
                    <div class="modify-section">
                        <div class="form-group">
                            <label data-icon="location">新目的地地址</label>
                            <div class="address-input-container">
                                <div class="input-group">
                                    <input type="text" id="new-destination" placeholder="请输入新的目的地地址" autocomplete="off" />
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-info active" type="button" id="suggestion-toggle" onclick="window.popupManager.toggleAddressSuggestion()" title="切换地址建议">
                                            <i class="fas fa-lightbulb"></i>
                                        </button>
                                    </div>
                                </div>
                                <div id="address-suggestions" class="address-suggestions hidden"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label data-icon="money">预估费用</label>
                            <div id="modify-estimate" class="estimate-display">输入地址后自动计算</div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button class="btn-cancel" data-action="close">取消</button>
                    <button class="btn-confirm" data-action="confirm" data-order-id="${orderId}" disabled>确认修改</button>
                </div>
            </div>
        `;

        // 直接添加到body确保最高层级
        document.body.appendChild(dialog);

        // 绑定对话框事件
        this.bindDialogEvents(dialog, orderId, 'modify');

        // 添加地址输入监听
        const addressInput = dialog.querySelector('#new-destination');
        const suggestionsDiv = dialog.querySelector('#address-suggestions');
        const confirmBtn = dialog.querySelector('.btn-confirm');

        let debounceTimer;
        let selectedCoordinates = null;

        addressInput.addEventListener('input', () => {
            clearTimeout(debounceTimer);
            const query = addressInput.value.trim();

            if (query.length < 2) {
                suggestionsDiv.classList.add('hidden');
                confirmBtn.disabled = true;
                document.getElementById('modify-estimate').textContent = '输入地址后自动计算';
                return;
            }

            debounceTimer = setTimeout(() => {
                // 检查地址建议按钮是否激活
                const suggestionToggle = document.getElementById('suggestion-toggle');
                if (suggestionToggle && suggestionToggle.classList.contains('active')) {
                    this.getAddressSuggestions(query, suggestionsDiv, addressInput, confirmBtn, orderId);
                } else {
                    // 如果地址建议关闭，直接进行地址解析
                    this.geocodeAndEstimate(query, confirmBtn, orderId);
                }
            }, 500);
        });

        // 点击其他地方隐藏建议
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.address-input-container')) {
                suggestionsDiv.classList.add('hidden');
            }
        });
    }

    /**
     * 显示取消订单对话框
     */
    async showCancelOrderDialog(orderId) {
        console.log(`显示取消订单对话框: ${orderId}`);

        // 先清理已存在的对话框
        this.closeExistingDialogs();

        // 获取订单信息
        const orderInfo = await this.getOrderInfo(orderId);
        if (!orderInfo) {
            this.showPopupNotification('无法获取订单信息', 'error');
            return;
        }

        const dialog = document.createElement('div');
        dialog.className = 'order-dialog-overlay';

        // 透明背景，让用户能看到订单监控页面
        dialog.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(1px) !important;
            z-index: 2147483647 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        `;

        dialog.innerHTML = `
            <div class="order-dialog">
                <div class="dialog-header">
                    <h3>取消订单</h3>
                    <button class="close-btn" data-action="close">×</button>
                </div>
                <div class="dialog-body">
                    <!-- 订单信息展示 - 仿订单卡片风格 -->
                    <div class="order-info-section">
                        <div class="order-info-simple">
                            <div class="order-info-item">
                                <span class="order-info-label">订单号：</span>
                                <span class="order-info-value">${orderId}</span>
                            </div>
                            <div class="order-info-item">
                                <span class="order-info-label">客户：</span>
                                <div class="order-info-customer">
                                    <span class="customer-name">${orderInfo.customer_name}</span>
                                    <span class="customer-plate">${orderInfo.plate_number}</span>
                                </div>
                            </div>
                            <div class="order-info-item">
                                <span class="order-info-label">目的地：</span>
                                <div class="destination-address">${orderInfo.destination_address}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 取消费用信息 -->
                    <div class="cancel-section">
                        <div class="fee-section">
                            <h5 style="margin: 0 0 8px 0; color: #606266; font-size: 13px; font-weight: 500;">💰 费用信息</h5>
                            <div id="cancel-fee-info" class="fee-info" style="padding: 8px 12px; background: #f8f9fa; border-radius: 4px; border: 1px solid #e9ecef; font-size: 12px; color: #606266;">正在查询取消费用...</div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button class="btn-cancel" data-action="close">不取消</button>
                    <button class="btn-confirm btn-danger" data-action="confirm" data-order-id="${orderId}">确认取消</button>
                </div>
            </div>
        `;

        // 直接添加到body确保最高层级
        document.body.appendChild(dialog);

        // 绑定对话框事件
        this.bindDialogEvents(dialog, orderId, 'cancel');

        // 查询取消费用
        this.getCancelOrderFee(orderId);
    }

    /**
     * 关闭已存在的对话框
     */
    closeExistingDialogs() {
        const existingDialogs = document.querySelectorAll('.order-dialog-overlay');
        existingDialogs.forEach(dialog => {
            console.log('清理已存在的对话框');
            dialog.remove();
        });
    }

    /**
     * 切换地址建议功能
     */
    toggleAddressSuggestion() {
        const toggle = document.getElementById('suggestion-toggle');
        const suggestions = document.getElementById('address-suggestions');

        if (toggle && suggestions) {
            toggle.classList.toggle('active');
            const isActive = toggle.classList.contains('active');

            if (!isActive) {
                // 关闭建议时隐藏建议列表
                suggestions.classList.add('hidden');
            }

            console.log(`地址建议功能: ${isActive ? '开启' : '关闭'}`);
        }
    }



    /**
     * 绑定对话框事件
     */
    bindDialogEvents(dialog, orderId, dialogType) {
        console.log(`绑定对话框事件: ${dialogType}, 订单ID: ${orderId}`);

        // 绑定关闭按钮
        const closeButtons = dialog.querySelectorAll('[data-action="close"]');
        console.log(`找到 ${closeButtons.length} 个关闭按钮`);
        closeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('关闭对话框');
                dialog.remove();
            });
        });

        // 绑定确认按钮
        const confirmBtn = dialog.querySelector('[data-action="confirm"]');
        console.log(`确认按钮:`, confirmBtn);
        if (confirmBtn) {
            confirmBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`确认${dialogType}操作`);

                if (dialogType === 'modify') {
                    this.confirmModifyOrder(orderId);
                } else if (dialogType === 'cancel') {
                    this.confirmCancelOrder(orderId);
                }
            });
        }
    }

    /**
     * 获取地址建议
     */
    async getAddressSuggestions(query, suggestionsDiv, addressInput, confirmBtn, orderId) {
        try {
            const API_BASE_URL = 'http://localhost:5000';
            const response = await fetch(`${API_BASE_URL}/api/geocoding/suggestions`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    query: query,
                    city: '深圳市',
                    limit: 5
                })
            });

            const data = await response.json();
            if (data.success && data.suggestions && data.suggestions.length > 0) {
                this.renderAddressSuggestions(data.suggestions, suggestionsDiv, addressInput, confirmBtn, orderId);
            } else {
                // 如果没有建议，尝试直接解析地址
                this.geocodeAndEstimate(query, confirmBtn, orderId);
            }
        } catch (error) {
            console.error('获取地址建议失败:', error);
            // 尝试直接解析地址
            this.geocodeAndEstimate(query, confirmBtn, orderId);
        }
    }

    /**
     * 渲染地址建议
     */
    renderAddressSuggestions(suggestions, suggestionsDiv, addressInput, confirmBtn, orderId) {
        suggestionsDiv.innerHTML = suggestions.map(suggestion => {
            // 安全获取建议数据，避免undefined
            const name = suggestion.address || suggestion.name || suggestion.title || '未知地址';
            const address = suggestion.formatted_address || suggestion.district || suggestion.city || '';
            const lng = suggestion.longitude || suggestion.location?.lng || suggestion.lng || '';
            const lat = suggestion.latitude || suggestion.location?.lat || suggestion.lat || '';

            return `
                <div class="suggestion-item" data-address="${name}" data-lng="${lng}" data-lat="${lat}">
                    <div class="suggestion-name">${name}</div>
                    <div class="suggestion-address">${address}</div>
                </div>
            `;
        }).join('');

        suggestionsDiv.classList.remove('hidden');

        // 绑定点击事件
        suggestionsDiv.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const address = item.dataset.address;
                const lng = item.dataset.lng;
                const lat = item.dataset.lat;

                addressInput.value = address;
                suggestionsDiv.classList.add('hidden');

                if (lng && lat) {
                    this.estimateModifyOrderCostWithCoords(orderId, address, parseFloat(lng), parseFloat(lat), confirmBtn);
                } else {
                    this.geocodeAndEstimate(address, confirmBtn, orderId);
                }
            });
        });
    }

    /**
     * 地址解析并估值
     */
    async geocodeAndEstimate(address, confirmBtn, orderId) {
        try {
            const API_BASE_URL = 'http://localhost:5000';
            const response = await fetch(`${API_BASE_URL}/api/geocoding`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ address: address })
            });

            const data = await response.json();
            if (data.success && data.data) {
                const { longitude, latitude } = data.data;
                this.estimateModifyOrderCostWithCoords(orderId, address, longitude, latitude, confirmBtn);
            } else {
                document.getElementById('modify-estimate').textContent = '地址解析失败，请重新输入';
                confirmBtn.disabled = true;
            }
        } catch (error) {
            console.error('地址解析失败:', error);
            document.getElementById('modify-estimate').textContent = '地址解析失败，请重新输入';
            confirmBtn.disabled = true;
        }
    }

    /**
     * 使用坐标预估修改订单费用
     */
    async estimateModifyOrderCostWithCoords(orderId, address, longitude, latitude, confirmBtn) {
        try {
            console.log(`💰 开始预估修改订单费用: ${orderId}`);
            document.getElementById('modify-estimate').textContent = '计算中...';
            confirmBtn.disabled = true;

            const storeName = await this.getStoreName();
            console.log(`🏪 获取店面名称: ${storeName}`);

            if (!storeName) {
                throw new Error('无法获取店面名称，请确保在F6页面中使用插件');
            }

            const API_BASE_URL = 'http://localhost:5000';
            const requestData = {
                order_id: orderId,
                destination_address: address,
                destination_longitude: longitude,
                destination_latitude: latitude,
                manager_name: storeName
            };

            console.log(`📤 发送预估费用请求:`, requestData);

            const response = await fetch(`${API_BASE_URL}/api/f6/modify_order_estimate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData)
            });

            console.log(`📥 预估费用响应状态: ${response.status}`);

            if (!response.ok) {
                // 尝试解析错误响应
                let errorMessage = `HTTP ${response.status}`;
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorMessage;
                } catch (e) {
                    // 如果无法解析JSON，使用状态文本
                    errorMessage = response.statusText || errorMessage;
                }
                throw new Error(`API请求失败: ${errorMessage}`);
            }

            const data = await response.json();
            console.log(`📊 预估费用响应数据:`, data);

            if (data.success && data.estimate) {
                const estimate = data.estimate;
                document.getElementById('modify-estimate').innerHTML = `
                    <div>基础费用: ¥${estimate.base_fee || '0.00'}</div>
                    <div>距离费用: ¥${estimate.distance_fee || '0.00'}</div>
                    <div><strong>总费用: ¥${estimate.total_fee || estimate.total_money || '0.00'}</strong></div>
                `;
                confirmBtn.disabled = false;
                confirmBtn.dataset.coordinates = JSON.stringify({ longitude, latitude, address });
                console.log(`✅ 费用计算成功: ¥${estimate.total_fee || estimate.total_money}`);
            } else {
                const errorMsg = data.message || '费用计算失败';
                console.error(`❌ 费用计算失败:`, errorMsg);
                document.getElementById('modify-estimate').textContent = `费用计算失败: ${errorMsg}`;
                confirmBtn.disabled = true;
            }
        } catch (error) {
            console.error('❌ 预估修改费用失败:', error);
            document.getElementById('modify-estimate').textContent = `费用计算失败: ${error.message}`;
            confirmBtn.disabled = true;
        }
    }

    /**
     * 获取取消订单费用
     */
    async getCancelOrderFee(orderId) {
        try {
            const storeName = await this.getStoreName();
            if (!storeName) {
                throw new Error('无法获取店面名称，请确保在F6页面中使用插件');
            }

            const API_BASE_URL = 'http://localhost:5000';

            const response = await fetch(`${API_BASE_URL}/api/f6/cancel_fee`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    order_id: orderId,
                    manager_name: storeName
                })
            });

            const data = await response.json();
            const feeInfoEl = document.getElementById('cancel-fee-info');

            if (data.success && data.cancel_fee) {
                const fee = data.cancel_fee;
                feeInfoEl.innerHTML = `
                    <div>取消费用: ¥${fee.cancel_fee || '0.00'}</div>
                    <div>等待费用: ¥${fee.wait_fee || '0.00'}</div>
                    <div><strong>总费用: ¥${fee.total_cost || '0.00'}</strong></div>
                `;
            } else {
                feeInfoEl.textContent = '无需支付取消费用';
            }
        } catch (error) {
            console.error('获取取消费用失败:', error);
            document.getElementById('cancel-fee-info').textContent = `费用查询失败: ${error.message}`;
        }
    }

    /**
     * 确认修改订单
     */
    async confirmModifyOrder(orderId) {
        const newDestination = document.getElementById('new-destination').value.trim();
        const confirmBtn = document.querySelector('.btn-confirm');

        if (!newDestination) {
            alert('请输入新的目的地地址');
            return;
        }

        if (confirmBtn.disabled) {
            alert('请等待地址解析和费用计算完成');
            return;
        }

        try {
            const storeName = await this.getStoreName();
            if (!storeName) {
                throw new Error('无法获取店面名称，请确保在F6页面中使用插件');
            }

            const API_BASE_URL = 'http://localhost:5000';

            // 获取坐标信息
            const coordinatesData = confirmBtn.dataset.coordinates ? JSON.parse(confirmBtn.dataset.coordinates) : null;

            const requestData = {
                order_id: orderId,
                destination_address: newDestination,
                manager_name: storeName
            };

            // 如果有坐标信息，添加到请求中
            if (coordinatesData) {
                requestData.destination_longitude = coordinatesData.longitude;
                requestData.destination_latitude = coordinatesData.latitude;
            }

            const response = await fetch(`${API_BASE_URL}/api/f6/modify_order`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData)
            });

            const data = await response.json();
            if (data.success) {
                this.showPopupNotification('订单目的地修改成功', 'success');
                document.querySelector('.order-dialog-overlay').remove();
                // 刷新订单列表
                this.loadActiveOrders();
            } else {
                this.showPopupNotification(`修改失败: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('修改订单失败:', error);
            this.showPopupNotification(`修改订单失败: ${error.message}`, 'error');
        }
    }

    /**
     * 确认取消订单
     */
    async confirmCancelOrder(orderId) {
        try {
            const storeName = await this.getStoreName();
            if (!storeName) {
                throw new Error('无法获取店面名称，请确保在F6页面中使用插件');
            }

            const API_BASE_URL = 'http://localhost:5000';

            const response = await fetch(`${API_BASE_URL}/api/f6/cancel_order`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    order_id: orderId,
                    manager_name: storeName,
                    reason_code: "1",
                    reason_detail: "客户主动取消"
                })
            });

            const data = await response.json();
            if (data.success) {
                this.showPopupNotification('订单取消成功', 'success');
                document.querySelector('.order-dialog-overlay').remove();
                // 刷新订单列表
                this.loadActiveOrders();
            } else {
                this.showPopupNotification(`取消失败: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('取消订单失败:', error);
            this.showPopupNotification(`取消订单失败: ${error.message}`, 'error');
        }
    }

}

// 全局变量，用于HTML onclick访问
let popupManager;

// 初始化弹出窗口
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，初始化PopupManager');
    popupManager = new PopupManager();
});
