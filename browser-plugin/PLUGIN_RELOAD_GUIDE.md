# 🔄 插件重新加载指南

## 📋 问题描述
修改和取消订单页面的UI改动没有在实际插件中生效，需要强制重新加载插件。

## 🔧 解决方案

### **方法1：Chrome扩展管理页面重新加载**

1. **打开Chrome扩展管理页面**
   ```
   chrome://extensions/
   ```

2. **找到"F6 e代驾助手"插件**

3. **点击刷新按钮** 🔄
   - 在插件卡片右下角找到刷新图标
   - 点击刷新按钮重新加载插件

4. **验证版本更新**
   - 检查版本号是否变为 `1.0.1`
   - 确认"上次更新"时间是最新的

### **方法2：开发者模式重新加载**

1. **确保开发者模式已开启**
   - 在 `chrome://extensions/` 页面
   - 右上角开启"开发者模式"

2. **点击"重新加载"按钮**
   - 在插件卡片上会出现"重新加载"按钮
   - 点击重新加载

### **方法3：完全重新安装**

1. **移除旧插件**
   - 点击插件卡片上的"移除"按钮

2. **重新安装插件**
   - 点击"加载已解压的扩展程序"
   - 选择 `browser-plugin` 文件夹

## ✅ 验证步骤

### **1. 检查版本号**
- 插件版本应该显示为 `1.0.1`
- 如果还是 `1.0.0`，说明没有重新加载成功

### **2. 测试对话框样式**
- 在F6页面中打开插件
- 点击修改或取消订单按钮
- 检查以下改进：
  - ✅ 标题高度更小（不再头重脚轻）
  - ✅ 订单信息字体更大（15px）
  - ✅ 客户姓名和车牌号在同一行
  - ✅ 目的地信息和标签在同一行
  - ✅ 显示📍和💰图标

### **3. 对比测试**
- 打开测试页面验证样式：
  ```
  file:///home/<USER>/MyProj2025/browser-plugin/test-popup-dialog.html
  ```
- 对比实际插件中的对话框样式

## 🐛 故障排除

### **如果样式仍然没有生效：**

1. **清除浏览器缓存**
   ```
   Ctrl + Shift + Delete
   ```
   - 选择"缓存的图片和文件"
   - 点击"清除数据"

2. **硬刷新页面**
   ```
   Ctrl + Shift + R
   ```

3. **检查控制台错误**
   - 按 `F12` 打开开发者工具
   - 查看 Console 标签页是否有错误信息

4. **验证CSS注入**
   - 在对话框打开时，检查页面 `<head>` 中是否有：
   ```html
   <style id="dialog-styles-injected">
   ```

## 📝 技术说明

### **修改内容：**

1. **CSS样式注入**
   - 在 `popup.js` 中添加了 `injectDialogStyles()` 方法
   - 直接注入CSS样式，避免依赖外部CSS文件

2. **样式优化**
   - 标题高度：`padding: 8px 18px 6px`（原来是 `12px 18px 10px`）
   - 字体大小：订单信息从 `13px` 提升到 `15px`
   - 布局优化：客户信息和目的地信息同行显示
   - 图标添加：📍新目的地地址、💰预估费用

3. **版本更新**
   - `manifest.json` 版本号：`1.0.0` → `1.0.1`
   - CSS文件添加版本注释

## 🎯 预期效果

修改后的对话框应该：
- 标题区域更紧凑，不再头重脚轻
- 订单信息更清晰易读
- 客户信息（姓名+车牌）在同一行显示
- 目的地信息布局更合理
- 添加了直观的图标标识
- 整体视觉效果更加平衡和专业

## 📞 如果问题持续存在

请检查：
1. 浏览器版本是否支持最新的CSS特性
2. 是否有其他扩展程序冲突
3. F6页面是否有CSP（内容安全策略）限制
4. 控制台是否有JavaScript错误

---

**最后更新：** 2025-06-08 13:00  
**版本：** 1.0.1
