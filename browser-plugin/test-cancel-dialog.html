<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>取消订单对话框测试</title>
    <link rel="stylesheet" href="css/plugin.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #ff6600;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.2s;
        }
        
        .test-button:hover {
            background: #e55a00;
        }
        
        .demo-order-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            background: white;
        }
        
        .order-info {
            margin-bottom: 12px;
        }
        
        .order-route {
            color: #606266;
            font-size: 14px;
            margin: 8px 0;
        }
        
        .order-status {
            color: #67c23a;
            font-weight: 500;
        }
        
        .action-btn {
            background: #f56c6c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>取消订单对话框测试</h1>
        <p>这个页面用于测试美化后的取消订单对话框功能。</p>
        
        <h2>功能特点</h2>
        <ul>
            <li>✅ 显示完整的订单信息（订单号、客户、车牌、状态、目的地）</li>
            <li>✅ 与修改订单页面保持一致的设计风格</li>
            <li>✅ 更友好的背景透明度，不会遮挡通知</li>
            <li>✅ 调用中间件API获取取消费用信息</li>
            <li>✅ 响应式设计，适配不同屏幕尺寸</li>
        </ul>
        
        <h2>测试订单</h2>
        <div class="demo-order-card">
            <div class="order-info">
                <div><strong>张先生</strong> · 粤B12345</div>
                <div class="order-route">
                    <span>🚗 深圳市福田区华强北</span>
                    <span>🎯 深圳市南山区科技园南区</span>
                </div>
                <div class="order-status">司机接单</div>
            </div>
            <button class="action-btn" onclick="showTestCancelDialog()">❌ 取消订单</button>
        </div>
        
        <h2>测试按钮</h2>
        <button class="test-button" onclick="showTestCancelDialog()">显示取消订单对话框</button>
        <button class="test-button" onclick="showTestNotification()">测试通知显示</button>
        <button class="test-button" onclick="showTestCancelDialogWithFee()">显示带费用的取消对话框</button>
    </div>

    <script>
        // 模拟订单信息
        const mockOrderInfo = {
            customer_name: '张先生',
            plate_number: '粤B12345',
            destination_address: '深圳市南山区科技园南区深南大道10000号',
            status_name: '司机接单'
        };

        // 显示测试取消对话框
        function showTestCancelDialog() {
            const orderId = 'TEST_ORDER_' + Date.now();
            
            const dialog = document.createElement('div');
            dialog.className = 'order-dialog-overlay';
            
            dialog.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-color: rgba(255, 255, 255, 0.1) !important;
                backdrop-filter: blur(1px) !important;
                z-index: 2147483647 !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                margin: 0 !important;
                padding: 0 !important;
                box-sizing: border-box !important;
            `;

            dialog.innerHTML = `
                <div class="order-dialog">
                    <div class="dialog-header">
                        <h3>取消订单</h3>
                        <button class="close-btn" onclick="closeDialog()">×</button>
                    </div>
                    <div class="dialog-body">
                        <!-- 订单信息展示 - 仿订单卡片风格 -->
                        <div class="order-info-section">
                            <h4 style="margin: 0 0 8px 0; color: #303133; font-size: 13px; font-weight: 600;">📋 订单信息</h4>
                            <div class="order-info-card">
                                <div class="order-card-header">
                                    <div class="order-card-id">${orderId}</div>
                                    <div class="order-card-status">${mockOrderInfo.status_name}</div>
                                </div>
                                <div class="order-card-info">
                                    <div class="order-card-customer"><strong>${mockOrderInfo.customer_name}</strong> · ${mockOrderInfo.plate_number}</div>
                                    <div class="order-card-route">
                                        <span>🎯 目的地</span>
                                        <span class="route-icon">→</span>
                                        <span>${mockOrderInfo.destination_address}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 取消确认 -->
                        <div class="cancel-section">
                            <h4 style="margin: 0 0 8px 0; color: #303133; font-size: 13px; font-weight: 600;">⚠️ 取消确认</h4>
                            <div class="warning-message" style="padding: 12px; background: #fef0f0; border: 1px solid #fbc4c4; border-radius: 6px; margin-bottom: 16px;">
                                <p style="margin: 0 0 8px 0; color: #f56c6c; font-weight: 500;">确定要取消这个订单吗？</p>
                                <p style="margin: 0; color: #909399; font-size: 12px;">取消后订单将无法恢复，可能产生相应费用</p>
                            </div>
                            
                            <!-- 取消费用信息 -->
                            <div class="fee-section">
                                <h5 style="margin: 0 0 8px 0; color: #606266; font-size: 13px; font-weight: 500;">💰 费用信息</h5>
                                <div id="cancel-fee-info" class="fee-info" style="padding: 8px 12px; background: #f8f9fa; border-radius: 4px; border: 1px solid #e9ecef; font-size: 12px; color: #606266;">正在查询取消费用...</div>
                            </div>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button class="btn-cancel" onclick="closeDialog()">不取消</button>
                        <button class="btn-confirm btn-danger" onclick="confirmCancel()">确认取消</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);
            
            // 模拟获取取消费用
            setTimeout(() => {
                const feeInfo = document.getElementById('cancel-fee-info');
                if (feeInfo) {
                    feeInfo.innerHTML = `
                        <div>取消费用: ¥5.00</div>
                        <div>等待费用: ¥3.00</div>
                        <div><strong>总费用: ¥8.00</strong></div>
                    `;
                }
            }, 1000);
        }

        // 显示带费用的取消对话框
        function showTestCancelDialogWithFee() {
            showTestCancelDialog();
            setTimeout(() => {
                const feeInfo = document.getElementById('cancel-fee-info');
                if (feeInfo) {
                    feeInfo.innerHTML = `
                        <div style="color: #67c23a;">✅ 无需支付取消费用</div>
                        <div style="font-size: 11px; color: #909399; margin-top: 4px;">订单状态允许免费取消</div>
                    `;
                }
            }, 500);
        }

        // 关闭对话框
        function closeDialog() {
            const dialog = document.querySelector('.order-dialog-overlay');
            if (dialog) {
                dialog.remove();
            }
        }

        // 确认取消
        function confirmCancel() {
            showTestNotification('订单取消成功', 'success');
            closeDialog();
        }

        // 显示测试通知
        function showTestNotification(message = '这是一个测试通知', type = 'info') {
            // 移除现有通知
            const existingNotification = document.getElementById('popup-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // 创建通知元素
            const notification = document.createElement('div');
            notification.id = 'popup-notification';
            notification.style.cssText = `
                position: fixed;
                top: 50px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#F44336' : type === 'warning' ? '#FF9800' : '#2196F3'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 2147483648;
                box-shadow: 0 4px 16px rgba(0,0,0,0.3);
                animation: slideInDown 0.3s ease-out;
                max-width: 280px;
                text-align: center;
                line-height: 1.4;
                white-space: pre-wrap;
            `;
            notification.textContent = message;

            // 添加CSS动画
            if (!document.getElementById('popup-notification-styles')) {
                const style = document.createElement('style');
                style.id = 'popup-notification-styles';
                style.textContent = `
                    @keyframes slideInDown {
                        from {
                            opacity: 0;
                            transform: translateX(-50%) translateY(-20px);
                        }
                        to {
                            opacity: 1;
                            transform: translateX(-50%) translateY(0);
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // 自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeDialog();
            }
        });
    </script>
</body>
</html>
