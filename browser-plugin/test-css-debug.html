<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS调试测试</title>
    <link rel="stylesheet" href="css/plugin.css?v=20250608_125000">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #ff6600;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.2s;
        }
        
        .test-button:hover {
            background: #e55a00;
        }

        /* 调试样式 */
        .debug-info {
            background: #e8f4fd;
            border: 1px solid #b3d8ff;
            border-radius: 6px;
            padding: 12px;
            margin: 16px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CSS调试测试页面</h1>
        <p>这个页面用于调试CSS样式是否正确加载和应用。</p>
        
        <div class="debug-info">
            <strong>CSS版本检查:</strong><br>
            如果CSS正确加载，下面的对话框标题应该有较小的padding（8px而不是12px）
        </div>
        
        <h2>测试按钮</h2>
        <button class="test-button" onclick="showDebugDialog()">显示调试对话框</button>
        <button class="test-button" onclick="checkCSSVersion()">检查CSS版本</button>
    </div>

    <script>
        // 检查CSS版本
        function checkCSSVersion() {
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            let cssInfo = 'CSS文件加载情况:\n';
            links.forEach((link, index) => {
                cssInfo += `${index + 1}. ${link.href}\n`;
            });
            
            // 检查特定样式是否应用
            const testDiv = document.createElement('div');
            testDiv.className = 'dialog-header';
            testDiv.style.visibility = 'hidden';
            testDiv.style.position = 'absolute';
            document.body.appendChild(testDiv);
            
            const computedStyle = window.getComputedStyle(testDiv);
            const padding = computedStyle.padding;
            
            document.body.removeChild(testDiv);
            
            cssInfo += `\n.dialog-header padding: ${padding}`;
            cssInfo += `\n期望值: 8px 18px 6px (新版本) 或 12px 18px 10px (旧版本)`;
            
            alert(cssInfo);
        }

        // 显示调试对话框
        function showDebugDialog() {
            const dialog = document.createElement('div');
            dialog.className = 'order-dialog-overlay';
            
            dialog.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-color: rgba(255, 255, 255, 0.1) !important;
                backdrop-filter: blur(1px) !important;
                z-index: 2147483647 !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                margin: 0 !important;
                padding: 0 !important;
                box-sizing: border-box !important;
            `;

            dialog.innerHTML = `
                <div class="order-dialog">
                    <div class="dialog-header">
                        <h3>CSS调试对话框</h3>
                        <button class="close-btn" onclick="closeDebugDialog()">×</button>
                    </div>
                    <div class="dialog-body">
                        <div class="order-info-section">
                            <div class="order-info-simple">
                                <div class="order-info-item">
                                    <span class="order-info-label">测试项目：</span>
                                    <span class="order-info-value">CSS样式应用测试</span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">标题高度：</span>
                                    <div class="order-info-customer">
                                        <span class="customer-name">应该比之前更小</span>
                                        <span class="customer-plate">padding: 8px 18px 6px</span>
                                    </div>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">字体大小：</span>
                                    <div class="destination-address">订单信息字体应该是15px</div>
                                </div>
                            </div>
                        </div>

                        <div class="modify-section">
                            <div class="form-group">
                                <label data-icon="location">测试标签</label>
                                <div class="address-input-container">
                                    <input type="text" placeholder="测试输入框" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label data-icon="money">图标测试</label>
                                <div class="estimate-display">应该显示📍和💰图标</div>
                            </div>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button class="btn-cancel" onclick="closeDebugDialog()">关闭</button>
                        <button class="btn-confirm">确认</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);
        }

        // 关闭调试对话框
        function closeDebugDialog() {
            const dialog = document.querySelector('.order-dialog-overlay');
            if (dialog) {
                dialog.remove();
            }
        }

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeDebugDialog();
            }
        });
    </script>
</body>
</html>
