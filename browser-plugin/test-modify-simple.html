<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改目的地对话框测试</title>
    <link rel="stylesheet" href="css/plugin.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #ff6600;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.2s;
        }
        
        .test-button:hover {
            background: #e55a00;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>修改目的地对话框测试</h1>
        <p>这个页面用于测试优化后的修改目的地对话框功能。</p>
        
        <h2>优化特点</h2>
        <ul>
            <li>✅ 缩小标题高度，避免头重脚轻</li>
            <li>✅ 按照订单监控卡片风格显示订单信息，节省空间</li>
            <li>✅ 去掉黑色背景，能看到订单监控页面</li>
            <li>✅ 确保确认按钮完全显示</li>
            <li>✅ 优化布局和间距</li>
        </ul>
        
        <h2>测试按钮</h2>
        <button class="test-button" onclick="showTestModifyDialog()">显示修改目的地对话框</button>
        <button class="test-button" onclick="showTestNotification()">测试通知显示</button>
    </div>

    <script>
        // 模拟订单信息
        const mockOrderInfo = {
            customer_name: '张先生',
            plate_number: '粤B12345',
            destination_address: '深圳市南山区科技园南区深南大道10000号',
            status_name: '司机接单'
        };

        // 显示测试修改对话框
        function showTestModifyDialog() {
            const orderId = 'TEST_ORDER_' + Date.now();
            
            const dialog = document.createElement('div');
            dialog.className = 'order-dialog-overlay';
            
            dialog.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-color: rgba(255, 255, 255, 0.1) !important;
                backdrop-filter: blur(1px) !important;
                z-index: 2147483647 !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                margin: 0 !important;
                padding: 0 !important;
                box-sizing: border-box !important;
            `;

            dialog.innerHTML = `
                <div class="order-dialog">
                    <div class="dialog-header">
                        <h3>修改目的地</h3>
                        <button class="close-btn" onclick="closeDialog()">×</button>
                    </div>
                    <div class="dialog-body">
                        <!-- 订单信息展示 - 仿订单卡片风格 -->
                        <div class="order-info-section">
                            <h4 style="margin: 0 0 8px 0; color: #303133; font-size: 13px; font-weight: 600;">📋 订单信息</h4>
                            <div class="order-info-card">
                                <div class="order-card-header">
                                    <div class="order-card-id">${orderId}</div>
                                    <div class="order-card-status">${mockOrderInfo.status_name}</div>
                                </div>
                                <div class="order-card-info">
                                    <div class="order-card-customer"><strong>${mockOrderInfo.customer_name}</strong> · ${mockOrderInfo.plate_number}</div>
                                    <div class="order-card-route">
                                        <span>🎯 当前目的地</span>
                                        <span class="route-icon">→</span>
                                        <span>${mockOrderInfo.destination_address}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 修改目的地 -->
                        <div class="modify-section">
                            <h4 style="margin: 0 0 8px 0; color: #303133; font-size: 13px; font-weight: 600;">📍 修改目的地</h4>
                            <div class="form-group">
                                <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #606266; font-size: 13px;">新目的地地址：</label>
                                <div class="address-input-container">
                                    <input type="text" id="new-destination" placeholder="请输入新的目的地地址" autocomplete="off" style="width: 100%; padding: 8px 12px; border: 1px solid #dcdfe6; border-radius: 4px; font-size: 13px; box-sizing: border-box;" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #606266; font-size: 13px;">预估费用：</label>
                                <div id="modify-estimate" class="estimate-display" style="padding: 8px 12px; background: #f8f9fa; border-radius: 4px; border: 1px solid #e9ecef; font-size: 12px; color: #606266;">输入地址后自动计算</div>
                            </div>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button class="btn-cancel" onclick="closeDialog()">取消</button>
                        <button class="btn-confirm" onclick="confirmModify()" disabled>确认修改</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);
            
            // 模拟地址输入和费用计算
            const addressInput = dialog.querySelector('#new-destination');
            const confirmBtn = dialog.querySelector('.btn-confirm');
            const estimateDiv = dialog.querySelector('#modify-estimate');
            
            addressInput.addEventListener('input', () => {
                const value = addressInput.value.trim();
                if (value.length > 3) {
                    estimateDiv.textContent = '计算中...';
                    setTimeout(() => {
                        estimateDiv.innerHTML = `
                            <div>基础费用: ¥15.00</div>
                            <div>距离费用: ¥8.50</div>
                            <div><strong>总费用: ¥23.50</strong></div>
                        `;
                        confirmBtn.disabled = false;
                    }, 1000);
                } else {
                    estimateDiv.textContent = '输入地址后自动计算';
                    confirmBtn.disabled = true;
                }
            });
        }

        // 关闭对话框
        function closeDialog() {
            const dialog = document.querySelector('.order-dialog-overlay');
            if (dialog) {
                dialog.remove();
            }
        }

        // 确认修改
        function confirmModify() {
            showTestNotification('目的地修改成功', 'success');
            closeDialog();
        }

        // 显示测试通知
        function showTestNotification(message = '这是一个测试通知', type = 'info') {
            // 移除现有通知
            const existingNotification = document.getElementById('popup-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // 创建通知元素
            const notification = document.createElement('div');
            notification.id = 'popup-notification';
            notification.style.cssText = `
                position: fixed;
                top: 50px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#F44336' : type === 'warning' ? '#FF9800' : '#2196F3'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 2147483648;
                box-shadow: 0 4px 16px rgba(0,0,0,0.3);
                animation: slideInDown 0.3s ease-out;
                max-width: 280px;
                text-align: center;
                line-height: 1.4;
                white-space: pre-wrap;
            `;
            notification.textContent = message;

            // 添加CSS动画
            if (!document.getElementById('popup-notification-styles')) {
                const style = document.createElement('style');
                style.id = 'popup-notification-styles';
                style.textContent = `
                    @keyframes slideInDown {
                        from {
                            opacity: 0;
                            transform: translateX(-50%) translateY(-20px);
                        }
                        to {
                            opacity: 1;
                            transform: translateX(-50%) translateY(0);
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // 自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeDialog();
            }
        });
    </script>
</body>
</html>
