/* F6 e代驾插件样式 - 匹配F6系统风格 */
/* 版本: 1.0.1 - UI优化版本 - 2025-06-08 12:50 */
/* 修改: 缩小对话框标题高度，优化订单信息显示 */

/* e代驾按钮样式 - F6风格 */
.edriver-btn {
    background: #ff6600;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(255, 102, 0, 0.3);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.edriver-btn:hover {
    background: #e55a00;
    box-shadow: 0 2px 6px rgba(255, 102, 0, 0.4);
}

.edriver-btn:active {
    background: #cc5200;
    transform: translateY(1px);
}

.edriver-btn-primary {
    background: #ff6600;
}

.edriver-btn-primary:hover {
    background: #e55a00;
}

.edriver-btn-info {
    background: #409eff;
    box-shadow: 0 1px 3px rgba(64, 158, 255, 0.3);
}

.edriver-btn-info:hover {
    background: #3a8ee6;
    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.4);
}

.edriver-btn-warning {
    background: #e6a23c;
    box-shadow: 0 1px 3px rgba(230, 162, 60, 0.3);
}

.edriver-btn-warning:hover {
    background: #cf9236;
    box-shadow: 0 2px 6px rgba(230, 162, 60, 0.4);
}

/* 订单详情模态框样式 - F6风格 */
.edriver-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.edriver-modal-content {
    background: white;
    border-radius: 6px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    max-width: 700px;
    width: 90%;
    max-height: 85vh;
    overflow: hidden;
    animation: modalSlideIn 0.25s ease-out;
    border: 1px solid #e4e7ed;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.edriver-modal-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    color: #303133;
}

.edriver-modal-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

.edriver-modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #909399;
    cursor: pointer;
    padding: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
}

.edriver-modal-close:hover {
    background-color: #f5f7fa;
    color: #606266;
}

.edriver-modal-body {
    padding: 10px;
    max-height: calc(85vh - 120px);
    overflow-y: auto;
}

/* 订单项样式 - F6风格 */
.edriver-order-item {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 12px;
    background: white;
    transition: all 0.2s ease;
}

.edriver-order-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border-color: #c0c4cc;
}

.edriver-order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f2f5;
}

.edriver-order-id {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
}

.edriver-status-badge {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
    color: white;
}

.edriver-status-pending { background-color: #e6a23c; }
.edriver-status-accepted { background-color: #409eff; }
.edriver-status-in_progress { background-color: #67c23a; }
.edriver-status-completed { background-color: #67c23a; }
.edriver-status-cancelled { background-color: #909399; }
.edriver-status-failed { background-color: #f56c6c; }

.edriver-order-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 12px;
}

.edriver-info-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #666;
}

.edriver-info-icon {
    margin-right: 6px;
    width: 16px;
    color: #999;
}

.edriver-price {
    font-size: 16px;
    font-weight: 600;
    color: #4caf50;
    text-align: right;
}

.edriver-time {
    font-size: 12px;
    color: #999;
    text-align: right;
}

/* 空状态样式 */
.edriver-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.edriver-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* 加载状态 */
.edriver-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.edriver-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff6600;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 - F6风格 */
.edriver-error {
    text-align: center;
    padding: 20px;
    color: #f56c6c;
    background-color: #fef0f0;
    border-radius: 6px;
    border: 1px solid #fde2e2;
    margin: 10px 0;
}

/* 成功提示 - F6风格 */
.edriver-success {
    text-align: center;
    padding: 20px;
    color: #67c23a;
    background-color: #f0f9ff;
    border-radius: 6px;
    border: 1px solid #e1f3d8;
    margin: 10px 0;
}

/* 订单表单样式 - F6风格 */
.edriver-form-group {
    margin-bottom: 16px;
}

.edriver-form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #606266;
    font-size: 13px;
}

.edriver-form-input,
.edriver-form-select,
.edriver-form-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.2s;
    box-sizing: border-box;
    background: white;
    color: #303133;
}

.edriver-form-input:focus,
.edriver-form-select:focus,
.edriver-form-textarea:focus {
    outline: none;
    border-color: #ff6600;
    box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.1);
}

.edriver-form-textarea {
    resize: vertical;
    min-height: 60px;
    font-family: inherit;
}

/* 文字链接样式 - 完全匹配F6系统操作链接风格 */
.edriver-text-link {
    color: #409eff !important;
    text-decoration: none !important;
    font-size: 12px !important;
    font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
    cursor: pointer;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    transition: all 0.3s !important;
    display: inline-block !important;
    font-weight: normal !important;
    margin: 0 !important;
}

.edriver-text-link:hover {
    background: #ecf5ff !important;
    color: #66b1ff !important;
    text-decoration: none !important;
}

.edriver-text-link:active {
    color: #3a8ee6 !important;
}

/* 订单操作按钮样式 */
.order-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(1.1);
}

.modify-btn:hover {
    background-color: rgba(64, 158, 255, 0.1);
}

.cancel-btn:hover {
    background-color: rgba(245, 108, 108, 0.1);
}

/* 订单对话框样式 - 适配popup窗口 */
.order-dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(1px);
    z-index: 999999;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.2s ease-out;
    /* 确保在popup环境中正确显示 */
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.order-dialog {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    /* 适配popup窗口尺寸 */
    width: 380px;
    max-width: 90%;
    max-height: 480px;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
    /* 重置用户选择限制，允许对话框内容被选择 */
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    /* 确保对话框在popup中正确定位 */
    position: relative;
    margin: 10px auto;
    display: flex;
    flex-direction: column;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dialog-header {
    padding: 8px 18px 6px; /* 缩小标题高度 */
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border-radius: 18px 18px 0 0;
    color: white;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.dialog-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.3px;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    font-size: 20px;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dialog-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    background: rgba(255, 255, 255, 0.5);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #606266;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(220, 223, 230, 0.6);
    border-radius: 12px;
    font-size: 13px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    /* 确保输入框可以被选择和输入 */
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    pointer-events: auto;
}

.form-group input:focus {
    outline: none;
    border-color: #ff6600;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
}

.estimate-display {
    padding: 12px 16px;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    font-size: 14px;
    color: #606266;
    min-height: 20px;
}

.warning-message {
    text-align: center;
    color: #e6a23c;
}

.warning-message p {
    font-size: 16px;
    margin: 16px 0;
    color: #303133;
}

.fee-info {
    margin-top: 16px;
    padding: 12px;
    background: #fdf6ec;
    border: 1px solid #f5dab1;
    border-radius: 6px;
    font-size: 14px;
    color: #e6a23c;
}

/* 订单信息样式 - 参考F6页面风格 */
.order-info-section {
    margin-bottom: 16px;
}

.order-info-simple {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 18px;
    margin-bottom: 16px;
    line-height: 1.6;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.order-info-item {
    margin-bottom: 6px;
    font-size: 15px;
    color: #303133;
    display: flex;
    align-items: flex-start;
    line-height: 1.4;
}

.order-info-item:last-child {
    margin-bottom: 0;
}

.order-info-label {
    color: #4a5568;
    font-weight: 600;
    min-width: 80px;
    font-size: 15px;
    flex-shrink: 0;
}

.order-info-value {
    color: #2d3748;
    font-weight: 500;
    font-size: 15px;
    flex: 1;
}

.order-info-customer {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.destination-address {
    color: #2d3748;
    word-break: break-all;
    line-height: 1.4;
    flex: 1;
}

.customer-name {
    font-weight: 600;
    color: #303133;
    font-size: 15px;
}

.customer-plate {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 6px rgba(72, 187, 120, 0.3);
}

/* 取消确认区域样式 */
.cancel-section {
    margin-bottom: 16px;
}

.cancel-section .warning-message {
    text-align: left;
    padding: 16px;
    background: linear-gradient(135deg, rgba(254, 240, 240, 0.9), rgba(252, 196, 196, 0.3));
    border: 1px solid rgba(251, 196, 196, 0.6);
    border-radius: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 16px rgba(245, 108, 108, 0.1);
}

.cancel-section .warning-message p {
    margin: 0;
    font-size: 13px;
}

.cancel-section .warning-message p:first-child {
    color: #e53e3e;
    font-weight: 600;
    margin-bottom: 8px;
}

.cancel-section .warning-message p:last-child {
    color: #718096;
    font-size: 12px;
}

/* 修改目的地区域样式 */
.modify-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.modify-section .form-group {
    margin-bottom: 14px;
}

.modify-section .form-group:last-child {
    margin-bottom: 0;
}

.modify-section .form-group label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 600;
    color: #4a5568;
    font-size: 15px;
    gap: 6px;
}

.modify-section .form-group label::before {
    font-size: 16px;
}

.modify-section .form-group label[data-icon="location"]::before {
    content: "📍";
}

.modify-section .form-group label[data-icon="money"]::before {
    content: "💰";
}

.estimate-display {
    padding: 12px 16px;
    background: rgba(248, 249, 250, 0.9);
    border-radius: 12px;
    border: 1px solid rgba(233, 236, 239, 0.8);
    font-size: 14px;
    color: #4a5568;
    line-height: 1.5;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* 费用区域样式 */
.fee-section h5 {
    margin: 0 0 8px 0;
    color: #4a5568;
    font-size: 13px;
    font-weight: 600;
}

.dialog-footer {
    padding: 16px 20px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: none;
    background: rgba(255, 255, 255, 0.5);
    flex-shrink: 0;
    border-radius: 0 0 18px 18px;
}

.btn-cancel,
.btn-confirm {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 90px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn-cancel {
    background: rgba(255, 255, 255, 0.8);
    color: #606266;
    border: 1px solid rgba(220, 223, 230, 0.6);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.btn-cancel:hover {
    background: rgba(236, 245, 255, 0.9);
    color: #409eff;
    border-color: rgba(198, 226, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.btn-confirm {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-confirm:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%) !important;
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4) !important;
}

.btn-danger:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(245, 101, 101, 0.6) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .edriver-modal-content {
        width: 95%;
        margin: 10px;
    }

    .edriver-order-info {
        grid-template-columns: 1fr;
    }

    .edriver-order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .edriver-price,
    .edriver-time {
        text-align: left;
    }

    .order-dialog {
        width: 95%;
        margin: 20px;
    }

    .dialog-header,
    .dialog-body,
    .dialog-footer {
        padding: 16px;
    }

    .dialog-footer {
        flex-direction: column;
    }

    .btn-cancel,
    .btn-confirm {
        width: 100%;
    }

    /* 响应式订单信息网格 */
    .order-info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .info-label {
        margin-bottom: 4px;
        min-width: auto;
    }

    .info-value {
        margin-left: 0;
    }
}

/* Bootstrap兼容样式 - 确保地址建议按钮正确显示 */
.btn-outline-info {
    color: #17a2b8;
    border-color: #17a2b8;
    background-color: transparent;
    padding: 6px 12px;
    border: 1px solid #17a2b8;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-outline-info.active,
.btn-outline-info:active {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

/* 地址建议相关样式 */
.address-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.address-suggestions.hidden {
    display: none;
}

.suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-main {
    font-weight: 500;
    color: #303133;
    font-size: 13px;
}

.suggestion-detail {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
}
