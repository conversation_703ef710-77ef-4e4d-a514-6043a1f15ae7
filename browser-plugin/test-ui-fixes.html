<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI修复测试 - 地址建议图标和取消页面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .test-button {
            background: #ff6600;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
        }

        .test-button:hover {
            background: #e55a00;
            transform: translateY(-2px);
        }

        /* 复制popup.html中的对话框样式 */
        .order-dialog-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            z-index: 2147483647 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 !important;
            padding: 20px !important;
            box-sizing: border-box !important;
            animation: fadeIn 0.3s ease-out !important;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .order-dialog {
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 20px !important;
            padding: 0 !important;
            max-width: 500px !important;
            width: 90% !important;
            max-height: 80vh !important;
            overflow: hidden !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
            backdrop-filter: blur(20px) !important;
            -webkit-backdrop-filter: blur(20px) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            animation: slideIn 0.3s ease-out !important;
            display: flex !important;
            flex-direction: column !important;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px) scale(0.9);
                opacity: 0;
            }
            to {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .dialog-header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
            color: white !important;
            padding: 20px 24px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        .dialog-header h3 {
            margin: 0 !important;
            font-size: 18px !important;
            font-weight: 700 !important;
        }

        .close-btn {
            background: none !important;
            border: none !important;
            color: white !important;
            font-size: 24px !important;
            cursor: pointer !important;
            padding: 0 !important;
            width: 30px !important;
            height: 30px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.2s !important;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2) !important;
        }

        .dialog-body {
            padding: 24px !important;
            flex: 1 !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
        }

        .dialog-footer {
            padding: 20px 24px !important;
            display: flex !important;
            gap: 12px !important;
            justify-content: flex-end !important;
            background: rgba(0, 0, 0, 0.02) !important;
            border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
            flex-shrink: 0 !important;
        }

        /* 地址建议开关样式 - 使用小灯泡图标 */
        .address-suggestion-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 6px 8px;
            background: #e8f4fd;
            border-radius: 6px;
            border: 1px solid #b3d8ff;
        }

        .toggle-label {
            font-size: 12px;
            color: #409eff;
            font-weight: 500;
        }

        .toggle-switch {
            position: relative;
            width: 32px;
            height: 24px;
            background: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #606266;
        }

        .toggle-switch.active {
            background: #17a2b8;
            border-color: #17a2b8;
            color: white;
        }

        .toggle-switch i {
            font-size: 12px;
            transition: all 0.3s;
        }

        .btn-cancel, .btn-confirm {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 70px;
        }

        .btn-cancel {
            background: #f5f7fa;
            color: #606266;
            border: 1px solid #dcdfe6;
        }

        .btn-cancel:hover {
            background: #ecf5ff;
            color: #409eff;
            border-color: #b3d8ff;
        }

        .btn-confirm {
            background: #ff6600;
            color: white;
        }

        .btn-confirm:hover:not(:disabled) {
            background: #e55a00;
        }

        .btn-confirm:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .btn-danger {
            background: #f56c6c;
            color: white;
        }

        .btn-danger:hover {
            background: #f78989;
        }

        /* 订单信息样式 */
        .order-info-section {
            margin-bottom: 16px;
        }

        .order-info-simple {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            border: 1px solid #e9ecef;
        }

        .order-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .order-info-item:last-child {
            margin-bottom: 0;
        }

        .order-info-label {
            font-weight: 500;
            color: #606266;
            min-width: 60px;
        }

        .order-info-value {
            color: #303133;
        }

        .order-info-customer {
            display: flex;
            gap: 8px;
        }

        .customer-name {
            color: #303133;
            font-weight: 500;
        }

        .customer-plate {
            color: #409eff;
            background: #ecf5ff;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }

        .destination-address {
            color: #303133;
        }

        .modify-section {
            margin-top: 16px;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #606266;
            font-size: 13px;
        }

        .address-input-container {
            position: relative;
        }

        .address-input-container input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 13px;
            box-sizing: border-box;
        }

        .estimate-display {
            background: white;
            border: 2px solid #e4e7ed;
            border-radius: 6px;
            padding: 10px;
            font-size: 13px;
            color: #606266;
            min-height: 16px;
            line-height: 1.3;
        }

        .fee-section {
            margin-bottom: 12px;
        }

        .fee-info {
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            font-size: 12px;
            color: #606266;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 UI修复测试</h1>
        <p>测试修复后的地址建议图标和取消页面布局</p>
        
        <h2>✅ 修复内容</h2>
        <ul>
            <li><strong>问题1：</strong>修改目的地页面的地址建议图标改为小灯泡（与下单页面保持一致）</li>
            <li><strong>问题2：</strong>取消页面去掉中间的"确认取消"消息，只保留费用信息</li>
            <li><strong>问题3：</strong>确保确认按钮完全可见（修复对话框布局）</li>
        </ul>
        
        <h2>🧪 测试按钮</h2>
        <button class="test-button" onclick="showModifyDialog()">测试修改目的地对话框</button>
        <button class="test-button" onclick="showCancelDialog()">测试取消订单对话框</button>
    </div>

    <script>
        // 模拟订单信息
        const mockOrderInfo = {
            order_id: 'EDJ202506082000',
            customer_name: '张先生',
            plate_number: '粤B12345',
            destination_address: '深圳市南山区科技园南区深南大道10000号',
            status: '301',
            status_name: '司机接单'
        };

        // 显示修改目的地对话框
        function showModifyDialog() {
            const dialog = document.createElement('div');
            dialog.className = 'order-dialog-overlay';
            dialog.innerHTML = `
                <div class="order-dialog">
                    <div class="dialog-header">
                        <h3>修改目的地</h3>
                        <button class="close-btn" onclick="closeDialog()">×</button>
                    </div>
                    <div class="dialog-body">
                        <!-- 订单信息展示 -->
                        <div class="order-info-section">
                            <div class="order-info-simple">
                                <div class="order-info-item">
                                    <span class="order-info-label">订单号：</span>
                                    <span class="order-info-value">${mockOrderInfo.order_id}</span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">客户：</span>
                                    <div class="order-info-customer">
                                        <span class="customer-name">${mockOrderInfo.customer_name}</span>
                                        <span class="customer-plate">${mockOrderInfo.plate_number}</span>
                                    </div>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">当前目的地：</span>
                                    <div class="destination-address">${mockOrderInfo.destination_address}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 修改目的地 -->
                        <div class="modify-section">
                            <div class="form-group">
                                <label data-icon="location">新目的地地址</label>
                                <!-- 地址建议开关 - 使用小灯泡图标 -->
                                <div class="address-suggestion-toggle">
                                    <span class="toggle-label">地址建议</span>
                                    <div class="toggle-switch active" onclick="toggleAddressSuggestion(this)">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                </div>
                                <div class="address-input-container">
                                    <input type="text" placeholder="请输入新的目的地地址" autocomplete="off" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label data-icon="money">预估费用</label>
                                <div class="estimate-display">输入地址后自动计算</div>
                            </div>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button class="btn-cancel" onclick="closeDialog()">取消</button>
                        <button class="btn-confirm" disabled>确认修改</button>
                    </div>
                </div>
            `;
            document.body.appendChild(dialog);
        }

        // 显示取消订单对话框
        function showCancelDialog() {
            const dialog = document.createElement('div');
            dialog.className = 'order-dialog-overlay';
            dialog.innerHTML = `
                <div class="order-dialog">
                    <div class="dialog-header">
                        <h3>取消订单</h3>
                        <button class="close-btn" onclick="closeDialog()">×</button>
                    </div>
                    <div class="dialog-body">
                        <!-- 订单信息展示 -->
                        <div class="order-info-section">
                            <div class="order-info-simple">
                                <div class="order-info-item">
                                    <span class="order-info-label">订单号：</span>
                                    <span class="order-info-value">${mockOrderInfo.order_id}</span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">客户：</span>
                                    <div class="order-info-customer">
                                        <span class="customer-name">${mockOrderInfo.customer_name}</span>
                                        <span class="customer-plate">${mockOrderInfo.plate_number}</span>
                                    </div>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">目的地：</span>
                                    <div class="destination-address">${mockOrderInfo.destination_address}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 取消费用信息 - 去掉了"确认取消"消息 -->
                        <div class="cancel-section">
                            <div class="fee-section">
                                <h5 style="margin: 0 0 8px 0; color: #606266; font-size: 13px; font-weight: 500;">💰 费用信息</h5>
                                <div class="fee-info">
                                    <div>取消费用: ¥0.00</div>
                                    <div>等待费用: ¥0.00</div>
                                    <div><strong>总费用: ¥0.00</strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button class="btn-cancel" onclick="closeDialog()">不取消</button>
                        <button class="btn-confirm btn-danger">确认取消</button>
                    </div>
                </div>
            `;
            document.body.appendChild(dialog);
        }

        // 切换地址建议
        function toggleAddressSuggestion(element) {
            element.classList.toggle('active');
            console.log('地址建议切换:', element.classList.contains('active') ? '开启' : '关闭');
        }

        // 关闭对话框
        function closeDialog() {
            const dialog = document.querySelector('.order-dialog-overlay');
            if (dialog) {
                dialog.remove();
            }
        }
    </script>
</body>
</html>
