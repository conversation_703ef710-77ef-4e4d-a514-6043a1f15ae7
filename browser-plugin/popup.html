<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>F6 e代驾助手</title>
    <style>
        body {
            width: 420px;  /* 缩小宽度，让界面更精致 */
            height: 580px;  /* 符合Chrome扩展popup最大高度限制 */
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #303133;
            overflow: hidden;  /* 禁用滚动条 */
            position: relative;  /* 为对话框提供定位上下文 */
            display: flex;
            flex-direction: column;
        }

        .header {
            padding: 12px 16px;
            text-align: center;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            position: relative;
            box-shadow: 0 6px 25px rgba(255, 107, 53, 0.4);
            border-radius: 0 0 20px 20px;
            margin-bottom: 10px;
            flex-shrink: 0;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255,255,255,0.2);
        }

        .header h1 {
            margin: 0;
            font-size: 17px;
            font-weight: 800;
            text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.8px;
            background: linear-gradient(45deg, #ffffff, #fff8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .header p {
            margin: 6px 0 0 0;
            font-size: 13px;
            opacity: 0.95;
            font-weight: 400;
        }

        .content {
            padding: 0 16px 10px 16px;
            background: transparent;
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 18px;
            padding: 14px 16px;
            margin-bottom: 10px;
            border: none;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            flex-shrink: 0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
            padding: 6px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .status-item:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .status-label {
            color: #4a5568;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-value {
            font-size: 13px;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .status-success {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
        }

        .status-error {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
            border: none;
            box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
        }

        .status-warning {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
            border: none;
            box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
        }

        .config-section {
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 18px;
            padding: 14px 16px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .config-title {
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 6px;
            padding-bottom: 4px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
        }

        .input-group {
            margin-bottom: 12px;
        }

        .input-label {
            display: block;
            font-size: 12px;
            margin-bottom: 6px;
            color: #606266;
            font-weight: 500;
        }

        .input-field {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: white;
            color: #303133;
            font-size: 12px;
            box-sizing: border-box;
            transition: border-color 0.2s;
        }

        .input-field:focus {
            outline: none;
            border-color: #ff6600;
            box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.1);
        }

        .btn {
            padding: 8px 14px;
            border: none;
            border-radius: 20px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-right: 6px;
            margin-bottom: 6px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.6);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 101, 101, 0.6);
        }

        .actions {
            text-align: center;
            margin-top: 8px;
        }

        .action-row {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-row .btn {
            flex: 1;
            min-width: 100px;
            margin-right: 0;
            margin-bottom: 0;
        }

        .footer {
            padding: 6px 16px;
            text-align: center;
            font-size: 9px;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 18px 18px 0 0;
            margin-top: 4px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            flex-shrink: 0;
        }

        .loading {
            text-align: center;
            padding: 30px 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .spinner {
            display: inline-block;
            width: 28px;
            height: 28px;
            border: 3px solid rgba(102, 126, 234, 0.2);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 12px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading div {
            color: #4a5568;
            font-weight: 500;
            font-size: 12px;
        }

        .hidden {
            display: none;
        }

        /* Popup保持打开的样式 */
        #popup-main-container {
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            position: relative;  /* 为对话框提供定位上下文 */
        }

        /* 优化滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 增强popup稳定性 */
        .popup-container {
            position: relative;
            z-index: 9999;
        }

        /* 阻止选择和拖拽 */
        * {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
        }

        /* ===== 对话框样式 - 统一管理 ===== */
        .order-dialog-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(1px) !important;
            z-index: 2147483647 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        }

        .order-dialog {
            background: white;
            border-radius: 18px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 460px;
            width: 90%;
            max-height: 85vh;
            overflow: hidden;
            animation: dialogSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .dialog-header {
            padding: 6px 18px 4px; /* 进一步缩小标题高度 */
            border-bottom: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border-radius: 18px 18px 0 0;
            color: white;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .dialog-header h3 {
            margin: 0;
            font-size: 15px; /* 缩小标题字体 */
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.3px;
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            font-size: 18px; /* 缩小关闭按钮 */
            color: white;
            cursor: pointer;
            padding: 0;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .dialog-body {
            padding: 16px; /* 缩小内边距 */
            max-height: 65vh;
            overflow-y: auto;
        }

        /* 订单信息样式 - 紧凑版 */
        .order-info-section {
            margin-bottom: 12px; /* 缩小间距 */
        }

        .order-info-simple {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px; /* 缩小圆角 */
            padding: 14px; /* 缩小内边距 */
            margin-bottom: 12px; /* 缩小间距 */
            line-height: 1.4;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        }

        .order-info-item {
            margin-bottom: 4px; /* 缩小间距 */
            font-size: 14px; /* 适中字体 */
            color: #303133;
            display: flex;
            align-items: flex-start;
            line-height: 1.3;
        }

        .order-info-item:last-child {
            margin-bottom: 0;
        }

        .order-info-label {
            color: #4a5568;
            font-weight: 600;
            min-width: 70px; /* 缩小标签宽度 */
            font-size: 14px;
            flex-shrink: 0;
        }

        .order-info-value {
            color: #303133;
            font-weight: 500;
            font-size: 14px;
        }

        .order-info-customer {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .customer-name {
            color: #303133;
            font-weight: 600;
            font-size: 14px;
        }

        .customer-plate {
            background: #f0f9ff;
            color: #1890ff;
            padding: 1px 6px; /* 缩小内边距 */
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
        }

        .destination-address {
            color: #606266;
            font-size: 13px;
            line-height: 1.3;
            word-break: break-all;
        }

        /* 修改目的地区域样式 - 紧凑版 */
        .modify-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 12px; /* 缩小内边距 */
            margin-bottom: 12px;
        }

        .form-group {
            margin-bottom: 12px; /* 缩小间距 */
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .form-group label {
            display: block;
            font-size: 13px; /* 缩小字体 */
            font-weight: 600;
            color: #303133;
            margin-bottom: 6px; /* 缩小间距 */
            position: relative;
            padding-left: 22px;
        }

        .form-group label::before {
            position: absolute;
            left: 0;
            top: 0;
            font-size: 14px;
        }

        .form-group label[data-icon="location"]::before {
            content: "📍";
        }

        .form-group label[data-icon="money"]::before {
            content: "💰";
        }

        /* 地址建议开关样式 - 使用小灯泡图标 */
        .address-suggestion-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 6px 8px;
            background: #e8f4fd;
            border-radius: 6px;
            border: 1px solid #b3d8ff;
        }

        .toggle-label {
            font-size: 12px;
            color: #409eff;
            font-weight: 500;
        }

        .toggle-switch {
            position: relative;
            width: 32px;
            height: 24px;
            background: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #606266;
        }

        .toggle-switch.active {
            background: #17a2b8;
            border-color: #17a2b8;
            color: white;
        }

        .toggle-switch i {
            font-size: 12px;
            transition: all 0.3s;
        }

        .address-input-container {
            position: relative;
        }

        .address-input-container input {
            width: 100%;
            padding: 10px; /* 缩小内边距 */
            border: 2px solid #e4e7ed;
            border-radius: 6px; /* 缩小圆角 */
            font-size: 13px; /* 缩小字体 */
            transition: border-color 0.2s;
            box-sizing: border-box;
        }

        .address-input-container input:focus {
            outline: none;
            border-color: #ff6600;
            box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.1);
        }

        .address-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e4e7ed;
            border-top: none;
            border-radius: 0 0 6px 6px;
            max-height: 150px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .address-suggestions.hidden {
            display: none;
        }

        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f5f7fa;
            transition: background 0.2s;
        }

        .suggestion-item:hover {
            background: #f5f7fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-name {
            font-size: 13px;
            color: #303133;
            font-weight: 500;
        }

        .suggestion-address {
            font-size: 11px;
            color: #909399;
            margin-top: 2px;
        }

        .estimate-display {
            background: white;
            border: 2px solid #e4e7ed;
            border-radius: 6px;
            padding: 10px; /* 缩小内边距 */
            font-size: 13px; /* 缩小字体 */
            color: #606266;
            min-height: 16px; /* 缩小最小高度 */
            line-height: 1.3;
        }

        /* 对话框底部样式 */
        .dialog-footer {
            padding: 12px 16px; /* 缩小内边距 */
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 10px; /* 缩小间距 */
            background: #fafafa;
            border-radius: 0 0 18px 18px;
        }

        .btn-cancel, .btn-confirm {
            padding: 8px 16px; /* 缩小内边距 */
            border: none;
            border-radius: 5px; /* 缩小圆角 */
            font-size: 13px; /* 缩小字体 */
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 70px; /* 缩小最小宽度 */
        }

        .btn-cancel {
            background: #f5f7fa;
            color: #606266;
            border: 1px solid #dcdfe6;
        }

        .btn-cancel:hover {
            background: #ecf5ff;
            color: #409eff;
            border-color: #b3d8ff;
        }

        .btn-confirm {
            background: #ff6600;
            color: white;
        }

        .btn-confirm:hover:not(:disabled) {
            background: #e55a00;
        }

        .btn-confirm:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .btn-danger {
            background: #f56c6c;
            color: white;
        }

        .btn-danger:hover {
            background: #f78989;
        }

        /* 取消订单特殊样式 */
        .cancel-section {
            margin-bottom: 12px;
        }

        .warning-message {
            padding: 10px;
            background: #fef0f0;
            border: 1px solid #fbc4c4;
            border-radius: 6px;
            margin-bottom: 12px;
        }

        .fee-section {
            margin-bottom: 12px;
        }

        .fee-info {
            padding: 8px 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            font-size: 12px;
            color: #606266;
        }

        /* 动画效果 */
        @keyframes dialogSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* 响应式优化 */
        @media (max-width: 480px) {
            .order-dialog {
                width: 95%;
                margin: 10px;
            }

            .dialog-header,
            .dialog-body,
            .dialog-footer {
                padding: 12px;
            }

            .dialog-footer {
                flex-direction: column;
            }

            .btn-cancel,
            .btn-confirm {
                width: 100%;
            }
        }

        /* 订单卡片样式 */
        .order-card {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .order-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 107, 53, 0.3);
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .order-id {
            font-weight: 500;
            font-size: 14px;
            color: #303133;
        }

        .order-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        /* e代驾官方状态码样式 */
        .status-102 { background: #fdf6ec; color: #e6a23c; } /* 开始系统派单 */
        .status-180 { background: #fdf6ec; color: #e6a23c; } /* 系统派单中 */
        .status-301 { background: #f0f9ff; color: #409eff; } /* 司机接单 */
        .status-302 { background: #e6f7ff; color: #1890ff; } /* 司机就位 */
        .status-303 { background: #f6ffed; color: #52c41a; } /* 司机开车 */
        .status-304 { background: #f6ffed; color: #52c41a; } /* 代驾结束 */
        .status-403 { background: #fff2f0; color: #ff4d4f; } /* 客户取消 */
        .status-404 { background: #fff2f0; color: #ff4d4f; } /* 司机取消 */
        .status-501 { background: #f6ffed; color: #52c41a; } /* 司机报单 */
        .status-506 { background: #fff2f0; color: #ff4d4f; } /* 系统派单失败 */

        /* 内部状态样式（兼容性） */
        .status-pending { background: #fdf6ec; color: #e6a23c; } /* 等待派单 */
        .status-accepted { background: #f0f9ff; color: #409eff; } /* 司机接单 */
        .status-in_progress { background: #f6ffed; color: #52c41a; } /* 服务中 */
        .status-completed { background: #f6ffed; color: #52c41a; } /* 已完成 */
        .status-cancelled { background: #fff2f0; color: #ff4d4f; } /* 已取消 */
        .status-failed { background: #fff2f0; color: #ff4d4f; } /* 派单失败 */

        .order-info {
            font-size: 12px;
            color: #606266;
            line-height: 1.4;
        }

        .order-route {
            display: flex;
            align-items: center;
            margin: 6px 0;
            font-size: 12px;
        }

        .route-icon {
            color: #909399;
            margin: 0 6px;
        }

        .order-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #f5f7fa;
            font-size: 11px;
            color: #909399;
        }

        .order-time {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .order-price {
            font-weight: 500;
            color: #ff6600;
        }

        .monitoring-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            background: #67c23a;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 标签导航样式 */
        .tab-navigation {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 18px;
            padding: 5px;
            margin-bottom: 10px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .tab-item {
            flex: 1;
            padding: 10px 14px;
            text-align: center;
            cursor: pointer;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 13px;
            font-weight: 600;
            color: #718096;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 3px;
            position: relative;
            overflow: hidden;
        }

        .tab-item.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white !important;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
            transform: translateY(-2px);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .tab-item.active .tab-text,
        .tab-item.active .tab-icon {
            color: white !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .tab-item:not(.active):hover {
            background: rgba(255, 255, 255, 0.8);
            color: #4a5568;
            transform: translateY(-1px);
        }

        .tab-icon {
            font-size: 14px;
            margin-bottom: 1px;
        }

        .tab-text {
            font-size: 11px;
            font-weight: 600;
        }

        /* 标签内容样式 */
        .tab-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .tab-content.hidden {
            display: none;
        }

        /* 对话框样式 */
        .order-dialog-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            z-index: 2147483647 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 !important;
            padding: 20px !important;
            box-sizing: border-box !important;
            animation: fadeIn 0.3s ease-out !important;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .order-dialog {
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 20px !important;
            padding: 0 !important;
            max-width: 500px !important;
            width: 90% !important;
            max-height: 80vh !important;
            overflow: hidden !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
            backdrop-filter: blur(20px) !important;
            -webkit-backdrop-filter: blur(20px) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            animation: slideIn 0.3s ease-out !important;
            display: flex !important;
            flex-direction: column !important;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px) scale(0.9);
                opacity: 0;
            }
            to {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .dialog-header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
            color: white !important;
            padding: 20px 24px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        .dialog-header h3 {
            margin: 0 !important;
            font-size: 18px !important;
            font-weight: 700 !important;
        }

        .close-btn {
            background: none !important;
            border: none !important;
            color: white !important;
            font-size: 24px !important;
            cursor: pointer !important;
            padding: 0 !important;
            width: 30px !important;
            height: 30px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.2s !important;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2) !important;
        }

        .dialog-body {
            padding: 24px !important;
            flex: 1 !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
        }

        .dialog-footer {
            padding: 20px 24px !important;
            display: flex !important;
            gap: 12px !important;
            justify-content: flex-end !important;
            background: rgba(0, 0, 0, 0.02) !important;
            border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
            flex-shrink: 0 !important;
        }

        .dialog-footer .btn {
            margin: 0 !important;
            min-width: 100px !important;
        }

        /* 监控控制栏样式 */
        .monitor-control-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 14px;
            padding: 14px 16px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .monitor-info {
            flex: 1;
        }

        .monitor-title {
            font-weight: 700;
            font-size: 15px;
            color: #2d3748;
            display: flex;
            align-items: center;
            margin-bottom: 3px;
        }

        .monitor-subtitle {
            font-size: 12px;
            color: #718096;
            font-weight: 500;
        }

        .monitor-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        /* 自定义开关样式 */
        .auto-monitor-switch {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .auto-monitor-switch input[type="checkbox"] {
            display: none;
        }

        .auto-monitor-switch label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            color: #4a5568;
        }

        .switch-slider {
            width: 44px;
            height: 24px;
            background: #cbd5e0;
            border-radius: 12px;
            position: relative;
            transition: all 0.3s ease;
        }

        .switch-slider::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .auto-monitor-switch input:checked + label .switch-slider {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }

        .auto-monitor-switch input:checked + label .switch-slider::before {
            transform: translateX(20px);
        }

        .refresh-btn {
            padding: 8px 12px !important;
            font-size: 16px !important;
            border-radius: 12px !important;
            min-width: auto !important;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .empty-title {
            font-size: 16px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 6px;
        }

        .empty-subtitle {
            font-size: 14px;
            color: #718096;
            font-weight: 500;
        }

        /* 地址建议样式 */
        .address-input-container {
            position: relative;
        }

        .address-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
        }

        .suggestion-item {
            padding: 12px;
            border-bottom: 1px solid #f5f7fa;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-item:hover {
            background-color: #f5f7fa;
        }

        .suggestion-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
        }

        .suggestion-address {
            font-size: 12px;
            color: #909399;
        }

        .estimate-display {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            font-size: 13px;
            line-height: 1.4;
        }

        .btn-confirm:disabled {
            background: #c0c4cc !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚗 F6 e代驾助手</h1>
    </div>

    <div class="content popup-container" id="popup-main-container">
        <!-- 标签导航 -->
        <div class="tab-navigation">
            <div class="tab-item active" data-tab="dashboard">
                <span class="tab-icon">📊</span>
                <span class="tab-text">仪表板</span>
            </div>
            <div class="tab-item" data-tab="orders">
                <span class="tab-icon">📋</span>
                <span class="tab-text">订单监控</span>
            </div>
        </div>

        <!-- 仪表板标签 -->
        <div class="tab-content active" id="dashboard">
            <!-- 状态显示 -->
            <div class="status-card">
                <div class="status-item">
                    <span class="status-label">
                        <span style="font-size: 16px;">🔌</span>
                        插件状态
                    </span>
                    <span id="plugin-status" class="status-value">检查中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">
                        <span style="font-size: 16px;">🌐</span>
                        服务连接
                    </span>
                    <span id="api-status" class="status-value">检查中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">
                        <span style="font-size: 16px;">📄</span>
                        当前页面
                    </span>
                    <span id="page-status" class="status-value">检查中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">
                        <span style="font-size: 16px;">📊</span>
                        订单监控
                    </span>
                    <span id="active-polling" class="status-value">0个</span>
                </div>
                <div class="status-item">
                    <span class="status-label">
                        <span style="font-size: 16px;">📋</span>
                        历史订单
                    </span>
                    <span id="cached-orders" class="status-value">0个</span>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="config-section">
                <div class="config-title">🚀 快捷操作</div>
                <div class="actions">
                    <div class="action-row">
                        <button id="refresh-status" class="btn btn-success">🔄 刷新状态</button>
                        <button id="reload-plugin" class="btn btn-primary">🔄 重载插件</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单监控标签 -->
        <div class="tab-content hidden" id="orders">
            <!-- 监控控制栏 -->
            <div class="monitor-control-bar">
                <div class="monitor-info">
                    <div class="monitor-title">
                        <span style="font-size: 16px; margin-right: 8px;">📊</span>
                        活跃订单监控
                    </div>
                    <div class="monitor-subtitle">实时跟踪订单状态变化</div>
                </div>
                <div class="monitor-controls">
                    <div class="auto-monitor-switch">
                        <input type="checkbox" id="auto-monitor" checked>
                        <label for="auto-monitor">
                            <span class="switch-slider"></span>
                            <span class="switch-text">自动监控</span>
                        </label>
                    </div>
                    <button id="refresh-orders" class="btn btn-primary refresh-btn">
                        🔄
                    </button>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="orders-loading">
                <div class="spinner"></div>
                <div style="margin-top: 8px; font-size: 12px;">正在加载活跃订单...</div>
            </div>

            <!-- 活跃订单列表 -->
            <div id="active-orders-list" class="hidden" style="max-height: 320px; overflow-y: auto; padding-right: 4px; flex: 1;">
                <!-- 活跃订单将在这里动态生成 -->
            </div>

            <!-- 空状态 -->
            <div id="orders-empty" class="hidden empty-state">
                <div class="empty-icon">🎯</div>
                <div class="empty-title">暂无活跃订单</div>
                <div class="empty-subtitle">所有订单都已完成或取消</div>
            </div>
        </div>

    </div>

    <div class="footer">
        <div>F6 e代驾助手 v1.0.0</div>
        <div>© 2025 MyProj2025</div>
    </div>

    <script src="js/popup.js"></script>
</body>
</html>
