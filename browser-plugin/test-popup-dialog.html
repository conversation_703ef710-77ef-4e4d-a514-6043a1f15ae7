<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Popup对话框测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #ff6600;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.2s;
        }
        
        .test-button:hover {
            background: #e55a00;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .comparison-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .comparison-item h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .comparison-item ul {
            margin: 0;
            padding-left: 20px;
        }

        .comparison-item li {
            margin-bottom: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Popup对话框样式测试</h1>
        <p>这个页面用于测试popup.js中注入的CSS样式是否正确应用。</p>
        
        <div class="comparison">
            <div class="comparison-item">
                <h4>🔴 修改前的问题</h4>
                <ul>
                    <li>标题高度过大 (padding: 12px 18px 10px)</li>
                    <li>字体偏小 (13px)</li>
                    <li>客户信息分行显示</li>
                    <li>缺少图标标签</li>
                    <li>整体显得头重脚轻</li>
                </ul>
            </div>
            <div class="comparison-item">
                <h4>✅ 修改后的效果</h4>
                <ul>
                    <li>标题高度缩小 (padding: 8px 18px 6px)</li>
                    <li>字体增大 (15px)</li>
                    <li>客户信息同行显示</li>
                    <li>添加📍💰图标</li>
                    <li>布局更加平衡</li>
                </ul>
            </div>
        </div>
        
        <h2>测试按钮</h2>
        <button class="test-button" onclick="testModifyDialog()">测试修改订单对话框</button>
        <button class="test-button" onclick="testCancelDialog()">测试取消订单对话框</button>
    </div>

    <!-- 引入popup.js的相关代码 -->
    <script>
        // 模拟PopupManager类的部分功能
        class TestPopupManager {
            injectDialogStyles() {
                if (document.getElementById('dialog-styles-injected')) {
                    return;
                }

                const style = document.createElement('style');
                style.id = 'dialog-styles-injected';
                style.textContent = `
                    /* 对话框样式 - 优化版本 */
                    .order-dialog-overlay {
                        position: fixed !important;
                        top: 0 !important;
                        left: 0 !important;
                        right: 0 !important;
                        bottom: 0 !important;
                        width: 100% !important;
                        height: 100% !important;
                        background-color: rgba(255, 255, 255, 0.1) !important;
                        backdrop-filter: blur(1px) !important;
                        z-index: 2147483647 !important;
                        display: flex !important;
                        justify-content: center !important;
                        align-items: center !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        box-sizing: border-box !important;
                    }

                    .order-dialog {
                        background: white;
                        border-radius: 18px;
                        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                        max-width: 480px;
                        width: 90%;
                        max-height: 80vh;
                        overflow: hidden;
                        animation: dialogSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                        position: relative;
                    }

                    .dialog-header {
                        padding: 8px 18px 6px; /* 缩小标题高度 */
                        border-bottom: none;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
                        border-radius: 18px 18px 0 0;
                        color: white;
                        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
                    }

                    .dialog-header h3 {
                        margin: 0;
                        font-size: 16px;
                        font-weight: 700;
                        color: white;
                        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                        letter-spacing: 0.3px;
                    }

                    .close-btn {
                        background: rgba(255, 255, 255, 0.2);
                        border: none;
                        font-size: 20px;
                        color: white;
                        cursor: pointer;
                        padding: 0;
                        width: 28px;
                        height: 28px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: background 0.2s;
                    }

                    .close-btn:hover {
                        background: rgba(255, 255, 255, 0.3);
                    }

                    .dialog-body {
                        padding: 20px;
                        max-height: 60vh;
                        overflow-y: auto;
                    }

                    .order-info-section {
                        margin-bottom: 16px;
                    }

                    .order-info-simple {
                        background: rgba(255, 255, 255, 0.9);
                        border-radius: 16px;
                        padding: 18px;
                        margin-bottom: 16px;
                        line-height: 1.6;
                        backdrop-filter: blur(10px);
                        -webkit-backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
                    }

                    .order-info-item {
                        margin-bottom: 6px;
                        font-size: 15px; /* 增大字体 */
                        color: #303133;
                        display: flex;
                        align-items: flex-start;
                        line-height: 1.4;
                    }

                    .order-info-item:last-child {
                        margin-bottom: 0;
                    }

                    .order-info-label {
                        color: #4a5568;
                        font-weight: 600;
                        min-width: 80px;
                        font-size: 15px; /* 增大字体 */
                        flex-shrink: 0;
                    }

                    .order-info-value {
                        color: #303133;
                        font-weight: 500;
                        font-size: 15px; /* 增大字体 */
                    }

                    .order-info-customer {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        flex-wrap: wrap;
                    }

                    .customer-name {
                        color: #303133;
                        font-weight: 600;
                        font-size: 15px; /* 增大字体 */
                    }

                    .customer-plate {
                        background: #f0f9ff;
                        color: #1890ff;
                        padding: 2px 8px;
                        border-radius: 4px;
                        font-size: 13px;
                        font-weight: 500;
                    }

                    .destination-address {
                        color: #606266;
                        font-size: 14px;
                        line-height: 1.4;
                        word-break: break-all;
                    }

                    .modify-section {
                        background: #f8f9fa;
                        border-radius: 12px;
                        padding: 16px;
                        margin-bottom: 16px;
                    }

                    .form-group {
                        margin-bottom: 16px;
                    }

                    .form-group:last-child {
                        margin-bottom: 0;
                    }

                    .form-group label {
                        display: block;
                        font-size: 14px;
                        font-weight: 600;
                        color: #303133;
                        margin-bottom: 8px;
                        position: relative;
                        padding-left: 24px;
                    }

                    .form-group label::before {
                        position: absolute;
                        left: 0;
                        top: 0;
                        font-size: 16px;
                    }

                    .form-group label[data-icon="location"]::before {
                        content: "📍";
                    }

                    .form-group label[data-icon="money"]::before {
                        content: "💰";
                    }

                    .address-input-container input {
                        width: 100%;
                        padding: 12px;
                        border: 2px solid #e4e7ed;
                        border-radius: 8px;
                        font-size: 14px;
                        transition: border-color 0.2s;
                        box-sizing: border-box;
                    }

                    .estimate-display {
                        background: white;
                        border: 2px solid #e4e7ed;
                        border-radius: 8px;
                        padding: 12px;
                        font-size: 14px;
                        color: #606266;
                        min-height: 20px;
                    }

                    .dialog-footer {
                        padding: 16px 20px;
                        border-top: 1px solid #f0f0f0;
                        display: flex;
                        justify-content: flex-end;
                        gap: 12px;
                        background: #fafafa;
                        border-radius: 0 0 18px 18px;
                    }

                    .btn-cancel, .btn-confirm {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 6px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s;
                        min-width: 80px;
                    }

                    .btn-cancel {
                        background: #f5f7fa;
                        color: #606266;
                        border: 1px solid #dcdfe6;
                    }

                    .btn-confirm {
                        background: #ff6600;
                        color: white;
                    }

                    .btn-danger {
                        background: #f56c6c;
                        color: white;
                    }

                    @keyframes dialogSlideIn {
                        from {
                            opacity: 0;
                            transform: translateY(-20px) scale(0.95);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0) scale(1);
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            closeDialog() {
                const dialog = document.querySelector('.order-dialog-overlay');
                if (dialog) {
                    dialog.remove();
                }
            }
        }

        const testManager = new TestPopupManager();

        function testModifyDialog() {
            testManager.injectDialogStyles();

            const dialog = document.createElement('div');
            dialog.className = 'order-dialog-overlay';

            dialog.innerHTML = `
                <div class="order-dialog">
                    <div class="dialog-header">
                        <h3>修改目的地</h3>
                        <button class="close-btn" onclick="testManager.closeDialog()">×</button>
                    </div>
                    <div class="dialog-body">
                        <div class="order-info-section">
                            <div class="order-info-simple">
                                <div class="order-info-item">
                                    <span class="order-info-label">订单号：</span>
                                    <span class="order-info-value">TEST_ORDER_123456</span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">客户：</span>
                                    <div class="order-info-customer">
                                        <span class="customer-name">张先生</span>
                                        <span class="customer-plate">粤B12345</span>
                                    </div>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">当前目的地：</span>
                                    <div class="destination-address">深圳市南山区科技园南区深南大道10000号</div>
                                </div>
                            </div>
                        </div>

                        <div class="modify-section">
                            <div class="form-group">
                                <label data-icon="location">新目的地地址</label>
                                <div class="address-input-container">
                                    <input type="text" placeholder="请输入新的目的地地址" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label data-icon="money">预估费用</label>
                                <div class="estimate-display">输入地址后自动计算</div>
                            </div>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button class="btn-cancel" onclick="testManager.closeDialog()">取消</button>
                        <button class="btn-confirm">确认修改</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);
        }

        function testCancelDialog() {
            testManager.injectDialogStyles();

            const dialog = document.createElement('div');
            dialog.className = 'order-dialog-overlay';

            dialog.innerHTML = `
                <div class="order-dialog">
                    <div class="dialog-header">
                        <h3>取消订单</h3>
                        <button class="close-btn" onclick="testManager.closeDialog()">×</button>
                    </div>
                    <div class="dialog-body">
                        <div class="order-info-section">
                            <div class="order-info-simple">
                                <div class="order-info-item">
                                    <span class="order-info-label">订单号：</span>
                                    <span class="order-info-value">TEST_ORDER_123456</span>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">客户：</span>
                                    <div class="order-info-customer">
                                        <span class="customer-name">张先生</span>
                                        <span class="customer-plate">粤B12345</span>
                                    </div>
                                </div>
                                <div class="order-info-item">
                                    <span class="order-info-label">目的地：</span>
                                    <div class="destination-address">深圳市南山区科技园南区深南大道10000号</div>
                                </div>
                            </div>
                        </div>

                        <div style="background: #fef0f0; border: 1px solid #fbc4c4; border-radius: 6px; padding: 12px;">
                            <p style="margin: 0 0 8px 0; color: #f56c6c; font-weight: 500;">确定要取消这个订单吗？</p>
                            <p style="margin: 0; color: #909399; font-size: 12px;">取消后订单将无法恢复，可能产生相应费用</p>
                            <p style="margin: 8px 0 0 0; color: #67c23a; font-size: 12px; font-weight: 500;">💡 当前订单处于等待派单状态，通常可以免费取消</p>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button class="btn-cancel" onclick="testManager.closeDialog()">不取消</button>
                        <button class="btn-confirm btn-danger">确认取消</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);
        }

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                testManager.closeDialog();
            }
        });
    </script>
</body>
</html>
