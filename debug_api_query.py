#!/usr/bin/env python3
"""
调试API查询逻辑
"""

try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))

    from app import create_app, db
    from app.models import Order

    def debug_api_query():
        """调试API查询逻辑"""
        app = create_app()

        with app.app_context():
            print("🔧 调试API查询逻辑")
            print("=" * 50)

            store_name = "龙华店"
            EDJ_TERMINATED_STATUSES = ['304', '403', '404', '506']

            print(f"查询店面: {store_name}")
            print(f"终止状态码: {EDJ_TERMINATED_STATUSES}")

            # 1. 查询所有龙华店订单
            all_orders = Order.query.filter(Order.store_name == store_name).all()
            print(f"\n📋 龙华店所有订单 ({len(all_orders)}个):")
            for order in all_orders:
                print(f"  {order.edaijia_order_id}: {order.edj_status_code} - {order.customer_name}")

            # 2. 完整的API查询
            api_orders = Order.query.filter(
                Order.edj_status_code.isnot(None),
                ~Order.edj_status_code.in_(EDJ_TERMINATED_STATUSES)
            ).filter(Order.store_name == store_name).order_by(Order.created_at.desc()).all()

            print(f"\n✅ 完整API查询结果 ({len(api_orders)}个):")
            for order in api_orders:
                print(f"  {order.edaijia_order_id}: {order.edj_status_code} - {order.customer_name}")

    if __name__ == '__main__':
        debug_api_query()

except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
