#!/usr/bin/env python3
"""
清理重复订单数据脚本

功能：
1. 识别并删除重复的订单数据
2. 保留最新的订单记录
3. 生成清理报告
4. 备份被删除的数据

使用方法：
python clean_duplicate_orders.py [--dry-run] [--backup]

参数：
--dry-run: 只显示将要删除的数据，不实际删除
--backup: 在删除前备份数据到文件
"""

import sys
import os
import json
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Order
from sqlalchemy import func, and_

def find_duplicate_orders():
    """
    查找重复的订单数据
    
    重复标准：
    1. 相同的客户手机号 + 车牌号 + 店面名称
    2. 相同的路线（起点 + 终点）
    3. 创建时间在24小时内
    
    Returns:
        list: 重复订单组列表
    """
    print("🔍 正在查找重复订单...")
    
    # 查找基于客户信息的重复订单
    # 使用SQLite兼容的语法
    customer_duplicates = db.session.query(
        Order.customer_phone,
        Order.plate_number,
        Order.store_name,
        func.count(Order.id).label('count')
    ).filter(
        and_(
            Order.customer_phone.isnot(None),
            Order.customer_phone != '',
            Order.plate_number.isnot(None),
            Order.plate_number != ''
        )
    ).group_by(
        Order.customer_phone,
        Order.plate_number,
        Order.store_name
    ).having(func.count(Order.id) > 1).all()
    
    duplicate_groups = []
    
    for dup in customer_duplicates:
        # 重新查询该组的所有订单
        orders = Order.query.filter(
            and_(
                Order.customer_phone == dup.customer_phone,
                Order.plate_number == dup.plate_number,
                Order.store_name == dup.store_name
            )
        ).order_by(Order.created_at.desc()).all()

        if len(orders) > 1:
            duplicate_groups.append({
                'type': 'customer_duplicate',
                'key': f"{dup.customer_phone}|{dup.plate_number}|{dup.store_name}",
                'count': dup.count,
                'orders': orders,
                'keep_order': orders[0],  # 保留最新的
                'delete_orders': orders[1:]  # 删除其他的
            })
    
    print(f"📊 发现 {len(duplicate_groups)} 组重复订单")
    return duplicate_groups

def print_duplicate_report(duplicate_groups):
    """打印重复订单报告"""
    print("\n" + "="*80)
    print("📋 重复订单详细报告")
    print("="*80)
    
    total_duplicates = 0
    total_to_delete = 0
    
    for i, group in enumerate(duplicate_groups, 1):
        print(f"\n🔸 重复组 {i}: {group['key']}")
        print(f"   类型: {group['type']}")
        print(f"   总数: {group['count']} 个订单")
        
        print(f"\n   ✅ 保留订单:")
        keep_order = group['keep_order']
        print(f"      ID: {keep_order.edaijia_order_id}")
        print(f"      创建时间: {keep_order.created_at}")
        print(f"      状态: {keep_order.status}")
        print(f"      路线: {keep_order.pickup_address} → {keep_order.destination_address}")
        
        print(f"\n   ❌ 将删除订单:")
        for order in group['delete_orders']:
            print(f"      ID: {order.edaijia_order_id}")
            print(f"      创建时间: {order.created_at}")
            print(f"      状态: {order.status}")
            print(f"      路线: {order.pickup_address} → {order.destination_address}")
            total_to_delete += 1
        
        total_duplicates += group['count']
    
    print(f"\n" + "="*80)
    print(f"📊 统计汇总:")
    print(f"   重复订单组数: {len(duplicate_groups)}")
    print(f"   涉及订单总数: {total_duplicates}")
    print(f"   将删除订单数: {total_to_delete}")
    print(f"   将保留订单数: {len(duplicate_groups)}")
    print("="*80)

def backup_orders(orders, backup_file):
    """备份订单数据到文件"""
    print(f"💾 正在备份数据到 {backup_file}...")
    
    backup_data = []
    for order in orders:
        order_data = {
            'id': order.id,
            'edaijia_order_id': order.edaijia_order_id,
            'store_name': order.store_name,
            'customer_name': order.customer_name,
            'customer_phone': order.customer_phone,
            'plate_number': order.plate_number,
            'car_model': order.car_model,
            'manager_name': order.manager_name,
            'status': order.status,
            'pickup_address': order.pickup_address,
            'destination_address': order.destination_address,
            'created_at': order.created_at.isoformat() if order.created_at else None,
            'updated_at': order.updated_at.isoformat() if order.updated_at else None,
            'price': order.price,
            'driver_name': order.driver_name,
            'driver_phone': order.driver_phone,
            'remarks': order.remarks
        }
        backup_data.append(order_data)
    
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump({
            'backup_time': datetime.now().isoformat(),
            'total_orders': len(backup_data),
            'orders': backup_data
        }, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 已备份 {len(backup_data)} 个订单到 {backup_file}")

def delete_duplicate_orders(duplicate_groups, dry_run=True, backup=False):
    """删除重复订单"""
    all_delete_orders = []
    
    # 收集所有要删除的订单
    for group in duplicate_groups:
        all_delete_orders.extend(group['delete_orders'])
    
    if not all_delete_orders:
        print("✅ 没有需要删除的重复订单")
        return
    
    if backup:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"deleted_orders_backup_{timestamp}.json"
        backup_orders(all_delete_orders, backup_file)
    
    if dry_run:
        print(f"\n🔍 DRY RUN 模式 - 将要删除 {len(all_delete_orders)} 个重复订单")
        print("   (实际不会删除，如需真正删除请去掉 --dry-run 参数)")
        return
    
    print(f"\n🗑️  正在删除 {len(all_delete_orders)} 个重复订单...")
    
    try:
        deleted_count = 0
        for order in all_delete_orders:
            print(f"   删除订单: {order.edaijia_order_id}")
            db.session.delete(order)
            deleted_count += 1
        
        db.session.commit()
        print(f"✅ 成功删除 {deleted_count} 个重复订单")
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ 删除失败: {str(e)}")
        raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='清理重复订单数据')
    parser.add_argument('--dry-run', action='store_true', 
                       help='只显示将要删除的数据，不实际删除')
    parser.add_argument('--backup', action='store_true',
                       help='在删除前备份数据到文件')
    
    args = parser.parse_args()
    
    print("🚀 开始清理重复订单数据...")
    print(f"   模式: {'DRY RUN (预览)' if args.dry_run else '实际删除'}")
    print(f"   备份: {'是' if args.backup else '否'}")
    
    # 创建应用上下文
    app = create_app()
    with app.app_context():
        try:
            # 查找重复订单
            duplicate_groups = find_duplicate_orders()
            
            if not duplicate_groups:
                print("✅ 没有发现重复订单，数据库已经是干净的！")
                return
            
            # 打印报告
            print_duplicate_report(duplicate_groups)
            
            # 确认操作
            if not args.dry_run:
                print(f"\n⚠️  警告: 即将删除 {sum(len(g['delete_orders']) for g in duplicate_groups)} 个重复订单")
                confirm = input("确认继续吗？(输入 'yes' 确认): ")
                if confirm.lower() != 'yes':
                    print("❌ 操作已取消")
                    return
            
            # 执行删除
            delete_duplicate_orders(duplicate_groups, args.dry_run, args.backup)
            
            print("\n🎉 重复订单清理完成！")
            
        except Exception as e:
            print(f"❌ 清理过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

if __name__ == '__main__':
    main()
